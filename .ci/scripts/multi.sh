#!/usr/bin/env bash
UID=$(id -u)
GID=$(id -g)
USERNAME=$(whoami)
dockerImage="intappsconreg.azurecr.io/hci-koyal/hcd-flutter-linux:1.0.63" 
# dockerImage="hci:1.0.4" 

testSuccess=true

testsDataOriginal=$(jq -n '[
  {
    "name": "in_integration_test",
    "displayName": "IN:Integration tests",
    "flavor": "fakein",
    "flutterDriverFile": "test_driver/integration_test.dart",
    "flutterDriveTargets": [
      {
        "testName": "part_1",
        "testFile": "test_driver/integration_tests_fake_in_part1.dart"
      },
      {
        "testName": "part_2",
        "testFile": "test_driver/integration_tests_fake_in_part2.dart"
      },
      {
        "testName": "part_3",
        "testFile": "test_driver/integration_tests_fake_in_part3.dart"
      },
      {
        "testName": "part_4",
        "testFile": "test_driver/integration_tests_fake_in_part4.dart"
      }
    ]
  },
  {
    "name": "vn_integration_test",
    "displayName": "VN:Integration tests",
    "flavor": "fakevn",
    "flutterDriverFile": "test_driver/integration_test.dart",
    "flutterDriveTargets": [
      {
        "testName": "part_1",
        "testFile": "test_driver/integration_tests_fake_vn_part1.dart"
      },
      {
        "testName": "part_2",
        "testFile": "test_driver/integration_tests_fake_vn_part2.dart"
      },
      {
        "testName": "part_3",
        "testFile": "test_driver/integration_tests_fake_vn_part3.dart"
      },
      {
        "testName": "part_4",
        "testFile": "test_driver/integration_tests_fake_vn_part4.dart"
      },
      {
        "testName": "part_5",
        "testFile": "test_driver/integration_tests_fake_vn_part5.dart"
      }
    ]
  },
  {
    "name": "in_integration_test",
    "displayName": "IN:Integration tests",
    "isLegacy": true,
    "flavor": "fakein",
    "flutterDriverFile": "test_driver/all_tests_fake_test.dart",
    "flutterDriveTargets": [
      {
        "testName": "part_1",
        "testFile": "test_driver/integration_fake_in.dart"
      }
    ]
  }
]')
testsData=$(jq -n '[
  {
    "name": "in_integration_test",
    "displayName": "IN:Integration tests",
    "flavor": "fakein",
    "flutterDriverFile": "test_driver/integration_test.dart",
    "flutterDriveTargets": [
      {
        "testName": "part_1",
        "testFile": "test_driver/integration_tests_fake_in_part1.dart"
      },
      {
        "testName": "part_2",
        "testFile": "test_driver/integration_tests_fake_in_part2.dart"
      }
    ]
  },
 {
    "name": "in_integration_test",
    "displayName": "IN:Integration tests",
    "isLegacy": true,
    "flavor": "fakein",
    "flutterDriverFile": "test_driver/all_tests_fake_test.dart",
    "flutterDriveTargets": [
      {
        "testName": "part_1",
        "testFile": "test_driver/integration_fake_in.dart"
      }
    ]
  }
]')
sourceFolder=`pwd`
parsed=$(echo "${testsData}" | jq -c '[.[] | 
.name as $Name | 
.flavor as $Flavor | 
.flutterDriverFile as $DriveFile | 
(.isLegacy // false) as $IsLegacy | 
.flutterDriveTargets[] | {
    package: "self_care",
    folder: "capp",
    name: $Name,
    flavor: $Flavor,
    driverFile: $DriveFile,
    testFile: .testFile,
    isLegacy: $IsLegacy,
    container: [ $Name,.testName, $IsLegacy|tostring ] | join("_")
}]')
# echo "${parsed}" | jq -c '.'
mkdir -p ../test/reports/
while read -r item; do
    package=$(echo "${item}" | jq -r '.package')
    appFolder=$(echo "${item}" | jq -r '.folder')
    container=$(echo "${item}" | jq -r '.container')
    name=$(echo "${item}" | jq -r '.name')
    flavor=$(echo "${item}" | jq -r '.flavor')
    isLegacy=$(echo "${item}" | jq -r '.isLegacy')
    driverFile=$(echo "${item}" | jq -r '.driverFile')
    testFile=$(echo "${item}" | jq -r '.testFile')
    #
    cd $sourceFolder
    destFolder="`pwd`/../test/${container}"
    rm -rf $destFolder
    
    cp -R `pwd` $destFolder
    cd $destFolder

   
    sh .ci/scripts/docker-wait.sh \
      $container \
      $destFolder \
      $dockerImage \
      $package \
      $appFolder \
      $driverFile \
      $testFile \
      $flavor \
      $isLegacy \
      4js5auihb7bmwf5lxtam6aory2q4jofwyxavceuyahpbjo6xbsiq \
      "true" &
done <<EOT
$(echo "${parsed}" | jq -c '.[]')
EOT

sleep 10

docker ps --format "{{.ID}} {{.Names}} {{.Status}}"
sleep 10
echo "----------------------------------------------"
echo "Results"
echo "----------------------------------------------"

containerReportsOutFolder="$(pwd)/reports"
mkdir -p $containerReportsOutFolder

echo ""
echo "Results"
echo ""

while read -r item; do
      containerName=$(echo "${item}" | jq -r '.container')
      echo ""
      echo "docker wait ${containerName}"
      exitCode=$(docker wait "${containerName}")
      echo "result: ${containerName}: $exitCode"

      containerId=$(docker ps -aqf "name=${containerName}")
      echo "result: id: ${containerId} name: ${containerName}: ${exitCode}"

      # echo "docker cp $containerId:/app/reports/ $containerReportsOutFolder"
      # copyDockerReports=$(docker cp $containerId:/app/reports/ $containerReportsOutFolder)
      # echo ""
      # echo "copyDockerReports: ${copyDockerReports}"
      # echo ""

      if [ "$exitCode" != "0" ]; then
        echo "!! test ${containerName} failed with result ${exitCode}."
        testSuccess=false
      else 
        echo "!! test ${containerName} all test passed."
      fi
done <<EOT
$(echo "${parsed}" | jq -c '.[]')
EOT
echo "Final Exit after all tests - testSuccess: $testSuccess"
if [ "$testSuccess" = false ]; then
  echo ""
  echo "Something wrong, check results for docker and tests"
  echo ""
  exit 400
else 
  echo ""
  echo "All test passed, docker exit correctly."
  echo ""
  exit 0
fi