{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Capp Debug Prod IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_prod.dart",
            "args": [
                "--flavor",
                "prodin"
            ],
            "preLaunchTask": "flavor:capp:prodin"
        },
        {
            "name": "Capp Debug Prod VN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_prod.dart",
            "args": [
                "--flavor",
                "prodvn"
            ],
            "preLaunchTask": "flavor:capp:prodvn"
        },        
        {
            "name": "Capp Debug Fake IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_fake.dart",
            "args": [
                "--flavor",
                "fakein"
            ],
            "preLaunchTask": "flavor:capp:fakein"
        },
        {
            "name": "Capp Debug Fake VN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_fake.dart",
            "args": [
                "--flavor",
                "fakevn"
            ],
            "preLaunchTask": "flavor:capp:fakevn"
        },
        {
            "name": "Capp Profile Fake IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_fake.dart",
            "args": [
                "--flavor",
                "fakein",
                "--profile"
            ],
            "preLaunchTask": "flavor:capp:fakein"
        },
        {
            "name": "Capp Debug Test Fake IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/test_driver/integration_fake_in.dart",
            "args": [
                "--flavor",
                "fakein"
            ]
        },
        {
            "name": "Capp Debug Test Fake VN",
            "request": "launch",
            "type": "dart",
            "program": "capp/test_driver/integration_fake_vn.dart",
            "args": [
                "--flavor",
                "fakevn"
            ]
        },
        {
            "name": "Capp Debug Int-Test Fake IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/test_driver/integration_tests_fake_in.dart",
            "args": [
                "--flavor",
                "fakein"
            ],
            "preLaunchTask": "flavor:capp:fakein"
        },
        {
            "name": "Capp Debug Test Prod IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/test_driver/integration_prod_in.dart",
            "args": [
                "--flavor",
                "prodin"
            ],
            "preLaunchTask": "flavor:capp:prodin"
        },
        {
            "name": "Capp Debug Test Prod VN",
            "request": "launch",
            "type": "dart",
            "program": "capp/test_driver/integration_prod_vn.dart",
            "args": [
                "--flavor",
                "prodvn"
            ],
            "preLaunchTask": "flavor:capp:prodvn"
        },
        {
            "name": "Capp Profile Test Fake IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/test_driver/integration_fake_in.dart",
            "args": [
                "--flavor",
                "fakein",
                "--profile"
            ],
            "preLaunchTask": "flavor:capp:fakein"
        },
        {
            "name": "Capp Profile Prod IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_prod.dart",
            "args": [
                "--flavor",
                "prodin",
                "--profile"
            ],
            "preLaunchTask": "flavor:capp:prodin"
        },
        {
            "name": "Capp Release Prod IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_prod.dart",
            "args": [
                "--flavor",
                "prodin",
                "--release",
                "--no-track-widget-creation"
            ],
            "preLaunchTask": "flavor:capp:prodin"
        },
        {
            "name": "Capp Release Prod VN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_prod.dart",
            "args": [
                "--flavor",
                "prodvn",
                "--release",
                "--no-track-widget-creation"
            ],
            "preLaunchTask": "flavor:capp:prodvn"
        },
        {
            "name": "Capp Release Fake IN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_fake.dart",
            "args": [
                "--flavor",
                "fakein",
                "--release",
                "--no-track-widget-creation"
            ],
            "preLaunchTask": "flavor:capp:fakein"
        },
        {
            "name": "Capp Release Fake VN",
            "request": "launch",
            "type": "dart",
            "program": "capp/lib/main_fake.dart",
            "args": [
                "--flavor",
                "fakevn",
                "--release",
                "--no-track-widget-creation"
            ],
            "preLaunchTask": "flavor:capp:fakevn"
        },
        {
            "name": "Storybook",
            "request": "launch",
            "type": "dart",
            "program": "storybook/lib/main.dart",
            "args": []
        },
        {
            "name": "Widgetbook",
            "request": "launch",
            "type": "dart",
            "program": "widgetbook/lib/main.dart",
            "args": []
        },
        {
            "name": "SSO VN",
            "request": "launch",
            "type": "dart",
            "program": "sso/lib/main_prod.dart",
            "args": [
                "--flavor",
                "prodvn"
            ]
        }
    ]
}