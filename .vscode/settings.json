{"dart.lineLength": 120, "[dart]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [120], "editor.codeActionsOnSave": {"source.fixAll": "never", "source.organizeImports": "never"}}, "dart.maxCompletionItems": 10000, "dart.maxLogLineLength": 1000, "dart.debugExtensionBackendProtocol": "sse", "dart.offline": false, "dart.devToolsLocation": "beside", "dart.doNotFormat": ["**/*.g.dart", "**/*.freezed.dart"], "dart.renameFilesWithClasses": "prompt", "explorer.autoReveal": "focusNoScroll", "editor.foldingImportsByDefault": false, "dart.updateImportsOnRename": true, "dart.notifyAnalyzerErrors": true, "dart.analysisExcludedFolders": ["build", "example", "l10n", ".fvm", "bin", "ci", "docs", "dynamic_forms_playground", "forks", "localizations_processor", "mapp", "plugins", "sso", "storybook", "tools", "widgetbook"], "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/.hg/store/**": true, "*.code-workspace": true}, "dart.onlyAnalyzeProjectsWithOpenFiles": true, "explorer.confirmDelete": false, "explorer.compactFolders": false, "dart.promptToGetPackages": false, "dart.analysisServerFolding": false, "dart.flutterOutline": true, "dart.flutterSdkPath": ".fvm/versions/stable"}