{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "command": "dotnet",
            "type": "process",
            "args": [
                "build",
                "${workspaceFolder}/localizations_processor/Hci.Hcd.LocalizationsProcessor/Hci.Hcd.LocalizationsProcessor.csproj",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "publish",
            "command": "dotnet",
            "type": "process",
            "args": [
                "publish",
                "${workspaceFolder}/localizations_processor/Hci.Hcd.LocalizationsProcessor/Hci.Hcd.LocalizationsProcessor.csproj",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "watch",
            "command": "dotnet",
            "type": "process",
            "args": [
                "watch",
                "run",
                "${workspaceFolder}/localizations_processor/Hci.Hcd.LocalizationsProcessor/Hci.Hcd.LocalizationsProcessor.csproj",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "type": "process",
            "command": "dart",
            "args": [
                "tools/koyal.dart",
                "g"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "label": "Koyal: flutter pub get"
        },
        {
            "type": "process",
            "command": "dart",
            "args": [
                "tools/koyal.dart",
                "a"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "label": "Koyal: flutter analyze"
        },
        {
            "type": "process",
            "command": "dart",
            "args": [
                "tools/koyal.dart",
                "c"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "label": "Koyal: clean & get"
        },
        {
            "type": "process",
            "command": "dart",
            "args": [
                "tools/koyal.dart",
                "tr"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "label": "Koyal: translation"
        },
        {
            "label": "flavor:capp:prodin",
            "type": "shell",
            "command": "gmat",
            "args": [
                "flavor",
                "self_care",
                "--change",
                "prodin"
            ],
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "clear": true,
                "showReuseMessage": false,
                "panel": "shared"
            },
            "promptOnClose": false,
            "isBackground": false
        },
        {
            "label": "flavor:capp:prodvn",
            "type": "shell",
            "command": "gmat",
            "args": [
                "flavor",
                "self_care",
                "--change",
                "prodvn"
            ],
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "clear": true,
                "showReuseMessage": false,
                "panel": "shared"
            },
            "promptOnClose": false,
            "isBackground": false
        },        
        {
            "label": "flavor:capp:fakein",
            "type": "shell",
            "command": "gmat",
            "args": [
                "flavor",
                "self_care",
                "--change",
                "fakein"
            ],
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "clear": true,
                "showReuseMessage": false,
                "panel": "shared"
            },
            "promptOnClose": false,
            "isBackground": false
        },
        {
            "label": "flavor:capp:fakevn",
            "type": "shell",
            "command": "gmat",
            "args": [
                "flavor",
                "self_care",
                "--change",
                "fakevn"
            ],
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "clear": true,
                "showReuseMessage": false,
                "panel": "shared"
            },
            "promptOnClose": false,
            "isBackground": false
        },
    ]
}