import java.util.regex.Matcher
import java.util.regex.Pattern

plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.firebase.crashlytics"
    id "com.google.gms.google-services"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('signing.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}


def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


android {
    compileSdkVersion 35
    ndkVersion "28.1.13356709"
    namespace "net.homecredit.selfcare"
    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }

    defaultConfig {
        applicationId "net.homecredit.selfcare"
        minSdkVersion 26
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        multiDexKeepFile file('multidex-config.txt')
        aaptOptions {
            ignoreAssetsPattern '!mlkit_pose:!mlkit_label_default_model:'
        }

        applicationVariants.all {
            if (buildType.name == "release" && applicationId == "net.homecredit.selfcare") {
                resValue("string", "clientId", "xntpfxsoea-25onyslvzsj5pouufnql8k0jin0_-x6i")
            } else if (buildType.name == "debug" && applicationId == "net.homecredit.selfcare") {
                resValue("string", "clientId", "13_11tigtifgvl-dlwcgm7_qtawb9e9s_lutkgqmqem")
            } else if (buildType.name == "profile" && applicationId == "net.homecredit.selfcare") {
                resValue("string", "clientId", "13_11tigtifgvl-dlwcgm7_qtawb9e9s_lutkgqmqem")
            } else if (buildType.name == "release" && applicationId == "net.homecredit.selfcare.fake") {
                resValue("string", "clientId", "0cr0tifiy1g0saxsrsckpo3twoupi8co3jccy1btcq0")
            } else if (buildType.name == "debug" && applicationId == "net.homecredit.selfcare.fake") {
                resValue("string", "clientId", "0cr0tifiy1g0saxsrsckpo3twoupi8co3jccy1btcq0")
            } else if (buildType.name == "profile" && applicationId == "net.homecredit.selfcare.fake") {
                resValue("string", "clientId", "0cr0tifiy1g0saxsrsckpo3twoupi8co3jccy1btcq0")
            } else {
                resValue("string", "clientId", "")
            }
        }
    }
    signingConfigs {
        release {
            storeFile keystoreProperties['release.keystore'] ? file(keystoreProperties['release.keystore']) : null
            keyAlias keystoreProperties['release.keystore.alias']
            keyPassword keystoreProperties['release.keystore.keyPassword']
            storePassword keystoreProperties['release.keystore.storePassword']

            v1SigningEnabled true
            v2SigningEnabled true
            enableV3Signing = true
            enableV4Signing = true
        }
    }
    buildTypes {
        release {
            minifyEnabled true
            signingConfig signingConfigs.debug
            //removing support for x86 Android devices, because Flutter Sdk does not support them.
            //Ref: https://docs.flutter.dev/deployment/android#what-are-the-supported-target-architectures
            //Solution: https://github.com/flutter/flutter/issues/32756#issuecomment-846705128
            ndk {
                abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64'
            }
            proguardFiles 'proguard-rules-vnidcard.pro'
        }
        profile {
            minifyEnabled true
            proguardFiles 'proguard-rules-vnidcard.pro'
        }
        debug {
            minifyEnabled true
            proguardFiles 'proguard-rules-vnidcard.pro'
        }
        // DO NOT DELETE - Comments are removed before App Center tests to reduce APK Size
        //-debug {
        //-    ndk {
        //-        abiFilters "arm64-v8a", "armeabi-v7a"
        //-    }
        //-}
    }
    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled = true

        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    // For Kotlin projects
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
        languageVersion = '1.9'
    }


    packagingOptions {
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        exclude 'lib/**/libtranslate_jni.so'
        exclude 'lib/**/libdigitalink.so'
        exclude 'lib/**/libxeno_native.so'
        exclude 'lib/**/libmlkitcommonpipeline.so'
        exclude 'lib/**/libbarhopper_v2.so'
        exclude 'lib/**/libclassifier_jni.so'
        // exclude 'lib/**/libface_detector_v2_jni.so' → required for face detection in selfie
        exclude 'lib/**/libtensorflowlite_jni.so'
        // exclude 'lib/**/liblanguage_id_jni.so' → required for language detection
    }
    flavorDimensions "app"

    productFlavors {
        prodin {
            minSdkVersion 24
            applicationId "net.homecredit.selfcare"
            dimension "app"
            resValue "string", "app_name_hc", "Home Credit"
            // required by Yellow.AI
            resValue "string", "application_id_for_provider", "net.homecredit.selfcare.fileprovider"
            apply plugin: 'com.google.gms.google-services'
        }
        fakein {
            dimension "app"
            applicationIdSuffix ".fake"
            resValue "string", "app_name_hc", "Home Credit Fake"
            apply plugin: 'com.google.gms.google-services'
        }
        prodvn {
            applicationId "vn.homecredit.capp"
            dimension "app"
            resValue "string", "app_name_hc", "Home Credit"
            minSdkVersion 26
            apply from: "${rootProject.projectDir.parentFile.parentFile.toPath()}/plugins/flutter_true_call/android/true_call_build.gradle"
            apply plugin: 'com.google.gms.google-services'
        }
        fakevn {
            applicationId "vn.homecredit.capp.fake"
            dimension "app"
            resValue "string", "app_name_hc", "Home Credit Fake"
            minSdkVersion 26
            apply from: "${rootProject.projectDir.parentFile.parentFile.toPath()}/plugins/flutter_true_call/android/true_call_build.gradle"
            apply plugin: 'com.google.gms.google-services'
        }
    }
}

flutter {
    source '../..'
    target getInputFile()
}

dependencies {
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test:runner:1.5.1'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.0'
    androidTestImplementation('com.microsoft.appcenter:espresso-test-extension:1.4')
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava'
    implementation "com.android.installreferrer:installreferrer:2.2"
    String flavor = getCurrentFlavor()
    if (flavor == "prodin" || flavor == "fakein") {
        implementation "com.truecaller.android.sdk:truecaller-sdk:3.0.0"
    }    
    
    implementation 'com.google.firebase:firebase-analytics:17.4.1'        
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:1.2.2")
    implementation "androidx.window:window:1.0.0-rc01"
    implementation "androidx.window:window-java:1.0.0-rc01"
    implementation "androidx.core:core-splashscreen:1.0.0"
    implementation 'com.huaweicloud:esdk-obs-android:3.21.12'
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation("com.google.android.gms:play-services-ads-identifier:18.0.1")
    implementation "com.threatmark.mobile.android:sdk:1.17.0"
}

def getCurrentFlavor() {
    Gradle gradle = getGradle()
    String tskReqStr = gradle.getStartParameter().getTaskRequests().toString()

    Pattern pattern;

    // For APK
    if (tskReqStr.contains("assemble"))
        pattern = Pattern.compile("assemble(\\w+)(Release|Debug|Profile)")
    // For Bundle
    else if (tskReqStr.contains("bundle"))
        pattern = Pattern.compile("bundle(\\w+)(Release|Debug|Profile)")
    else
        pattern = Pattern.compile("generate(\\w+)(Release|Debug|Profile)")

    Matcher matcher = pattern.matcher(tskReqStr)

    if (matcher.find())
        return matcher.group(1).toLowerCase()
    else {
        println "NO MATCH FOUND"
        return ""
    }
}

def getInputFile() {
    String flavor = getCurrentFlavor()
    if (flavor == "prodin") {
        return "lib/main_prod.dart"
    }
    return "lib/main_fake.dart"
}

configurations {
    all {
        exclude group: 'androidx.lifecycle', module: 'lifecycle-viewmodel-ktx'
        resolutionStrategy.eachDependency { DependencyResolveDetails details ->
            if (details.requested.group == 'androidx.test' && details.requested.name == 'runner') {
                details.useVersion '1.5.1'
            }
            if (details.requested.group == 'androidx.test' && details.requested.name == 'rules') {
                details.useVersion '1.5.0'
            }
            if (details.requested.group == 'androidx.test.ext' && details.requested.name == 'junit') {
                details.useVersion '1.1.4'
            }
            if (details.requested.group == 'androidx.test.espresso' && details.requested.name == 'espresso-core') {
                details.useVersion '3.5.0'
            }
            // Enforce this version to avoid compatibility issues for the ID card NFC reader SDK.
            if (details.requested.group == 'org.bouncycastle' && details.requested.name == 'bcprov-jdk15to18') {
                details.useVersion '1.76'
            }
            if (details.requested.group == 'org.bouncycastle' && details.requested.name == 'bcutil-jdk15to18') {
                details.useVersion '1.76'
            }
        }
    }
//Use this to list all configurations in the project
tasks.register("listConfigurations") {
    doLast {
        configurations.each {
            println it.name
        }
    }
}



android.applicationVariants.all { variant ->
    def variantName = variant.name // e.g., prodvnDebug
    def capitalized = variantName.capitalize()
    def taskName = "list${capitalized}Dependencies"
    def configName = "${variantName}RuntimeClasspath"

    tasks.register(taskName) {
        group = "Reporting"
        description = "Lists dependencies for the ${variantName} build variant"

  doLast {
    def config = configurations.findByName(configName)
    def outputFile = new File(buildDir, "dependency-report-${variantName}.txt")

    if (config == null) {
        println "Configuration '$configName' not found."
        return
    }

    outputFile.parentFile.mkdirs()

    def unresolved = []

    outputFile.withWriter { writer ->
        writer.println("Dependencies for configuration: $configName")

        config.incoming.resolutionResult.allDependencies.each { dep ->
            if (dep instanceof ResolvedDependencyResult) {
                def selected = dep.selected
                def group = selected.moduleVersion.group ?: "unknown-group"
                def name = selected.moduleVersion.name
                def version = selected.moduleVersion.version ?: "unspecified"
                writer.println(" - ${group}:${name}:${version}")
            } else if (dep instanceof UnresolvedDependencyResult) {
                def attempted = dep.attempted
                unresolved << attempted.displayName
                writer.println(" - [UNRESOLVED] ${attempted.displayName}")
            }
        }

        if (!unresolved.isEmpty()) {
            writer.println("\n[!] WARNING: Some dependencies could not be resolved:")
            unresolved.each { writer.println(" - $it") }
        }
    }

    println "Dependency report for $variantName written to: $outputFile"

}

    }

    // Run this report task before the actual build of that variant
    variant.assembleProvider.configure {
        dependsOn(taskName)
    }
}




}