-keep class androidx.lifecycle.** { *; }
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-keep class com.builttoroam.devicecalendar.** { *; }
-keep class org.xmlpull.v1.** { *;}
-dontwarn org.xmlpull.v1.**
-dontwarn java.lang.invoke.StringConcatFactory
-ignorewarnings
-keepattributes *Annotation*
-keepattributes Exceptions
-keepattributes InnerClasses
-keepattributes Signature
-keep class com.hianalytics.android.**{*;}
-keep class com.huawei.updatesdk.**{*;}
-keep class com.huawei.hms.**{*;}

## Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-dontwarn io.flutter.embedding.**
-keep class com.huawei.hms.flutter.** { *; }
-repackageclasses
#------------ Proguard rules for paynimo ----------------#
-dontnote android.net.http.*
-dontnote org.apache.commons.codec.**
-dontnote org.apache.http.**

-keep class com.paynimo.android.payment.** { *; }
-dontwarn com.paynimo.android.payment.**

-keepclassmembernames class * {
    java.lang.Class class$(java.lang.String);
    java.lang.Class class$(java.lang.String, boolean);
}

-keep class retrofit.** {
    <fields>;
    <methods>;
}
-dontwarn retrofit.**

# SAS
-keep class com.sas.mkt.mobile.sdk.** { *; }
-keep class com.sas.ia.** { *; }
-keep class androidx.fragment.app.** {*; }

# Appsflyer
-dontwarn com.appsflyer.**
-keep public class com.google.firebase.messaging.FirebaseMessagingService { public *; }

# Firebase
-keep class com.google.firebase.** { *; } 
-keep class com.firebase.** { *; }
-keep class org.apache.** { *; }
-keepnames class com.fasterxml.jackson.** { *; }
-keepnames class javax.servlet.** { *; }
-keepnames class org.ietf.jgss.** { *; }
-dontwarn org.w3c.dom.**
-dontwarn org.joda.time.**
-dontwarn org.shaded.apache.**
-dontwarn org.ietf.jgss.**


# Gson specific classes
-keep class sun.misc.Unsafe {
    <fields>;
    <methods>;
}

# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.** {
<fields>;
    <methods>;
}
# #---------------End: proguard configuration for Gson  ----------

-keep class de.greenrobot.event.** {
    <fields>;
    <methods>;
}
-keep class com.squareup.** {
    <fields>;
    <methods>;
}

-keep class android.support.v4.** {
    <fields>;
    <methods>;
}

-keep class android.support.v7.** {
    <fields>;
    <methods>;
}

-keep class android.service.media.MediaBrowserService {
    <fields>;
    <methods>;
}
-dontwarn android.service.media.MediaBrowserService
-keep class org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement {
     <fields>;
     <methods>;
}
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

-keep interface android.service.media.IMediaBrowserServiceCallbacks {
    <fields>;
    <methods>;
}
-keep class okio.** {
    <fields>;
    <methods>;
}
-keep class java.nio.file.** {
    <fields>;
    <methods>;
}
-keep class hc.capp.digio.DigioPluginActivity { public void onDigioNative*(...);}
-keep class hc.capp.digio.DigioPluginActivity { public void onDigioKyc*(...);}
-keepclassmembers class * {   @android.webkit.JavascriptInterface <methods>;}
-keepattributes JavascriptInterface
-keepattributes *Annotation*
-dontwarn org.json.**
-keep class org.json** { *; }

-dontwarn java.nio.file.**
#------------ End Proguard rules for paynimo ----------------#

#------------ Start Proguard rules for payoo_vn ----------------#
####################################################################################
# PAYMENT SDK
####################################################################################
-keep class vn.payoo.core.** { *; }
-dontwarn vn.payoo.core.**
-keepclassmembers enum vn.payoo.core.** { *; }

-keep class vn.payoo.model.** { *; }
-dontwarn vn.payoo.model.**
-keepclassmembers enum vn.payoo.model.** { *; }

-keep class vn.payoo.paymentsdk.PayooPaymentSDK {
    public static <methods>;
}
-keepclassmembers class vn.payoo.paymentsdk.PayooPaymentSDK {
   public static ** Companion;
}
-keepclassmembers enum vn.payoo.paymentsdk.** { *; }
-dontwarn vn.payoo.**
-keep class vn.payoo.** { *; }

####################################################################################
# DATA EXCEPTION
####################################################################################
-keep class vn.payoo.paymentsdk.data.exception.** { *; }
-dontwarn vn.payoo.paymentsdk.data.exception.**

####################################################################################
# DATA MODEL
####################################################################################
-keep class vn.payoo.paymentsdk.data.model.** { *; }
-dontwarn vn.payoo.paymentsdk.data.model.**
#------------ End Proguard rules for payoo_vn ----------------#
-dontwarn co.hyperverge.** 
-keepclassmembers class * implements javax.net.ssl.SSLSocketFactory { 
	private javax.net.ssl.SSLSocketFactory delegate; 
}

-keep class com.innovatrics.dot.face.*.*Module { *; }

# YellowAI
-keep class com.yellowmessenger.** { *; }

#------------ Start: Proguard rules for trueid call ----------------#
-keep class org.webrtc.** { *; }
-keep class vng.trueid.call.** { *; }
-dontwarn org.webrtc.**
#------------ End: Proguard rules for trueid call ----------------#

#------------ Start: Bug 114866: [crashlytics] Exception java.lang.NoSuchMethodError -> https://github.com/flutter/flutter/issues/141949 ----------------#
-keep class j$.util.function.Function
-keep class j$.util.function.Predicate$-CC {
    j$.util.function.Predicate $default$and(j$.util.function.Predicate, j$.util.function.Predicate);
    j$.util.function.Predicate $default$negate(j$.util.function.Predicate);
    j$.util.function.Predicate $default$or(j$.util.function.Predicate, j$.util.function.Predicate);
}
-keep class j$.util.function.Predicate { *; }
-keep class j$.util.stream.Stream {
    boolean anyMatch(j$.util.function.Predicate);
}
#------------ End: Bug 114866 ----------------#

# Huawei analytics kit 
-ignorewarnings
-keepattributes *Annotation*
-keepattributes Exceptions
-keepattributes InnerClasses
-keepattributes Signature
-keep class com.hianalytics.android.**{*;}
-keep class com.huawei.updatesdk.**{*;}
-keep class com.huawei.hms.**{*;}
-keep class com.huawei.hms.flutter.** { *; }
-repackageclasses
