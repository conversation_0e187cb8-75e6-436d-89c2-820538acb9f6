package net.homecredit.selfcare

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.webkit.WebView
import androidx.annotation.RequiresApi
import androidx.multidex.MultiDex
// import com.homecredit.sas_collector.SasCollectorPlugin
import android.app.Application

class GmaApplication : Application() {

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val process = getProcessName()
            if (packageName !== process) {
                WebView.setDataDirectorySuffix(process)
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun registerReceiver(receiver: BroadcastReceiver?, filter: IntentFilter?): Intent? {
        return if (Build.VERSION.SDK_INT >= 34 && applicationInfo.targetSdkVersion >= 34) {
            super.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED)
        } else {
            super.registerReceiver(receiver, filter, Context.RECEIVER_NOT_EXPORTED)
        }
    }
    override fun onCreate() {
        super.onCreate()

        // val sasCollector = SasCollectorPlugin()
        // sasCollector.initialize(this.applicationContext)
    }

}