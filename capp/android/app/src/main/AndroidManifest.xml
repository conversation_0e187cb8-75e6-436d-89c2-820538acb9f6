<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" >
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.HIDE_OVERLAY_WINDOWS" />

    <application
        android:name="${applicationName}"
        android:label="@string/app_name_hc"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:screenOrientation="portrait"
        android:requestLegacyExternalStorage="true"
        android:allowBackup="false"
        android:fullBackupContent="false"
        android:theme="@style/LaunchTheme"
        android:supportsRtl="true"
        android:usesCleartextTraffic="false"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:replace="android:allowBackup,android:fullBackupContent,android:name,android:label,android:theme,android:supportsRtl,android:screenOrientation,android:usesCleartextTraffic">
        <property android:name="REQUIRE_SECURE_ENV" android:value="1" />
        <meta-data android:name="com.google.android.geo.API_KEY" android:value="AIzaSyC9ZncCWVVJBEav6zQDPeFY4HeMise0hC0" />
        <meta-data android:name="com.google.firebase.messaging.default_notification_channel_id" android:value="@string/default_notification_channel_id" />
        <activity android:name="net.homecredit.selfcare.MainActivity"
            android:launchMode="singleTask"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:screenOrientation="portrait"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize"
            android:exported="true"
            >
            <meta-data android:name="com.google.firebase.messaging.default_notification_channel_id" android:value="@string/default_notification_channel_id" />
            <meta-data android:name="flutter_deeplinking_enabled" android:value="false" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="com.payu.custombrowser.PreLollipopPaymentsActivity" android:exported="true" android:screenOrientation="portrait" />
        <activity android:name="com.apptreesoftware.barcodescan.BarcodeScannerActivity" android:exported="true" android:screenOrientation="portrait" />
        <receiver android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver" android:exported="true" tools:replace="android:exported">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver" />

        <activity android:name="com.yalantis.ucrop.UCropActivity" android:screenOrientation="portrait" android:theme="@style/Theme.AppCompat.Light.NoActionBar" android:exported="true" />

        <provider android:name="androidx.core.content.FileProvider" android:authorities="${applicationId}.fileProvider" android:exported="false" android:grantUriPermissions="true" tools:replace="android:authorities">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/filepaths" tools:replace="android:resource" />
        </provider>
        <provider android:name="wtf.zikzak.zikzak_inappwebview_android.InAppWebViewFileProvider" android:authorities="${applicationId}.zikzak_inappwebview.fileprovider" android:exported="false" android:grantUriPermissions="true">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/filepaths" />
        </provider>
        <meta-data android:name="flutterEmbedding" android:value="2" />
        <meta-data android:name="google_analytics_automatic_screen_reporting_enabled" android:value="false" />
    </application>

    <queries>
        <intent>
            <action android:name="android.intent.action.DIAL" />
            <data android:scheme="tel" />
        </intent>
        <intent>
            <action android:name="android.intent.action.SENDTO"/>
            <category android:name="android.intent.category.DEFAULT"/>
            <data android:scheme="mailto"/>
        </intent>
    </queries>
</manifest>