<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools"
     >
    <uses-permission android:name="android.permission.READ_SMS" tools:node="remove"/>
    <uses-permission android:name="android.permission.USE_BIOMETRIC"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCEPT_HANDOVER" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_BLOBS_ACROSS_USERS" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_CHECKIN_PROPERTIES" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" tools:node="remove" />
    <uses-permission android:name="android.permission.ADD_VOICEMAIL" tools:node="remove" />
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" tools:node="remove" />
    <uses-permission android:name="android.permission.BATTERY_STATS" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_APPWIDGET" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_AUTOFILL_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_CALL_REDIRECTION_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_CARRIER_MESSAGING_CLIENT_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_CARRIER_MESSAGING_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_CARRIER_SERVICES" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_CHOOSER_TARGET_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_COMPANION_DEVICE_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_CONDITION_PROVIDER_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_CONTROLS" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_CREDENTIAL_PROVIDER_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_DREAM_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_INCALL_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_INPUT_METHOD" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_MIDI_DEVICE_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_NFC_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_PRINT_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_QUICK_ACCESS_WALLET_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_QUICK_SETTINGS_TILE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_REMOTEVIEWS" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_SCREENING_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_TEXT_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_TV_INPUT" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_TV_INTERACTIVE_APP" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_VISUAL_VOICEMAIL_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_VOICE_INTERACTION" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_VPN_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_VR_LISTENER_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.BIND_WALLPAPER" tools:node="remove" />
    <uses-permission android:name="android.permission.BLUETOOTH" tools:node="remove"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" tools:node="remove" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" tools:node="remove" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" tools:node="remove" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" tools:node="remove" />
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" tools:node="remove" />
    <uses-permission android:name="android.permission.BODY_SENSORS" tools:node="remove" />
    <uses-permission android:name="android.permission.BODY_SENSORS_BACKGROUND" tools:node="remove" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_REMOVED" tools:node="remove" />
    <uses-permission android:name="android.permission.BROADCAST_SMS" tools:node="remove" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" tools:node="remove" />
    <uses-permission android:name="android.permission.BROADCAST_WAP_PUSH" tools:node="remove" />
    <uses-permission android:name="android.permission.CALL_COMPANION_APP" tools:node="remove" />
    <uses-permission android:name="android.permission.CALL_PRIVILEGED" tools:node="remove" />
    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" tools:node="remove" />
    <uses-permission android:name="android.permission.CHANGE_COMPONENT_ENABLED_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" tools:node="remove" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" tools:node="remove" />
    <uses-permission android:name="android.permission.CONFIGURE_WIFI_DISPLAY" tools:node="remove" />
    <uses-permission android:name="android.permission.CONTROL_LOCATION_UPDATES" tools:node="remove" />
    <uses-permission android:name="android.permission.CREDENTIAL_MANAGER_QUERY_CANDIDATE_CREDENTIALS" tools:node="remove" />
    <uses-permission android:name="android.permission.CREDENTIAL_MANAGER_SET_ALLOWED_PROVIDERS" tools:node="remove" />
    <uses-permission android:name="android.permission.CREDENTIAL_MANAGER_SET_ORIGIN" tools:node="remove" />
    <uses-permission android:name="android.permission.DELETE_CACHE_FILES" tools:node="remove" />
    <uses-permission android:name="android.permission.DELETE_PACKAGES" tools:node="remove" />
    <uses-permission android:name="android.permission.DELIVER_COMPANION_MESSAGES" tools:node="remove" />
    <uses-permission android:name="android.permission.DETECT_SCREEN_CAPTURE" tools:node="remove" />
    <uses-permission android:name="android.permission.DIAGNOSTIC" tools:node="remove" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" tools:node="remove" />
    <uses-permission android:name="android.permission.DUMP" tools:node="remove" />
    <uses-permission android:name="android.permission.ENFORCE_UPDATE_OWNERSHIP" tools:node="remove" />
    <uses-permission android:name="android.permission.EXECUTE_APP_ACTION" tools:node="remove" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" tools:node="remove" />
    <uses-permission android:name="android.permission.FACTORY_TEST" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_HEALTH" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_REMOTE_MESSAGING" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED" tools:node="remove" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" tools:node="remove" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS_PRIVILEGED" tools:node="remove" />
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" tools:node="remove" />
    <uses-permission android:name="android.permission.GET_TASKS" tools:node="remove" />
    <uses-permission android:name="android.permission.GET_TOP_ACTIVITY_INFO" tools:node="remove" />
    <uses-permission android:name="android.permission.GLOBAL_SEARCH" tools:node="remove" />
    <uses-permission android:name="android.permission.HIDE_OVERLAY_WINDOWS" />
    <uses-permission android:name="android.permission.HIGH_SAMPLING_RATE_SENSORS" tools:node="remove" />
    <uses-permission android:name="android.permission.INSTALL_LOCATION_PROVIDER" tools:node="remove" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" tools:node="remove" />
    <uses-permission android:name="android.permission.INSTALL_SHORTCUT" tools:node="remove" />
    <uses-permission android:name="android.permission.INSTANT_APP_FOREGROUND_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_PROFILES" tools:node="remove" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" tools:node="remove" />
    <uses-permission android:name="android.permission.LAUNCH_CAPTURE_CONTENT_ACTIVITY_FOR_NOTE" tools:node="remove" />
    <uses-permission android:name="android.permission.LAUNCH_MULTI_PANE_SETTINGS_DEEP_LINK" tools:node="remove" />
    <uses-permission android:name="android.permission.LOADER_USAGE_STATS" tools:node="remove" />
    <uses-permission android:name="android.permission.LOCATION_HARDWARE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_LOCK_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_ACCESSIBILITY" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_ACCOUNT_MANAGEMENT" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_ACROSS_USERS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_ACROSS_USERS_FULL" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_ACROSS_USERS_SECURITY_CRITICAL" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_AIRPLANE_MODE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_APPS_CONTROL" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_APP_RESTRICTIONS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_APP_USER_DATA" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_AUDIO_OUTPUT" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_AUTOFILL" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_BACKUP_SERVICE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_BLUETOOTH" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_BUGREPORT" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_CALLS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_CAMERA" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_CERTIFICATES" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_COMMON_CRITERIA_MODE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_DEBUGGING_FEATURES" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_DEFAULT_SMS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_DEVICE_IDENTIFIERS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_DISPLAY" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_FACTORY_RESET" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_FUN" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_INPUT_METHODS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_INSTALL_UNKNOWN_SOURCES" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_KEEP_UNINSTALLED_PACKAGES" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_KEYGUARD" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_LOCALE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_LOCATION" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_LOCK" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_LOCK_CREDENTIALS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_LOCK_TASK" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_METERED_DATA" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_MICROPHONE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_MOBILE_NETWORK" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_MODIFY_USERS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_MTE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_NEARBY_COMMUNICATION" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_NETWORK_LOGGING" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_ORGANIZATION_IDENTITY" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_OVERRIDE_APN" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_PACKAGE_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_PHYSICAL_MEDIA" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_PRINTING" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_PRIVATE_DNS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_PROFILES" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_PROFILE_INTERACTION" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_PROXY" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_QUERY_SYSTEM_UPDATES" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_RESET_PASSWORD" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_RESTRICT_PRIVATE_DNS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_RUNTIME_PERMISSIONS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_RUN_IN_BACKGROUND" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SAFE_BOOT" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SCREEN_CAPTURE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SCREEN_CONTENT" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SECURITY_LOGGING" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SETTINGS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SMS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_STATUS_BAR" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SUPPORT_MESSAGE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SUSPEND_PERSONAL_APPS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SYSTEM_APPS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SYSTEM_DIALOGS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_SYSTEM_UPDATES" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_TIME" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_USB_DATA_SIGNALLING" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_USB_FILE_TRANSFER" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_USERS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_VPN" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_WALLPAPER" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_WIFI" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_WINDOWS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_POLICY_WIPE_DATA" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_DOCUMENTS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_MEDIA" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_ONGOING_CALLS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_WIFI_INTERFACES" tools:node="remove" />
    <uses-permission android:name="android.permission.MANAGE_WIFI_NETWORK_SELECTION" tools:node="remove" />
    <uses-permission android:name="android.permission.MASTER_CLEAR" tools:node="remove" />
    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" tools:node="remove" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.MOUNT_FORMAT_FILESYSTEMS" tools:node="remove" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" tools:node="remove" />
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" tools:node="remove" />
    <uses-permission android:name="android.permission.NFC" tools:node="remove" />
    <uses-permission android:name="android.permission.NFC_PREFERRED_PAYMENT_INFO" tools:node="remove" />
    <uses-permission android:name="android.permission.NFC_TRANSACTION_EVENT" tools:node="remove" />
    <uses-permission android:name="android.permission.OVERRIDE_WIFI_CONFIG" tools:node="remove" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" tools:node="remove" />
    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" tools:node="remove" />
    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" tools:node="remove" />
    <uses-permission android:name="android.permission.PROVIDE_OWN_AUTOFILL_SUGGESTIONS" tools:node="remove" />
    <uses-permission android:name="android.permission.PROVIDE_REMOTE_CREDENTIALS" tools:node="remove" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_ASSISTANT_APP_SEARCH_DATA" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_BASIC_PHONE_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_CONTACTS" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_HOME_APP_SEARCH_DATA" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_INPUT_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_LOGS" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_NEARBY_STREAMING_POLICY" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_PRECISE_PHONE_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_VOICEMAIL" tools:node="remove" />
    <uses-permission android:name="android.permission.REBOOT" tools:node="remove" />
    <uses-permission android:name="android.permission.RECEIVE_MMS" tools:node="remove" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" tools:node="remove" />
    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_PROFILE_APP_STREAMING" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_PROFILE_AUTOMOTIVE_PROJECTION" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_PROFILE_COMPUTER" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_PROFILE_GLASSES" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_PROFILE_NEARBY_DEVICE_STREAMING" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_PROFILE_WATCH" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_RUN_IN_BACKGROUND" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_SELF_MANAGED" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_START_FOREGROUND_SERVICES_FROM_BACKGROUND" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_USE_DATA_IN_BACKGROUND" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_OBSERVE_COMPANION_DEVICE_PRESENCE" tools:node="remove" />
    <uses-permission android:name="android.permission.REQUEST_PASSWORD_COMPLEXITY" tools:node="remove" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" tools:node="remove" />
    <uses-permission android:name="android.permission.RUN_USER_INITIATED_JOBS" tools:node="remove" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" tools:node="remove" />
    <uses-permission android:name="android.permission.SEND_RESPOND_VIA_MESSAGE" tools:node="remove" />
    <uses-permission android:name="android.permission.SEND_SMS" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_ALARM" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_ALWAYS_FINISH" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_ANIMATION_SCALE" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_DEBUG_APP" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_PREFERRED_APPLICATIONS" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_PROCESS_LIMIT" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_TIME" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_TIME_ZONE" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" tools:node="remove" />
    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" tools:node="remove" />
    <uses-permission android:name="android.permission.SIGNAL_PERSISTENT_PROCESSES" tools:node="remove" />
    <uses-permission android:name="android.permission.SMS_FINANCIAL_TRANSACTIONS" tools:node="remove" />
    <uses-permission android:name="android.permission.START_FOREGROUND_SERVICES_FROM_BACKGROUND" tools:node="remove" />
    <uses-permission android:name="android.permission.START_VIEW_APP_FEATURES" tools:node="remove" />
    <uses-permission android:name="android.permission.START_VIEW_PERMISSION_USAGE" tools:node="remove" />
    <uses-permission android:name="android.permission.STATUS_BAR" tools:node="remove" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" tools:node="remove" />
    <uses-permission android:name="android.permission.SUBSCRIBE_TO_KEYGUARD_LOCKED_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.TRANSMIT_IR" tools:node="remove" />
    <uses-permission android:name="android.permission.TURN_SCREEN_ON" tools:node="remove" />
    <uses-permission android:name="android.permission.UNINSTALL_SHORTCUT" tools:node="remove" />
    <uses-permission android:name="android.permission.UPDATE_DEVICE_STATS" tools:node="remove" />
    <uses-permission android:name="android.permission.UPDATE_PACKAGES_WITHOUT_USER_ACTION" tools:node="remove" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" tools:node="remove" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" tools:node="remove" />
    <uses-permission android:name="android.permission.USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER" tools:node="remove" />
    <uses-permission android:name="android.permission.USE_SIP" tools:node="remove" />
    <uses-permission android:name="android.permission.UWB_RANGING" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_APN_SETTINGS" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_GSERVICES" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_VOICEMAIL" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" tools:node="remove" />
    <uses-permission android:name="android.permission.CALL_PHONE" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_CALENDAR" tools:node="remove" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" tools:node="remove"/>
    <uses-permission android:name="android.permission.NFC" />
    <uses-feature android:name="android.hardware.nfc" android:required="false" />
    <application
        android:name="net.homecredit.selfcare.Application"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        tools:replace="android:icon,android:roundIcon">
        <property android:name="REQUIRE_SECURE_ENV" android:value="1" />

        <activity android:name="net.homecredit.selfcare.MainActivity" tools:node="merge" android:exported="true">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="app.gma.homecredit.vn" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" android:host="app.gma.homecredit.vn" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="hcv.onelink.me" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="homezuiapp" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="hcvn" android:pathPrefix="/result" />
            </intent-filter>

            <intent-filter>
                <action android:name="vn.homecredit.capp.a2a" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <provider android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths"
                tools:replace="android:resource" />
        </provider>
        <provider android:name="wtf.zikzak.zikzak_inappwebview_android.InAppWebViewFileProvider"
            android:authorities="${applicationId}.zikzak_inappwebview_android.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities"/>
        <meta-data
            android:name="android.support.FILE_PROVIDER_PATHS"
            android:resource="@xml/filepaths" />

    </application>
    <queries>
        <package android:name="com.mservice.momotransfer" />
        <package android:name="vn.momo.platform.test" />
    </queries>
</manifest>