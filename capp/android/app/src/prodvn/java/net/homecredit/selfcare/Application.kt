package net.homecredit.selfcare

import android.annotation.SuppressLint
import android.app.AppOpsManager
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.app.AsyncNotedAppOp
import android.app.SyncNotedAppOp
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.util.Log
import android.webkit.WebView
import androidx.annotation.RequiresApi
import androidx.multidex.MultiDex
import android.app.Application

//import com.threatmark.mobile.android.ThreatMark
//import com.threatmark.mobile.android.StartParameters
//import com.threatmark.mobile.android.CookiesListener
//import com.threatmark.mobile.android.ThreatMarkAlreadyActiveException
//import com.threatmark.mobile.android.ThreatMarkException
//import javax.net.ssl.X509TrustManager
//import java.security.cert.X509Certificate

class GmaApplication : Application() {

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val process = getProcessName()
            if (packageName !== process) {
                WebView.setDataDirectorySuffix(process)
            }
        }
    }
    @RequiresApi(Build.VERSION_CODES.O)
    override fun registerReceiver(receiver: BroadcastReceiver?, filter: IntentFilter?): Intent? {
        return if (Build.VERSION.SDK_INT >= 34 && applicationInfo.targetSdkVersion >= 34) {
            super.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED)
        } else {
            super.registerReceiver(receiver, filter, Context.RECEIVER_NOT_EXPORTED)
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    override fun onCreate() {
        super.onCreate()
        if (BuildConfig.DEBUG) {
            val appOpsCallback = @RequiresApi(Build.VERSION_CODES.R)
            object : AppOpsManager.OnOpNotedCallback() {
                private fun logPrivateDataAccess(opCode: String, trace: String) {

                    Log.i(
                        "VN.HOMECREDIT.CAPP", "Private data requested. " +
                                "Operation: $opCode\nStack Trace:\n$trace"
                    )


                }

                @RequiresApi(Build.VERSION_CODES.R)
                override fun onNoted(syncNotedAppOp: SyncNotedAppOp) {
                    logPrivateDataAccess(
                        syncNotedAppOp.op, Throwable().stackTrace.toString()
                    )
                }

                @RequiresApi(Build.VERSION_CODES.R)
                override fun onSelfNoted(syncNotedAppOp: SyncNotedAppOp) {
                    logPrivateDataAccess(
                        syncNotedAppOp.op, Throwable().stackTrace.toString()
                    )
                }

                override fun onAsyncNoted(asyncNotedAppOp: AsyncNotedAppOp) {
                    logPrivateDataAccess(asyncNotedAppOp.op, asyncNotedAppOp.message)
                }
            }

            val appOpsManager =
                getSystemService(AppOpsManager::class.java) as AppOpsManager
            appOpsManager.setOnOpNotedCallback(mainExecutor, appOpsCallback)
        }
    }

//    private fun getDefaultTrustManager(): X509TrustManager {
//        val trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
//        trustManagerFactory.init(null as KeyStore?)
//        val trustManagers = trustManagerFactory.trustManagers
//        return trustManagers.first { it is X509TrustManager } as X509TrustManager
//    }
//    fun initThreatmarkSdk() {
//        try {
//            ThreatMark.start(StartParameters (
//                context = applicationContext,
//                url = 'https://android.c470.threatmark.com/application/gma-android/android',
//                keyFilename = "tmSdkKey.pem",
//                loggingEnabled = BuildConfig.DEBUG,
//                trustManager = getDefaultTrustManager(),
//                callback = object : CookiesListener {
//                    override fun onCookiesResult(cookies: Map<String, String>) {
//                        sendThreatmarkCookiesToBackend();
//                    }
//                }
//            ))
//        } catch (e: ThreatMarkAlreadyActiveException) {
//            Log.e(LOG_TAG, "Starting SDK when already active.", e)
//        } catch (e: ThreatMarkException) {
//            Log.e(LOG_TAG, "Unknown ThreatMark exception.", e)
//        }
//    }
//    fun sendThreatmarkCookiesToBackend() {
//        android.util.Log.i("${LOG_TAG} Threatmark cookies: ${cookies}");
//    }

}