def secureProperties = new Properties()
def securePropertiesFile = rootProject.file('secure.properties')
if (securePropertiesFile.exists()) {
    securePropertiesFile.withReader('UTF-8') { reader ->
        secureProperties.load(reader)
    }
}
ext {
    secure = secureProperties
}

buildscript {
    ext.kotlin_version = '2.1.21'
    ext.material_version = '1.4.0'
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven { url 'https://mirrors.huaweicloud.com/repository/maven/' }
        maven { url "https://www.jitpack.io" }
        maven { url "https://maven.google.com" }
        maven { url 'https://developer.huawei.com/repo/' }
        // maven {
        //     url "https://git.threatmark.com/api/v4/projects/5/packages/maven"
        //     name "ThreatMark"
        //     credentials(HttpHeaderCredentials) {
        //         name = "Private-Token"
        //         value = "**************************"
        //     }
        //     authentication {
        //         header(HttpHeaderAuthentication)
        //     }
        // }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.3'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.2'
        classpath 'com.huawei.agconnect:agcp:1.9.1.303'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
        maven { url "https://maven.google.com" }
        // maven {
        //     url "https://git.threatmark.com/api/v4/projects/5/packages/maven"
        //     name "ThreatMark"
        //     credentials(HttpHeaderCredentials) {
        //         name = "Private-Token"
        //         value = "**************************"
        //     }
        //     authentication {
        //         header(HttpHeaderAuthentication)
        //     }
        // }
    }

    //directly address and modify all submodules in the app during build. THIS IS THE SOURCE OF TRUTH NOW
    subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            //modify the android block in build gradle files of these modules
            project.android {
                buildFeatures {
                    buildConfig true
                }
                defaultConfig {
                    minSdkVersion 24
                }
                ndkVersion "27.0.12077973"
                
                //force namespace onto sumbmodules that do not have it. Name is taken from their group name eg. group 'hc.capp.ga360.ga360_plugin'
                if (!project.android.hasProperty('namespace') || project.android.namespace == null) {
                    namespace = project.group 
                }
                //force all submodules to predefined minSdkVersion
                if (project.android.compileSdkVersion == null || (project.android.compileSdkVersion !=  'android-35')) {
                    compileSdkVersion 35
                }
                    compileOptions {
                        sourceCompatibility = JavaVersion.VERSION_17
                        targetCompatibility = JavaVersion.VERSION_17
                    }

                if (project.android.hasProperty('kotlinOptions')) {
                    kotlinOptions {
                        jvmTarget = "17"
                        languageVersion = "2.1"
                    }
                }
            }
        }
    }
}

}

rootProject.buildDir = '../build'

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}
tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
