pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven {
            url 'https://developer.huawei.com/repo/'
        }

        maven {
            url = uri("https://git.threatmark.com/api/v4/projects/5/packages/maven")
            name = "ThreatMark"

            credentials(HttpHeaderCredentials) {
                name = "Private-Token"
                value = "**************************"
            }

            authentication {
                header(HttpHeaderAuthentication)
            }
        }
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.google.devtools.ksp" version "2.1.21-2.0.1" apply false
    id "com.android.application" version "8.10.0" apply false
    id "com.android.library" version "8.10.0" apply false
    id "org.jetbrains.kotlin.android" version "2.1.21" apply false
}

include ":app"
apply from: "${rootProject.projectDir.parentFile.parentFile.toPath()}/plugins/flutter_true_call/android/true_call_settings.gradle"

dependencyResolutionManagement {
    repositories {
        google()
        mavenCentral()
        maven {
            url = uri("https://git.threatmark.com/api/v4/projects/5/packages/maven")
            name = "ThreatMark"

            credentials(HttpHeaderCredentials) {
                name = "Private-Token"
                value = "**************************"
            }

            authentication {
                header(HttpHeaderAuthentication)
            }
        }
    }
}
