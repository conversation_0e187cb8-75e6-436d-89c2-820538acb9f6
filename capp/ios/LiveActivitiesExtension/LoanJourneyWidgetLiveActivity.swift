//
//  LoanJourneyWidgetLiveActivity.swift
//  LoanJourneyWidget
//
//  Created by <PERSON><PERSON> on 10/7/24.
//

#if canImport(ActivityKit)
import ActivityKit
import WidgetKit
import Swift<PERSON>

@available(iOS 16.2, *)
struct LoanJourneyWidgetLiveActivity: Widget {
    private let deeplinkKey = "capp_live_activities.loan_deeplink"
    private let queryStatusName = "status"
    private var deeplink: URL? {
        URL(string: deeplinkKey.localizedStringFromStorage)
    }
    
    // MARK: Lock Screen UI
    @ViewBuilder
    private func MainView(_ loanState: LoanState) -> some View {
        switch loanState {
        case .initialScoring:
            InitialScoringViewBuilder.MainView()
        case .scoringTimeExceeded:
            ScoringTimeExceededViewBuilder.MainView()
        case .scoringWithOneOffer:
            ScoringWithOneOfferViewBuilder.MainView()
        case .scoringWithMoreOffers:
            ScoringWithMoreOffersViewBuilder.MainView()
        case .scoringWithNoOffers:
            ScoringWithNoOffersViewBuilder.MainView()
        case .bod1EnterApplicationForm:
            Bod1EnterApplicationFormViewBuilder.MainView()
        case .bod2SubmittedApplicationForm:
            Bod2SubmittedApplicationFormViewBuilder.MainView()
        case .bod2TimeExceeded:
            Bod2TimeExceededViewBuilder.MainView()
        case .bod2ApplicationApproved:
            Bod2ApplicationApprovedViewBuilder.MainView()
        case .bod2AOs:
            Bod2AOsViewBuilder.MainView()
        case .bod2RejectedCancelled:
            Bod2RejectedCancelledViewBuilder.MainView()
        case .bod2ApplicationApprovedBigTicket:
            Bod2ApplicationApprovedBigTicketViewBuilder.MainView()
        case .notFoundSA:
            NoFoundSAViewBuilder.MainView()
        case .waitingSA:
            WaitingSAViewBuilder.MainView()
        case .acceptedSA:
            AcceptedSAViewBuilder.MainView()
        default:
            EmptyView()
       }
    }
    
    // MARK: Dynamic Island UI for Expnaded Leading
    @ViewBuilder
    private func ExpandedLeadingView(_ loanState: LoanState) -> some View {
        switch loanState {
        case .initialScoring:
            InitialScoringViewBuilder.ExpandLeadingView()
        case .scoringTimeExceeded:
            ScoringTimeExceededViewBuilder.ExpandLeadingView()
        case .scoringWithOneOffer:
            ScoringWithOneOfferViewBuilder.ExpandLeadingView()
        case .scoringWithMoreOffers:
            ScoringWithMoreOffersViewBuilder.ExpandLeadingView()
        case .scoringWithNoOffers:
            ScoringWithNoOffersViewBuilder.ExpandLeadingView()
        case .bod1EnterApplicationForm:
            Bod1EnterApplicationFormViewBuilder.ExpandLeadingView()
        case .bod2SubmittedApplicationForm:
            Bod2SubmittedApplicationFormViewBuilder.ExpandLeadingView()
        case .bod2TimeExceeded:
            Bod2TimeExceededViewBuilder.ExpandLeadingView()
        case .bod2ApplicationApproved:
            Bod2ApplicationApprovedViewBuilder.ExpandLeadingView()
        case .bod2AOs:
            Bod2AOsViewBuilder.ExpandLeadingView()
        case .bod2RejectedCancelled:
            Bod2RejectedCancelledViewBuilder.ExpandLeadingView()
        case .bod2ApplicationApprovedBigTicket:
            Bod2ApplicationApprovedBigTicketViewBuilder.ExpandLeadingView()
        case .notFoundSA:
            NoFoundSAViewBuilder.ExpandLeadingView()
        case .waitingSA:
            WaitingSAViewBuilder.ExpandLeadingView()
        case .acceptedSA:
            AcceptedSAViewBuilder.ExpandLeadingView()
        default:
            EmptyView()
        }
    }
    
    // MARK: Dynamic Island UI for Expnaded Bottom
    @ViewBuilder
    private func ExpandedBottomView(_ loanState: LoanState) -> some View {
        switch loanState {
        case .initialScoring:
            InitialScoringViewBuilder.ExpandBottomView()
        case .scoringTimeExceeded:
            ScoringTimeExceededViewBuilder.ExpandBottomView()
        case .scoringWithOneOffer:
            ScoringWithOneOfferViewBuilder.ExpandBottomView()
        case .scoringWithMoreOffers:
            ScoringWithMoreOffersViewBuilder.ExpandBottomView()
        case .scoringWithNoOffers:
            ScoringWithNoOffersViewBuilder.ExpandBottomView()
        case .bod1EnterApplicationForm:
            Bod1EnterApplicationFormViewBuilder.ExpandBottomView()
        case .bod2SubmittedApplicationForm:
            Bod2SubmittedApplicationFormViewBuilder.ExpandBottomView()
        case .bod2TimeExceeded:
            Bod2TimeExceededViewBuilder.ExpandBottomView()
        case .bod2ApplicationApproved:
            Bod2ApplicationApprovedViewBuilder.ExpandBottomView()
        case .bod2AOs:
            Bod2AOsViewBuilder.ExpandBottomView()
        case .bod2RejectedCancelled:
            Bod2RejectedCancelledViewBuilder.ExpandBottomView()
        case .bod2ApplicationApprovedBigTicket:
            Bod2ApplicationApprovedBigTicketViewBuilder.ExpandBottomView()
        case .notFoundSA:
            NoFoundSAViewBuilder.ExpandBottomView()
        case .waitingSA:
            WaitingSAViewBuilder.ExpandBottomView()
        case .acceptedSA:
            AcceptedSAViewBuilder.ExpandBottomView()
        default:
            EmptyView()
        }
    }
    
    // MARK: Dynamic Island UI for Minimal
    @ViewBuilder
    private func MinimalView(_ loanState: LoanState) -> some View {
        switch loanState {
        case .scoringWithOneOffer:
            CompletedAppLogoMinimalView()
        case .scoringWithNoOffers:
            CompletedAppLogoMinimalView()
        case .scoringWithMoreOffers:
            CompletedAppLogoMinimalView()
        case .bod2ApplicationApproved:
            CompletedAppLogoMinimalView()
        case .bod2RejectedCancelled:
            CompletedAppLogoMinimalView()
        case .bod2ApplicationApprovedBigTicket:
            CompletedAppLogoMinimalView()
        case .notFoundSA:
            CompletedAppLogoMinimalView()
        case .waitingSA:
            CompletedAppLogoMinimalView()
        case .acceptedSA:
            CompletedAppLogoMinimalView()
        default:
            AppLogoMinimalView()
       }
    }
    
    // MARK: Dynamic Island UI for Compact Leading
    @ViewBuilder
    private func CompactLeadingView(_ loanState: LoanState) -> some View {
        switch loanState {
        default:
            AppLogoMinimalView()
        }
    }
    
    // MARK: Dynamic Island UI for Compact Trailing
    @ViewBuilder
    private func CompactTrailingView(_ loanState: LoanState) -> some View {
        switch loanState {
        case .initialScoring:
            InitialScoringViewBuilder.CompactTrailingView()
        case .scoringTimeExceeded:
            ScoringTimeExceededViewBuilder.CompactTrailingView()
        case .scoringWithOneOffer:
            ScoringWithOneOfferViewBuilder.CompactTrailingView()
        case .scoringWithMoreOffers:
            ScoringWithMoreOffersViewBuilder.CompactTrailingView()
        case .scoringWithNoOffers:
            ScoringWithNoOffersViewBuilder.CompactTrailingView()
        case .bod1EnterApplicationForm:
            Bod1EnterApplicationFormViewBuilder.CompactTrailingView()
        case .bod2SubmittedApplicationForm:
            Bod2SubmittedApplicationFormViewBuilder.CompactTrailingView()
        case .bod2TimeExceeded:
            Bod2TimeExceededViewBuilder.CompactTrailingView()
        case .bod2ApplicationApproved:
            Bod2ApplicationApprovedViewBuilder.CompactTrailingView()
        case .bod2AOs:
            Bod2AOsViewBuilder.CompactTrailingView()
        case .bod2RejectedCancelled:
            Bod2RejectedCancelledViewBuilder.CompactTrailingView()
        case .bod2ApplicationApprovedBigTicket:
            Bod2ApplicationApprovedBigTicketViewBuilder.CompactTrailingView()
        case .notFoundSA:
            NoFoundSAViewBuilder.CompactTrailingView()
        case .waitingSA:
            WaitingSAViewBuilder.CompactTrailingView()
        case .acceptedSA:
            AcceptedSAViewBuilder.CompactTrailingView()
        default:
            EmptyView()
        }
    }
    
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: LAAttributes.self) { context in
            MainView(context.state.loanState)
                .widgetURL(getDeeplinkWithQuery(context.state))
                .onAppear {
                    setContentStatus(context.state, for: context.activityID)
                }
        } dynamicIsland: { context in
            DynamicIsland {
                DynamicIslandExpandedRegion(.leading, priority: 1) {
                    ExpandedLeadingView(context.state.loanState)
                }
                
                DynamicIslandExpandedRegion(.bottom, priority: 1) {
                    ExpandedBottomView(context.state.loanState)
                }
            } compactLeading: {
                CompactLeadingView(context.state.loanState)
            } compactTrailing: {
                CompactTrailingView(context.state.loanState)
            } minimal: {
                MinimalView(context.state.loanState)
            }
            .widgetURL(getDeeplinkWithQuery(context.state))
            .keylineTint(Color.primaryRed)
        }
    }
    
    private func getDeeplinkWithQuery(_ state: LAAttributes.ContentState) -> URL? {
        return deeplink?.appending(queryItems: [.init(name: queryStatusName, value: state.status)])
    }
    
    private func setContentStatus(_ state: LAAttributes.ContentState, for activityID: String) {
        let loanState = state.loanState
        if loanState == .endLA || loanState == .endApplicationLA { return }
        AppGroupUtils.shared.setValue(value: state.status, for: activityID)
    }
}
#endif
