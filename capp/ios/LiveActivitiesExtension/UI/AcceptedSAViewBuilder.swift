//
//  AcceptedSAViewBuilder.swift
//  LiveActivitiesExtension
//
//  Created by <PERSON><PERSON><PERSON> (VN) on 12/06/2025.
//

import SwiftUI

@available(iOS 16.2, *)
struct AcceptedSAViewBuilder {
    private init() {}
    // configurable properties
    static let titleKey = "capp_live_activities.accepted_sa_title"
    static let ctaKey = "capp_live_activities.accepted_sa_cta"
    static let bannerImageKey = "capp_live_activities.accepted_sa_banner_image_url"
    static let topHeight: CGFloat = 96.0
    static let bottomHeight: CGFloat = 56.0
    static let placeholderBannerImage: ImageResource = .saAvatarIcon

    static var customBannerImage: UIImage? {
        bannerImageKey.imageDataFromStorage
    }
    
    private struct BottomView: View {
        var body: some View {
            PrimaryButtonView(buttonText: ctaKey.localizedStringFromStorage, isFullWidth: true)
        }
    }
    
    // MainView is the UI on lock screen
    struct MainView: View {
        var body: some View {
            VStack(spacing: 0) {
                LockScreenTopView(
                    title: titleKey.localizedStringFromStorage,
                    titleFontSize: 18,
                    height: topHeight,
                    bannerSize: 64
                ) {
                    BannerImageView(customBannerImage: customBannerImage, placeholderImage: placeholderBannerImage)
                }
                .frame(height: topHeight)
                
                BottomView()
                    .padding(.bottom, 16)
                    .padding(.top, 8)
                    .padding(.horizontal, 16)
                    .frame(height: bottomHeight)
            }
            .background(.bottomComponent)
        }
    }
    
    struct CompactTrailingView: View {
        var body: some View {
            Image(.greenCheckmarkCircle)
                .resizable()
                .scaledToFit()
                .frame(height: 24)
        }
    }
    
    struct ExpandLeadingView: View {
        var body: some View {
            Image(.appLogo)
                    .resizable()
                    .scaledToFit()
                    .frame(height: 32)
                    .padding(.top, 4)
                    .padding(.leading, 4)
                    .padding(.bottom, 8)
            GeometryReader { proxy in
                HStack(alignment: .bottom) {
                    Text(titleKey.localizedStringFromStorage)
                        .font(.system(size: 16, weight: .bold, design: .default))
                        .lineLimit(1)
                        .multilineTextAlignment(.leading)
                        .padding(.leading, 4)
                    Spacer()
                }
                .frame(width: proxy.size.width, height: 16)
                .overlay(alignment: .trailing) {
                    BannerImageView(
                        customBannerImage: customBannerImage,
                        placeholderImage: placeholderBannerImage,
                        size: 56
                    ).padding(.bottom, 42)
                }
            }
            .frame(height: 18)
        }
    }
    
    struct ExpandBottomView: View {
        var body: some View {
            BottomView()
                .frame(height: 28)
                .padding(.horizontal, 4)
                .padding(.bottom, 8)
        }
    }
}
