//
//  Bod2ApplicationApprovedBigTicketViewBuilder.swift
//  LiveActivitiesExtension
//
//  Created by <PERSON><PERSON><PERSON> (VN) on 12/06/2025.
//

import SwiftUI

@available(iOS 16.2, *)
struct Bod2ApplicationApprovedBigTicketViewBuilder {
    private init() {}
    // configurable properties
    static let titleKey = "capp_live_activities.bod2_application_approved_big_ticket_title"
    static let subtitleKey = "capp_live_activities.bod2_application_approved_big_ticket_subtitle"
    static let ctaKey = "capp_live_activities.bod2_application_approved_big_ticket_cta"
    
    static let topHeight: CGFloat = 88.0
    static let bottomHeight: CGFloat = 72.0

    private struct BottomView: View {
        
        var body: some View {
            HStack(spacing: 0) {
                LockScreenProgressBottomView(subtitle: subtitleKey.localizedStringFromStorage, subtitleColor: .applicationFormSubtitle) {
                    GeometryReader { proxy in
                        HStack(alignment: .center, spacing: 0) {
                            ThreeStepProgressBarView(
                                firstIcon: .applicationFormInitialActive,
                                secondIcon: .applicationFormWaitingActive,
                                thirdIcon: .applicationFormCompletedLocation,
                                firstStepProgress: 1.0,
                                secondStepProgress: 1.0
                            )
                            .frame(width: .infinity, height: 20)
                        }
                        .frame(height: proxy.size.height)
                    }
                }
                .padding(.trailing, 16)
                PrimaryButtonView(buttonText: ctaKey.localizedStringFromStorage)
            }
        }
    }
    
    // MainView is the UI on lock screen
    struct MainView: View {
        var body: some View {
            VStack(spacing: 0) {
                LockScreenTopView(
                    topImageBackground: .applicationFormTopBackground,
                    title: titleKey.localizedStringFromStorage,
                    titleFontSize: 18,
                    height: topHeight
                ) {
                    EmptyView()
                }
                .frame(height: topHeight)
                
                BottomView()
                    .padding(.bottom, 16)
                    .padding(.top, 12)
                    .padding(.horizontal, 16)
                    .frame(height: bottomHeight)
            }
            .background(.bottomComponent)
        }
    }
    
    struct CompactTrailingView: View {
        var body: some View {
            Image(.applicationFormCompletedActive)
                .resizable()
                .frame(width: 24, height: 24)
        }
    }
    
    struct ExpandLeadingView: View {
        var body: some View {
            Image(.appLogo)
                    .resizable()
                    .scaledToFit()
                    .frame(height: 32)
                    .padding(.top, 4)
                    .padding(.leading, 4)
                    .padding(.bottom, 8)
            GeometryReader { proxy in
                Text(titleKey.localizedStringFromStorage)
                    .font(.system(size: 18, weight: .bold, design: .default))
                    .lineLimit(1)
                    .multilineTextAlignment(.leading)
                    .padding(.leading, 4)
                    .minimumScaleFactor(0.9)
                    .frame(width: proxy.size.width, height: 18, alignment: .leading)
            }
            .frame(height: 18)
        }
    }
    
    struct ExpandBottomView: View {
        var body: some View {
            BottomView()
                .frame(height: 44)
                .padding(.horizontal, 6)
                .padding(.bottom, 16)
        }
    }
}
