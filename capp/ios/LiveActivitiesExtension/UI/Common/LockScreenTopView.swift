//
//  LockScreenTopView.swift
//  LiveActivitiesExtension
//
//  Created by <PERSON><PERSON> (VN) on 1/11/24.
//

import SwiftUI

@available(iOS 16.2, *)
struct LockScreenTopView<BannerImageContent: View>: View {
    let topImageBackground: ImageResource
    let height: CGFloat
    let title: String
    let titleFontSize: CGFloat
    let bannerSize: CGFloat
    let lineLimit: Int
    let bannerView: BannerImageContent
    let textHeight: CGFloat
    init(
        topImageBackground: ImageResource = .topBackground,
        title: String,
        titleFontSize: CGFloat = 16.0,
        height: CGFloat,
        bannerSize: CGFloat = 0,
        lineLimit: Int = 1,
        textHeight: CGFloat = 24.0,
        @ViewBuilder bannerView: () -> BannerImageContent) {
        self.topImageBackground = topImageBackground
        self.height = height
        self.title = title
        self.titleFontSize = titleFontSize
        self.bannerView = bannerView()
        self.bannerSize = bannerSize
        self.lineLimit = lineLimit
        self.textHeight = textHeight
    }
    
    var body: some View {
        ZStack {
            Image(topImageBackground)
                .resizable()
                .frame(height: height)
            VStack(alignment: .trailing, spacing: 0) {
                Spacer()
                HStack(alignment: .bottom, spacing: 0) {
                    GeometryReader { proxy in
                        Text(title)
                            .font(.system(size: titleFontSize, weight: .bold, design: .default))
                            .lineLimit(lineLimit)
                            .minimumScaleFactor(0.6)
                            .multilineTextAlignment(.leading)
                            .frame(width: proxy.size.width - bannerSize, height: textHeight, alignment: .leading)
                    }
                    .frame(height: textHeight)
                    Spacer()
                }
                .overlay(alignment: .bottomTrailing, content: {
                    bannerView
                })
                .padding(.horizontal, 16)
                .padding(.bottom, 8)
            }
            .overlay(
                alignment: .topLeading,
                content: {
                    Image(.appLogo)
                        .padding(.top, 16)
                        .padding(.leading, 16)
                }
            )
        }
        .background(.background)
    }
}
