//
//  NoFoundSAViewBuilder.swift
//  LiveActivitiesExtension
//
//  Created by <PERSON><PERSON><PERSON> (VN) on 12/06/2025.
//

import SwiftUI

@available(iOS 16.2, *)
struct NoFoundSAViewBuilder {
    private init() {}
    static let titleKey = "capp_live_activities.not_found_sa_title"
    static let subtitleKey = "capp_live_activities.not_found_sa_subtitle"
    static let ctaKey = "capp_live_activities.not_found_sa_cta"
    static let topHeight: CGFloat = 106.0
    static let bottomHeight: CGFloat = 64.0
    
    private struct BottomView: View {
        var body: some View {
            PrimaryButtonView(buttonText: ctaKey.localizedStringFromStorage, isFullWidth: true)
        }
    }
    
    // MainView is the UI on lock screen
    struct MainView: View {
        var body: some View {
            VStack(spacing: 0) {
                LockScreenTopView(title: titleKey.localizedStringFromStorage, titleFontSize: 16,
                    height: topHeight,
                    lineLimit:2,
                    textHeight: 48
                ) {
                    EmptyView()
                }
                .frame(height: topHeight)
                
                BottomView()
                    .padding(.horizontal, 16)
                    .padding(.top, 12)
                    .padding(.bottom, 18)
                    .frame(height: bottomHeight)
            }
            .background(.bottomComponent)
        }
    }
    
    struct CompactTrailingView: View {
        var body: some View {
            Image(.greenCheckmarkCircle)
                .resizable()
                .scaledToFit()
                .frame(height: 24)
        }
    }
    
    struct ExpandLeadingView: View {
        var body: some View {
            DIExpandLeadingView(
                title: titleKey.localizedStringFromStorage,
                customBannerImage: nil,
                placeholderImage: nil
            )
        }
    }
    
    struct ExpandBottomView: View {
        var body: some View {
            BottomView()
                .frame(height: 36)
                .padding(.horizontal, 4)
                .padding(.top, 8)
        }
    }
}

