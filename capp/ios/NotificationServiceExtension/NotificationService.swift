//
//  NotificationService.swift
//  NotificationServiceExtension
//
//  Created by <PERSON> on 05.12.2023.
//

import UserNotifications
import FirebaseMessaging

// Please use VN flags, if you need IN, ID you can add them
// This function is called in cases: terminated, background, foreground
class NotificationService: UNNotificationServiceExtension {
    
    var contentHandler: ((UNNotificationContent) -> Void)?
    var bestAttemptContent: UNMutableNotificationContent?
    
    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        NSLog("Received the notification")
        
        // Track messages ASAP
        // Currently only VN support native reporting for notifications
#if (VN)
        NSLog("Try to report messages delivered status");
        self.reportDeliveryStatus(request, withContentHandler: contentHandler)
#endif
        
#if EXPORT_FCM
        Messaging.serviceExtension()
            .exportDeliveryMetricsToBigQuery(withMessageInfo:request.content.userInfo)
#endif
           
#if SAS
        NSLog("Notification handling under SAS flag")
        self.contentHandler = contentHandler
        self.bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)
       
        downloadNotificationMediaIfNeeded(request,withContentHandler:contentHandler)
#endif
        
    }    
    
    func reportDeliveryStatus(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        
        guard let apiBaseUrl = KeychainHelper.shared.readApiBaseUrl() else {
            NSLog("NotificationServiceExLog apiBaseUrl is nil, return")
            return
        }
        
        // Only messages from MSS are tracked, so MSID is mandatory for tracking itself
        guard let messageServerId = request.content.userInfo["MSID"] as? String, !messageServerId.isEmpty else {
            NSLog("MSID not found. Do not track push notification");
            return
        }
        
        let trackingUrl = "\(apiBaseUrl)common/notifications/v2/track/push-notification-delivered?messageServerId=\(messageServerId)&devicePlatform=ios"
        let apiClient = ApiClient()
        
        apiClient.callAPI(url: trackingUrl) {result in
            switch(result) {
            case .success(_):
                NSLog("NotificationServiceExLog track success apiBaseUrl \(apiBaseUrl) for \(messageServerId)")
                break
            case .failure(_):
                NSLog("NotificationServiceExLog track failed")
                break
            }
        }
        
        
    }
    
    func fcmPopulateNotificationContent() -> Void {
        if let bestAttemptContent = bestAttemptContent, let contentHandler = contentHandler {
            FIRMessagingExtensionHelper().populateNotificationContent(
                bestAttemptContent,
                withContentHandler: contentHandler)
            
        }
    }
    
    func downloadNotificationMediaIfNeeded(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)
        
        guard let notificationData = request.content.userInfo["data"] as? [String: AnyObject] else {
            return
        }
        
        guard let urlStr = notificationData["attachment-url"] as? String else {
            return
        }
        
        guard let fileUrl = URL(string: urlStr) else {
            return
        }
        
        let downloadTask = URLSession.shared.downloadTask(with: fileUrl) { (location, response, error) in
            if let location = location, error == nil {
                let tempDir = NSTemporaryDirectory()
                if let suggestedName = response?.suggestedFilename {
                    let fileName = "file://\(tempDir)\(suggestedName)".replacingOccurrences(of: " ", with: "_")
                    if let tempUrl = URL(string: fileName) {
                        do {
                            if FileManager.default.fileExists(atPath: tempUrl.path) && FileManager.default.isDeletableFile(atPath: tempUrl.path) {
                                try FileManager.default.removeItem(at: tempUrl)
                            }
                            try FileManager.default.moveItem(at: location, to: tempUrl)
                            let attachment = try UNNotificationAttachment(identifier: "ci360content", url: tempUrl, options: nil)
                            self.bestAttemptContent?.attachments = [attachment]
                        } catch {
                            return
                        }
                    }
                }
            }
            if let bestAttemptContent = self.bestAttemptContent {
                FIRMessagingExtensionHelper().populateNotificationContent(
                    bestAttemptContent,
                    withContentHandler: contentHandler)
            }
        }
        downloadTask.resume()
        
    }
    
    
    override func serviceExtensionTimeWillExpire() {
        if let contentHandler = contentHandler, let bestAttemptContent =  bestAttemptContent {
            contentHandler(bestAttemptContent)
        }
    }
    
}
