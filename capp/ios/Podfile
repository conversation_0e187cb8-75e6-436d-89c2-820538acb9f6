source 'https://dl.cloudsmith.io/4tzVdnVcQodJri9l/credolab/proxy-sdk/cocoapods/index.git'
source 'https://cdn.cocoapods.org/'
source 'https://github.com/innovatrics/innovatrics-podspecs'

install! 'cocoapods', :warn_for_unused_master_specs_repo => false
# Uncomment this line to define a global platform for your project
platform :ios, '15.5'

$iOSVersion = '15.5'
$swiftVersion = '5.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

# Read the flavor file
flavor_file_path = File.join(__dir__, '.flavor')
flavor = ''

if File.exist?(flavor_file_path)
  flavor = File.read(flavor_file_path).strip
end

puts "Flavor is #{flavor}"

target 'Runner' do
  use_frameworks!
  use_modular_headers!
  pod 'GoogleUtilities'
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

end

target 'HomeCreditVietnam' do
  use_frameworks!
  use_modular_headers!
  pod 'GoogleUtilities'
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end

post_install do |installer|
  installer.pods_project.build_configurations.each do |config|
    config.build_settings["EXCLUDED_ARCHS[sdk=*]"] = "armv7"
    config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = $iOSVersion
  end

  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    target.build_configurations.each do |config|
      if Gem::Version.new($iOSVersion) > Gem::Version.new(config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'])
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = $iOSVersion

      end
      if Gem::Version.new($swiftVersion) > Gem::Version.new(config.build_settings['SWIFT_VERSION'])
        config.build_settings['SWIFT_VERSION'] = $swiftVersion
      end

      config.build_settings['ENABLE_BITCODE'] = 'NO'
      config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
      config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
      config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"



      # You can remove unused permissions here
      # for more infomation: https://github.com/BaseflowIT/flutter-permission-handler/blob/develop/permission_handler/ios/Classes/PermissionHandlerEnums.h
      # e.g. when you don't need camera permission, just add 'PERMISSION_CAMERA=0'
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',

        ## dart: PermissionGroup.calendar
        'PERMISSION_EVENTS=1',

        ## dart: PermissionGroup.reminders
        'PERMISSION_REMINDERS=0',

        ## dart: PermissionGroup.contacts
        # 'PERMISSION_CONTACTS=0',

        ## dart: PermissionGroup.camera
        'PERMISSION_CAMERA=1',

        ## dart: PermissionGroup.microphone
        'PERMISSION_MICROPHONE=1',

        ## dart: PermissionGroup.speech
        'PERMISSION_SPEECH_RECOGNIZER=0',

        ## dart: PermissionGroup.photos
        'PERMISSION_PHOTOS=1',

        ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
        'PERMISSION_LOCATION=1',

        ## dart: PermissionGroup.notification
        'PERMISSION_NOTIFICATIONS=1',

        ## dart: PermissionGroup.mediaLibrary
        'PERMISSION_MEDIA_LIBRARY=0',

        ## dart: PermissionGroup.sensors
        'PERMISSION_SENSORS=0',

        ## dart: PermissionGroup.bluetooth
        'PERMISSION_BLUETOOTH=0'
    ]
    end

    if target.name == 'kyc_workflow'
      source_files = target.source_build_phase.files
      dummy1 = source_files.find do |file|
          file.file_ref.name == 'Info.plist'
      end
      source_files.delete dummy1
      puts "Deleting source file #{dummy1.inspect} from target #{target.inspect}."

      dummy2 = source_files.find do |file|
        file.file_ref.name == 'Info.plist'
      end
      source_files.delete dummy2
      puts "Deleting source file #{dummy2.inspect} from target #{target.inspect}."

      dummy3 = source_files.find do |file|
        file.file_ref.name == 'Info.plist'
      end
      source_files.delete dummy3
      puts "Deleting source file #{dummy3.inspect} from target #{target.inspect}."
    end

    # Temporary fix for Xcode 16 compatibility, will be removed once a new version of gRPC is released.
    if target.name == 'BoringSSL-GRPC'
      target.source_build_phase.files.each do |file|
        if file.settings && file.settings['COMPILER_FLAGS']
          flags = file.settings['COMPILER_FLAGS'].split
          flags.reject! { |flag| flag == '-GCC_WARN_INHIBIT_ALL_WARNINGS' }
          file.settings['COMPILER_FLAGS'] = flags.join(' ')
        end
      end
    end

    # Bitcode trip, will remove after SDK upgrade
    bitcode_strip_path = `xcrun --find bitcode_strip`.chop!

    def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
      framework_path = File.join(Dir.pwd, framework_relative_path)
      command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
      puts "Stripping bitcode: #{command}"
      system(command)
    end

    framework_paths = [
      "Pods/iface/IFace/iface.xcframework/ios-arm64/iface.framework/iface",
      "Pods/onnx/Onnx/innoonnxruntime.xcframework/ios-arm64/innoonnxruntime.framework/innoonnxruntime",
      "Pods/OpenSSL-Universal/Frameworks/OpenSSL.xcframework/ios-arm64_arm64e_armv7_armv7s/OpenSSL.framework/OpenSSL",
    ]

    framework_paths.each do |framework_relative_path|
      strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    end
  end
end

target 'NotificationServiceExtension' do
  use_frameworks!
  pod 'Firebase/Messaging'
  pod 'GoogleUtilities'
end