PODS:
  - abseil/algorithm (1.********.0):
    - abseil/algorithm/algorithm (= 1.********.0)
    - abseil/algorithm/container (= 1.********.0)
  - abseil/algorithm/algorithm (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/algorithm/container (1.********.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base (1.********.0):
    - abseil/base/atomic_hook (= 1.********.0)
    - abseil/base/base (= 1.********.0)
    - abseil/base/base_internal (= 1.********.0)
    - abseil/base/config (= 1.********.0)
    - abseil/base/core_headers (= 1.********.0)
    - abseil/base/cycleclock_internal (= 1.********.0)
    - abseil/base/dynamic_annotations (= 1.********.0)
    - abseil/base/endian (= 1.********.0)
    - abseil/base/errno_saver (= 1.********.0)
    - abseil/base/fast_type_id (= 1.********.0)
    - abseil/base/log_severity (= 1.********.0)
    - abseil/base/malloc_internal (= 1.********.0)
    - abseil/base/no_destructor (= 1.********.0)
    - abseil/base/nullability (= 1.********.0)
    - abseil/base/poison (= 1.********.0)
    - abseil/base/prefetch (= 1.********.0)
    - abseil/base/pretty_function (= 1.********.0)
    - abseil/base/raw_logging_internal (= 1.********.0)
    - abseil/base/spinlock_wait (= 1.********.0)
    - abseil/base/strerror (= 1.********.0)
    - abseil/base/throw_delegate (= 1.********.0)
  - abseil/base/atomic_hook (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/base (1.********.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/base_internal (1.********.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/config (1.********.0):
    - abseil/xcprivacy
  - abseil/base/core_headers (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/cycleclock_internal (1.********.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/dynamic_annotations (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/endian (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/errno_saver (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/fast_type_id (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/log_severity (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/malloc_internal (1.********.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/base/no_destructor (1.********.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/nullability (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/poison (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/xcprivacy
  - abseil/base/prefetch (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/pretty_function (1.********.0):
    - abseil/xcprivacy
  - abseil/base/raw_logging_internal (1.********.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/xcprivacy
  - abseil/base/spinlock_wait (1.********.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/strerror (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/throw_delegate (1.********.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup_internal (1.********.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/common (1.********.0):
    - abseil/meta/type_traits
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/container/common_policy_traits (1.********.0):
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/compressed_tuple (1.********.0):
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/container_memory (1.********.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/fixed_array (1.********.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_map (1.********.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_container_defaults
    - abseil/container/raw_hash_map
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/flat_hash_set (1.********.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_container_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hash_container_defaults (1.********.0):
    - abseil/base/config
    - abseil/container/hash_function_defaults
    - abseil/xcprivacy
  - abseil/container/hash_function_defaults (1.********.0):
    - abseil/base/config
    - abseil/container/common
    - abseil/hash/hash
    - abseil/meta/type_traits
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/container/hash_policy_traits (1.********.0):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hashtable_debug_hooks (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/container/hashtablez_sampler (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/inlined_vector (1.********.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/inlined_vector_internal (1.********.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/container/layout (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/raw_hash_map (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
    - abseil/xcprivacy
  - abseil/container/raw_hash_set (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/crc/cpu_detect (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/crc32c (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_cord_state (1.********.0):
    - abseil/base/config
    - abseil/base/no_destructor
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/crc_internal (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/non_temporal_arm_intrinsics (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/non_temporal_memcpy (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
    - abseil/xcprivacy
  - abseil/debugging/bounded_utf8_length_sequence (1.********.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/debugging/debugging_internal (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/debugging/decode_rust_punycode (1.********.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/debugging/bounded_utf8_length_sequence
    - abseil/debugging/utf8_for_code_point
    - abseil/xcprivacy
  - abseil/debugging/demangle_internal (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/debugging/demangle_rust
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/debugging/demangle_rust (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/decode_rust_punycode
    - abseil/xcprivacy
  - abseil/debugging/examine_stack (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/xcprivacy
  - abseil/debugging/stacktrace (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/xcprivacy
  - abseil/debugging/symbolize (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/debugging/utf8_for_code_point (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/flags/commandlineflag (1.********.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/commandlineflag_internal (1.********.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/xcprivacy
  - abseil/flags/config (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/flag (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/commandlineflag
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/flag_internal (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/flags/marshalling (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/path_util (1.********.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/private_handle_accessor (1.********.0):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/program_name (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/reflection (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/functional/any_invocable (1.********.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/bind_front (1.********.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/function_ref (1.********.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/hash/city (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/xcprivacy
  - abseil/hash/hash (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/hash/low_level_hash (1.********.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/log/absl_check (1.********.0):
    - abseil/log/internal/check_impl
    - abseil/xcprivacy
  - abseil/log/absl_log (1.********.0):
    - abseil/log/internal/log_impl
    - abseil/xcprivacy
  - abseil/log/absl_vlog_is_on (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/check (1.********.0):
    - abseil/log/internal/check_impl
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/globals (1.********.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/hash/hash
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/append_truncated (1.********.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/check_impl (1.********.0):
    - abseil/base/core_headers
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/check_op (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/nullguard
    - abseil/log/internal/nullstream
    - abseil/log/internal/strip
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/conditions (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/voidify
    - abseil/xcprivacy
  - abseil/log/internal/config (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/fnmatch (1.********.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/format (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/append_truncated
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/globals (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/strings/strings
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/log/internal/log_impl (1.********.0):
    - abseil/log/absl_vlog_is_on
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/log_message (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/examine_stack
    - abseil/log/globals
    - abseil/log/internal/append_truncated
    - abseil/log/internal/format
    - abseil/log/internal/globals
    - abseil/log/internal/log_sink_set
    - abseil/log/internal/nullguard
    - abseil/log/internal/proto
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/log/log_sink_registry
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/log_sink_set (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/cleanup/cleanup
    - abseil/log/globals
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/nullguard (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/nullstream (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/proto (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/strip (1.********.0):
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/log_message
    - abseil/log/internal/nullstream
    - abseil/xcprivacy
  - abseil/log/internal/vlog_config (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/log/internal/fnmatch
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/log/internal/voidify (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/log/log (1.********.0):
    - abseil/log/internal/log_impl
    - abseil/log/vlog_is_on
    - abseil/xcprivacy
  - abseil/log/log_entry (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/config
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/log_sink (1.********.0):
    - abseil/base/config
    - abseil/log/log_entry
    - abseil/xcprivacy
  - abseil/log/log_sink_registry (1.********.0):
    - abseil/base/config
    - abseil/log/internal/log_sink_set
    - abseil/log/log_sink
    - abseil/xcprivacy
  - abseil/log/vlog_is_on (1.********.0):
    - abseil/log/absl_vlog_is_on
    - abseil/xcprivacy
  - abseil/memory (1.********.0):
    - abseil/memory/memory (= 1.********.0)
  - abseil/memory/memory (1.********.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/meta (1.********.0):
    - abseil/meta/type_traits (= 1.********.0)
  - abseil/meta/type_traits (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/bits (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/int128 (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
    - abseil/types/compare
    - abseil/xcprivacy
  - abseil/numeric/representation (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/profiling/exponential_biased (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/profiling/sample_recorder (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/random/bit_gen_ref (1.********.0):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
    - abseil/xcprivacy
  - abseil/random/distributions (1.********.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/random/internal/distribution_caller (1.********.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/random/internal/fast_uniform_bits (1.********.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/fastmath (1.********.0):
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/random/internal/generate_real (1.********.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/iostream_state_saver (1.********.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/nonsecure_base (1.********.0):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/pcg_engine (1.********.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
    - abseil/xcprivacy
  - abseil/random/internal/platform (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/internal/pool_urbg (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/randen (1.********.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
    - abseil/xcprivacy
  - abseil/random/internal/randen_engine (1.********.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes (1.********.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes_impl (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/randen_slow (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/salted_seed_seq (1.********.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/seed_material (1.********.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/traits (1.********.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/uniform_helper (1.********.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/wide_multiply (1.********.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/random (1.********.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
    - abseil/xcprivacy
  - abseil/random/seed_gen_exception (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/seed_sequences (1.********.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/status (1.********.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/statusor (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/charset (1.********.0):
    - abseil/base/core_headers
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/strings/cord (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/compare
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cord_internal (1.********.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_functions (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
    - abseil/xcprivacy
  - abseil/strings/cordz_handle (1.********.0):
    - abseil/base/config
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/strings/cordz_info (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_statistics (1.********.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_scope (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_tracker (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/has_ostream_operator (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/internal (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/strings/str_format (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/str_format_internal (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/string_view (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/xcprivacy
  - abseil/strings/strings (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/synchronization/graphcycles_internal (1.********.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/synchronization/kernel_timeout_internal (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/synchronization/synchronization (1.********.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/time (1.********.0):
    - abseil/time/internal (= 1.********.0)
    - abseil/time/time (= 1.********.0)
  - abseil/time/internal (1.********.0):
    - abseil/time/internal/cctz (= 1.********.0)
  - abseil/time/internal/cctz (1.********.0):
    - abseil/time/internal/cctz/civil_time (= 1.********.0)
    - abseil/time/internal/cctz/time_zone (= 1.********.0)
  - abseil/time/internal/cctz/civil_time (1.********.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/time/internal/cctz/time_zone (1.********.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
    - abseil/xcprivacy
  - abseil/time/time (1.********.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/types (1.********.0):
    - abseil/types/any (= 1.********.0)
    - abseil/types/bad_any_cast (= 1.********.0)
    - abseil/types/bad_any_cast_impl (= 1.********.0)
    - abseil/types/bad_optional_access (= 1.********.0)
    - abseil/types/bad_variant_access (= 1.********.0)
    - abseil/types/compare (= 1.********.0)
    - abseil/types/optional (= 1.********.0)
    - abseil/types/span (= 1.********.0)
    - abseil/types/variant (= 1.********.0)
  - abseil/types/any (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/bad_any_cast (1.********.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
    - abseil/xcprivacy
  - abseil/types/bad_any_cast_impl (1.********.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_optional_access (1.********.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_variant_access (1.********.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/compare (1.********.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/optional (1.********.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/span (1.********.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/variant (1.********.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/utility/utility (1.********.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/xcprivacy (1.********.0)
  - account_manager (0.0.1):
    - Flutter
  - advertising_id (0.0.1):
    - Flutter
  - app_links (0.0.2):
    - Flutter
  - app_settings (5.1.1):
    - Flutter
  - app_tracking_transparency (0.0.1):
    - Flutter
  - appsflyer_sdk (6.16.2):
    - AppsFlyerFramework (= 6.16.2)
    - Flutter
  - AppsFlyerFramework (6.16.2):
    - AppsFlyerFramework/Main (= 6.16.2)
  - AppsFlyerFramework/Main (6.16.2)
  - biometric_signature (6.2.0):
    - Flutter
  - BoringSSL-GRPC (0.0.37):
    - BoringSSL-GRPC/Implementation (= 0.0.37)
    - BoringSSL-GRPC/Interface (= 0.0.37)
  - BoringSSL-GRPC/Implementation (0.0.37):
    - BoringSSL-GRPC/Interface (= 0.0.37)
  - BoringSSL-GRPC/Interface (0.0.37)
  - camera_avfoundation (0.0.1):
    - Flutter
  - capp_plugins (0.0.1):
    - Flutter
  - cloud_firestore (5.6.11):
    - Firebase/Firestore (= 11.15.0)
    - firebase_core
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - CredoAppCalendarEvents (3.2.0)
  - CredoAppCore (3.2.0)
  - CredoAppIovation (3.3.0)
  - CredoAppMedia (3.2.0)
  - credolab (0.0.2):
    - Flutter
  - device_calendar (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - dot-camera (8.7.0)
  - dot-capture (8.7.0)
  - dot-core (8.7.0)
  - dot-document (8.7.0):
    - dot-camera (= 8.7.0)
    - dot-capture (= 8.7.0)
    - dot-core (= 8.7.0)
    - dot-document-commons (= 8.7.0)
    - dot-protobuf (= 1.12.0)
  - dot-document-commons (8.7.0)
  - dot-face-background-uniformity (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-face-commons (8.7.0)
  - dot-face-core (8.7.0):
    - dot-camera (= 8.7.0)
    - dot-capture (= 8.7.0)
    - dot-core (= 8.7.0)
    - dot-face-commons (= 8.7.0)
    - dot-protobuf (= 1.12.0)
    - iface (= 5.1.2)
  - dot-face-detection-balanced (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-face-expression-neutral (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-face-eye-gaze-liveness (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-face-passive-liveness (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-protobuf (1.12.0)
  - DTTJailbreakDetection (0.4.0)
  - file_saver (0.0.1):
    - Flutter
  - Firebase/Analytics (11.15.0):
    - Firebase/Core
  - Firebase/Core (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.15.0)
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Crashlytics (11.15.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.15.0)
  - Firebase/DynamicLinks (11.15.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 11.15.0)
  - Firebase/Firestore (11.15.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.15.0)
  - Firebase/Messaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.15.0)
  - Firebase/Performance (11.15.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 11.15.0)
  - Firebase/RemoteConfig (11.15.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.15.0)
  - firebase_analytics (11.5.2):
    - Firebase/Analytics (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_core (3.15.1):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - firebase_crashlytics (4.3.9):
    - Firebase/Crashlytics (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_dynamic_links (6.1.9):
    - Firebase/DynamicLinks (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.9):
    - Firebase/Messaging (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_performance (0.10.1-9):
    - Firebase/Performance (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.4.7):
    - Firebase/RemoteConfig (= 11.15.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseAnalytics (11.15.0):
    - FirebaseAnalytics/Default (= 11.15.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/Default (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/Default (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseDynamicLinks (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseFirestore (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - FirebaseFirestoreInternal (= 11.15.0)
    - FirebaseSharedSwift (~> 11.0)
  - FirebaseFirestoreInternal (11.15.0):
    - abseil/algorithm (~> 1.********.0)
    - abseil/base (~> 1.********.0)
    - abseil/container/flat_hash_map (~> 1.********.0)
    - abseil/memory (~> 1.********.0)
    - abseil/meta (~> 1.********.0)
    - abseil/strings/strings (~> 1.********.0)
    - abseil/time (~> 1.********.0)
    - abseil/types (~> 1.********.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - "gRPC-C++ (~> 1.69.0)"
    - gRPC-Core (~> 1.69.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebasePerformance (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfig (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (11.15.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.15.0)
  - Flutter (1.0.0)
  - flutter_app_badger (1.3.0):
    - Flutter
  - flutter_custom_tabs_ios (2.0.0):
    - Flutter
  - flutter_document_picker (0.0.1):
    - Flutter
  - flutter_html_to_pdf (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_innovatrics_vn (0.0.1):
    - dot-document (= 8.7.0)
    - dot-face-background-uniformity (= 8.7.0)
    - dot-face-core (= 8.7.0)
    - dot-face-detection-balanced (= 8.7.0)
    - dot-face-expression-neutral (= 8.7.0)
    - dot-face-eye-gaze-liveness (= 8.7.0)
    - dot-face-passive-liveness (= 8.7.0)
    - Flutter
  - flutter_ios_calendar_events (4.2.0):
    - CredoAppCalendarEvents (= 3.2.0)
    - CredoAppCore
    - Flutter
  - flutter_ios_core_proxy (6.3.0):
    - CredoAppCore (= 3.2.0)
    - Flutter
  - flutter_ios_iovation (6.2.0):
    - CredoAppCore
    - CredoAppIovation (= 3.3.0)
    - Flutter
    - FraudForce (= 5.6.0)
  - flutter_ios_media (4.2.0):
    - CredoAppCore
    - CredoAppMedia (= 3.2.0)
    - Flutter
  - flutter_libphonenumber (0.0.1):
    - Flutter
    - PhoneNumberKit/PhoneNumberKitCore (~> 3.3.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_contact_picker (0.0.1):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_pin_encryption (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - flutter_true_call (1.9.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - FraudForce (5.6.0)
  - geolocator_apple (1.2.0):
    - Flutter
  - gma_vault (0.0.1):
    - Flutter
  - Google-Maps-iOS-Utils (6.1.0):
    - GoogleMaps (~> 9.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - google_mlkit_barcode_scanning (0.14.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/BarcodeScanning (~> 7.0.0)
  - google_mlkit_commons (0.11.0):
    - Flutter
    - MLKitVision
  - GoogleAdsOnDeviceConversion (2.1.0):
    - GoogleUtilities/Logger (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Core (11.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Default (11.15.0):
    - GoogleAdsOnDeviceConversion (= 2.1.0)
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleAppMeasurement/IdentitySupport (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/IdentitySupport (11.15.0):
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (9.4.0):
    - GoogleMaps/Maps (= 9.4.0)
  - GoogleMaps/Maps (9.4.0)
  - GoogleMLKit/BarcodeScanning (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities (8.1.0):
    - GoogleUtilities/AppDelegateSwizzler (= 8.1.0)
    - GoogleUtilities/Environment (= 8.1.0)
    - GoogleUtilities/Logger (= 8.1.0)
    - GoogleUtilities/MethodSwizzler (= 8.1.0)
    - GoogleUtilities/Network (= 8.1.0)
    - "GoogleUtilities/NSData+zlib (= 8.1.0)"
    - GoogleUtilities/Privacy (= 8.1.0)
    - GoogleUtilities/Reachability (= 8.1.0)
    - GoogleUtilities/SwizzlerTestHelpers (= 8.1.0)
    - GoogleUtilities/UserDefaults (= 8.1.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (8.1.0):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.69.0)":
    - "gRPC-C++/Implementation (= 1.69.0)"
    - "gRPC-C++/Interface (= 1.69.0)"
  - "gRPC-C++/Implementation (1.69.0)":
    - abseil/algorithm/container (~> 1.********.0)
    - abseil/base/base (~> 1.********.0)
    - abseil/base/config (~> 1.********.0)
    - abseil/base/core_headers (~> 1.********.0)
    - abseil/base/log_severity (~> 1.********.0)
    - abseil/base/no_destructor (~> 1.********.0)
    - abseil/cleanup/cleanup (~> 1.********.0)
    - abseil/container/flat_hash_map (~> 1.********.0)
    - abseil/container/flat_hash_set (~> 1.********.0)
    - abseil/container/inlined_vector (~> 1.********.0)
    - abseil/flags/flag (~> 1.********.0)
    - abseil/flags/marshalling (~> 1.********.0)
    - abseil/functional/any_invocable (~> 1.********.0)
    - abseil/functional/bind_front (~> 1.********.0)
    - abseil/functional/function_ref (~> 1.********.0)
    - abseil/hash/hash (~> 1.********.0)
    - abseil/log/absl_check (~> 1.********.0)
    - abseil/log/absl_log (~> 1.********.0)
    - abseil/log/check (~> 1.********.0)
    - abseil/log/globals (~> 1.********.0)
    - abseil/log/log (~> 1.********.0)
    - abseil/memory/memory (~> 1.********.0)
    - abseil/meta/type_traits (~> 1.********.0)
    - abseil/numeric/bits (~> 1.********.0)
    - abseil/random/bit_gen_ref (~> 1.********.0)
    - abseil/random/distributions (~> 1.********.0)
    - abseil/random/random (~> 1.********.0)
    - abseil/status/status (~> 1.********.0)
    - abseil/status/statusor (~> 1.********.0)
    - abseil/strings/cord (~> 1.********.0)
    - abseil/strings/str_format (~> 1.********.0)
    - abseil/strings/strings (~> 1.********.0)
    - abseil/synchronization/synchronization (~> 1.********.0)
    - abseil/time/time (~> 1.********.0)
    - abseil/types/optional (~> 1.********.0)
    - abseil/types/span (~> 1.********.0)
    - abseil/types/variant (~> 1.********.0)
    - abseil/utility/utility (~> 1.********.0)
    - "gRPC-C++/Interface (= 1.69.0)"
    - "gRPC-C++/Privacy (= 1.69.0)"
    - gRPC-Core (= 1.69.0)
  - "gRPC-C++/Interface (1.69.0)"
  - "gRPC-C++/Privacy (1.69.0)"
  - gRPC-Core (1.69.0):
    - gRPC-Core/Implementation (= 1.69.0)
    - gRPC-Core/Interface (= 1.69.0)
  - gRPC-Core/Implementation (1.69.0):
    - abseil/algorithm/container (~> 1.********.0)
    - abseil/base/base (~> 1.********.0)
    - abseil/base/config (~> 1.********.0)
    - abseil/base/core_headers (~> 1.********.0)
    - abseil/base/log_severity (~> 1.********.0)
    - abseil/base/no_destructor (~> 1.********.0)
    - abseil/cleanup/cleanup (~> 1.********.0)
    - abseil/container/flat_hash_map (~> 1.********.0)
    - abseil/container/flat_hash_set (~> 1.********.0)
    - abseil/container/inlined_vector (~> 1.********.0)
    - abseil/flags/flag (~> 1.********.0)
    - abseil/flags/marshalling (~> 1.********.0)
    - abseil/functional/any_invocable (~> 1.********.0)
    - abseil/functional/bind_front (~> 1.********.0)
    - abseil/functional/function_ref (~> 1.********.0)
    - abseil/hash/hash (~> 1.********.0)
    - abseil/log/check (~> 1.********.0)
    - abseil/log/globals (~> 1.********.0)
    - abseil/log/log (~> 1.********.0)
    - abseil/memory/memory (~> 1.********.0)
    - abseil/meta/type_traits (~> 1.********.0)
    - abseil/numeric/bits (~> 1.********.0)
    - abseil/random/bit_gen_ref (~> 1.********.0)
    - abseil/random/distributions (~> 1.********.0)
    - abseil/random/random (~> 1.********.0)
    - abseil/status/status (~> 1.********.0)
    - abseil/status/statusor (~> 1.********.0)
    - abseil/strings/cord (~> 1.********.0)
    - abseil/strings/str_format (~> 1.********.0)
    - abseil/strings/strings (~> 1.********.0)
    - abseil/synchronization/synchronization (~> 1.********.0)
    - abseil/time/time (~> 1.********.0)
    - abseil/types/optional (~> 1.********.0)
    - abseil/types/span (~> 1.********.0)
    - abseil/types/variant (~> 1.********.0)
    - abseil/utility/utility (~> 1.********.0)
    - BoringSSL-GRPC (= 0.0.37)
    - gRPC-Core/Interface (= 1.69.0)
    - gRPC-Core/Privacy (= 1.69.0)
  - gRPC-Core/Interface (1.69.0)
  - gRPC-Core/Privacy (1.69.0)
  - GTMSessionFetcher/Core (3.5.0)
  - iface (5.1.2):
    - onnx (= 2.1.2)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_review (0.2.0):
    - Flutter
  - installer_checker (0.0.1):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - ios_teamid (0.0.1):
    - Flutter
  - launch_app_store (0.0.3):
    - Flutter
  - leveldb-library (1.22.6)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - maps_launcher (0.0.1):
    - Flutter
  - memory_info (0.0.1):
    - Flutter
  - MLImage (1.0.0-beta6)
  - MLKitBarcodeScanning (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - NFCPassportReader (1.1.6):
    - OpenSSL-Universal (= 1.1.180)
  - onepay_custom_deeplink (0.0.1):
    - Flutter
  - onnx (2.1.2)
  - open_filex (0.0.2):
    - Flutter
  - OpenSSL-Universal (1.1.180)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pdf_combiner (0.0.1):
    - Flutter
  - permission_handler_apple (9.3.0):
    - Flutter
  - PhoneNumberKit/PhoneNumberKitCore (3.3.4)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - restart_app (0.0.1):
    - Flutter
  - rive_common (0.0.1):
    - Flutter
  - safe_device (1.0.0):
    - DTTJailbreakDetection
    - Flutter
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - secure_application (0.0.1):
    - Flutter
  - sensors_plus (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sms_autofill (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (3.0.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - vn_idcard_reader (0.0.1):
    - Flutter
    - NFCPassportReader (= 1.1.6)
  - vnpay (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - workmanager (0.0.1):
    - Flutter
  - zalopay (0.0.1):
    - Flutter
  - zikzak_inappwebview_ios (2.4.8):
    - Flutter
    - OrderedSet (>= 6.0.3)
    - zikzak_inappwebview_ios/Core (= 2.4.8)
  - zikzak_inappwebview_ios/Core (2.4.8):
    - Flutter
    - OrderedSet (>= 6.0.3)

DEPENDENCIES:
  - account_manager (from `.symlinks/plugins/account_manager/ios`)
  - advertising_id (from `.symlinks/plugins/advertising_id/ios`)
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - biometric_signature (from `.symlinks/plugins/biometric_signature/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - capp_plugins (from `.symlinks/plugins/capp_plugins/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - credolab (from `.symlinks/plugins/credolab/ios`)
  - device_calendar (from `.symlinks/plugins/device_calendar/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_saver (from `.symlinks/plugins/file_saver/ios`)
  - Firebase/Messaging
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_performance (from `.symlinks/plugins/firebase_performance/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_app_badger (from `.symlinks/plugins/flutter_app_badger/ios`)
  - flutter_custom_tabs_ios (from `.symlinks/plugins/flutter_custom_tabs_ios/ios`)
  - flutter_document_picker (from `.symlinks/plugins/flutter_document_picker/ios`)
  - flutter_html_to_pdf (from `.symlinks/plugins/flutter_html_to_pdf/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_innovatrics_vn (from `.symlinks/plugins/flutter_innovatrics_vn/ios`)
  - flutter_ios_calendar_events (from `.symlinks/plugins/flutter_ios_calendar_events/ios`)
  - flutter_ios_core_proxy (from `.symlinks/plugins/flutter_ios_core_proxy/ios`)
  - flutter_ios_iovation (from `.symlinks/plugins/flutter_ios_iovation/ios`)
  - flutter_ios_media (from `.symlinks/plugins/flutter_ios_media/ios`)
  - flutter_libphonenumber (from `.symlinks/plugins/flutter_libphonenumber/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_contact_picker (from `.symlinks/plugins/flutter_native_contact_picker/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_pin_encryption (from `.symlinks/plugins/flutter_pin_encryption/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - flutter_true_call (from `.symlinks/plugins/flutter_true_call/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - gma_vault (from `.symlinks/plugins/gma_vault/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_mlkit_barcode_scanning (from `.symlinks/plugins/google_mlkit_barcode_scanning/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - GoogleUtilities
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - installer_checker (from `.symlinks/plugins/installer_checker/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - ios_teamid (from `.symlinks/plugins/ios_teamid/ios`)
  - launch_app_store (from `.symlinks/plugins/launch_app_store/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - maps_launcher (from `.symlinks/plugins/maps_launcher/ios`)
  - memory_info (from `.symlinks/plugins/memory_info/ios`)
  - onepay_custom_deeplink (from `.symlinks/plugins/onepay_custom_deeplink/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pdf_combiner (from `.symlinks/plugins/pdf_combiner/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - restart_app (from `.symlinks/plugins/restart_app/ios`)
  - rive_common (from `.symlinks/plugins/rive_common/ios`)
  - safe_device (from `.symlinks/plugins/safe_device/ios`)
  - secure_application (from `.symlinks/plugins/secure_application/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sms_autofill (from `.symlinks/plugins/sms_autofill/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - vn_idcard_reader (from `.symlinks/plugins/vn_idcard_reader/ios`)
  - vnpay (from `.symlinks/plugins/vnpay/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - workmanager (from `.symlinks/plugins/workmanager/ios`)
  - zalopay (from `.symlinks/plugins/zalopay/ios`)
  - zikzak_inappwebview_ios (from `.symlinks/plugins/zikzak_inappwebview_ios/ios`)

SPEC REPOS:
  https://dl.cloudsmith.io/4tzVdnVcQodJri9l/credolab/proxy-sdk/cocoapods/index.git:
    - CredoAppCalendarEvents
    - CredoAppCore
    - CredoAppIovation
    - CredoAppMedia
    - FraudForce
  https://github.com/innovatrics/innovatrics-podspecs:
    - dot-camera
    - dot-capture
    - dot-core
    - dot-document
    - dot-document-commons
    - dot-face-background-uniformity
    - dot-face-commons
    - dot-face-core
    - dot-face-detection-balanced
    - dot-face-expression-neutral
    - dot-face-eye-gaze-liveness
    - dot-face-passive-liveness
    - dot-protobuf
    - iface
    - onnx
  trunk:
    - abseil
    - AppsFlyerFramework
    - BoringSSL-GRPC
    - DTTJailbreakDetection
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDynamicLinks
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - Google-Maps-iOS-Utils
    - GoogleAdsOnDeviceConversion
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMSessionFetcher
    - leveldb-library
    - libwebp
    - Mantle
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - NFCPassportReader
    - OpenSSL-Universal
    - OrderedSet
    - PhoneNumberKit
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder

EXTERNAL SOURCES:
  account_manager:
    :path: ".symlinks/plugins/account_manager/ios"
  advertising_id:
    :path: ".symlinks/plugins/advertising_id/ios"
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  biometric_signature:
    :path: ".symlinks/plugins/biometric_signature/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  capp_plugins:
    :path: ".symlinks/plugins/capp_plugins/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  credolab:
    :path: ".symlinks/plugins/credolab/ios"
  device_calendar:
    :path: ".symlinks/plugins/device_calendar/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_saver:
    :path: ".symlinks/plugins/file_saver/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_performance:
    :path: ".symlinks/plugins/firebase_performance/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_app_badger:
    :path: ".symlinks/plugins/flutter_app_badger/ios"
  flutter_custom_tabs_ios:
    :path: ".symlinks/plugins/flutter_custom_tabs_ios/ios"
  flutter_document_picker:
    :path: ".symlinks/plugins/flutter_document_picker/ios"
  flutter_html_to_pdf:
    :path: ".symlinks/plugins/flutter_html_to_pdf/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_innovatrics_vn:
    :path: ".symlinks/plugins/flutter_innovatrics_vn/ios"
  flutter_ios_calendar_events:
    :path: ".symlinks/plugins/flutter_ios_calendar_events/ios"
  flutter_ios_core_proxy:
    :path: ".symlinks/plugins/flutter_ios_core_proxy/ios"
  flutter_ios_iovation:
    :path: ".symlinks/plugins/flutter_ios_iovation/ios"
  flutter_ios_media:
    :path: ".symlinks/plugins/flutter_ios_media/ios"
  flutter_libphonenumber:
    :path: ".symlinks/plugins/flutter_libphonenumber/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_contact_picker:
    :path: ".symlinks/plugins/flutter_native_contact_picker/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_pin_encryption:
    :path: ".symlinks/plugins/flutter_pin_encryption/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  flutter_true_call:
    :path: ".symlinks/plugins/flutter_true_call/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  gma_vault:
    :path: ".symlinks/plugins/gma_vault/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_mlkit_barcode_scanning:
    :path: ".symlinks/plugins/google_mlkit_barcode_scanning/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  installer_checker:
    :path: ".symlinks/plugins/installer_checker/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  ios_teamid:
    :path: ".symlinks/plugins/ios_teamid/ios"
  launch_app_store:
    :path: ".symlinks/plugins/launch_app_store/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  maps_launcher:
    :path: ".symlinks/plugins/maps_launcher/ios"
  memory_info:
    :path: ".symlinks/plugins/memory_info/ios"
  onepay_custom_deeplink:
    :path: ".symlinks/plugins/onepay_custom_deeplink/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pdf_combiner:
    :path: ".symlinks/plugins/pdf_combiner/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  restart_app:
    :path: ".symlinks/plugins/restart_app/ios"
  rive_common:
    :path: ".symlinks/plugins/rive_common/ios"
  safe_device:
    :path: ".symlinks/plugins/safe_device/ios"
  secure_application:
    :path: ".symlinks/plugins/secure_application/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sms_autofill:
    :path: ".symlinks/plugins/sms_autofill/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  vn_idcard_reader:
    :path: ".symlinks/plugins/vn_idcard_reader/ios"
  vnpay:
    :path: ".symlinks/plugins/vnpay/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"
  workmanager:
    :path: ".symlinks/plugins/workmanager/ios"
  zalopay:
    :path: ".symlinks/plugins/zalopay/ios"
  zikzak_inappwebview_ios:
    :path: ".symlinks/plugins/zikzak_inappwebview_ios/ios"

SPEC CHECKSUMS:
  abseil: a05cc83bf02079535e17169a73c5be5ba47f714b
  account_manager: 33c26fa1ad8f17d4427531bef8ff6c212453ae43
  advertising_id: d5de9e659986092d7ca50977dc50f4f4fcd4c30a
  app_links: 3da4c36b46cac3bf24eb897f1a6ce80bda109874
  app_settings: 3507c575c2b18a462c99948f61d5de21d4420999
  app_tracking_transparency: 3d84f147f67ca82d3c15355c36b1fa6b66ca7c92
  appsflyer_sdk: ebe94dc3f00b07bdd001251dbc87879bda3c494a
  AppsFlyerFramework: fe5303bffcdfd941d5f570c2d21eaaea982e7bdc
  biometric_signature: 89f8456bee939e7d1646f60785e55c2b4ed193c7
  BoringSSL-GRPC: dded2a44897e45f28f08ae87a55ee4bcd19bc508
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  capp_plugins: bd5b8f6197e2c4cc46ee7605e1fbda1b3bad9aca
  cloud_firestore: 8b8ad857da9a98527dab9c7ae520d0eba975bef9
  connectivity_plus: b21496ab28d1324eb59885d888a4d83b98531f01
  CredoAppCalendarEvents: fd1d5a689fa724b106c3a3813ea81695d3000ec3
  CredoAppCore: 32a500698736ac4441befb38e8a899d42a0a5b40
  CredoAppIovation: fd12419b51f09cb23d64c6c985dbad4bf9c202f1
  CredoAppMedia: f52b12ef4d08f8f4850716f6d826feb8aa184266
  credolab: 6f8024252ec8e2cfe6cfaeb940f62b2c2c5f1b7e
  device_calendar: b55b2c5406cfba45c95a59f9059156daee1f74ed
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  dot-camera: 0f642adc3830ef9e972e6b1a17b0cc3d2657d396
  dot-capture: 76accabe96bfb704a752b93e26003a5ddfd13040
  dot-core: 6f5eacf2512010edbd58c6121d6406fc41eaa6b3
  dot-document: dc40ee7b7431646c19c98521a999d7c3c03ed33e
  dot-document-commons: fd6a31907c3923d38c4e9fb7fc76226e27770347
  dot-face-background-uniformity: 25ea7db29b04b42869c1f57384a22d4cf1d4cfb4
  dot-face-commons: 8c06dd50e04502dda7ba3c9ad0d91d4dbd9750db
  dot-face-core: 680c68cd51c411d1c0a2e9989fb5d87bece8e24f
  dot-face-detection-balanced: 09f42b276c80b5f55c5cc5e22a0fbbd658fe3fcd
  dot-face-expression-neutral: f955b930aadad4902369cc2afe9cab7506aa56c0
  dot-face-eye-gaze-liveness: c5adc76430d4156ba288c041f2a4957a435c1eb6
  dot-face-passive-liveness: ba884b4898924b8e74b9528a31ad96458820f093
  dot-protobuf: c801e41197bcb48b2ee0dd5ef2a16e4fff9cc28e
  DTTJailbreakDetection: 5e356c5badc17995f65a83ed9483f787a0057b71
  file_saver: 6cdbcddd690cb02b0c1a0c225b37cd805c2bf8b6
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_analytics: 740475b8bf6f9a79b04c2d55fe226a60679fe593
  firebase_core: ece862f94b2bc72ee0edbeec7ab5c7cb09fe1ab5
  firebase_crashlytics: f89f5137e667ba1733ca5d7ab1b7af3390572520
  firebase_dynamic_links: 8b20b981da6004ff256be5ad53189be5fe551e17
  firebase_messaging: e1a5fae495603115be1d0183bc849da748734e2b
  firebase_performance: 561f917ed2cdfb1685a8c58322500f567c1400a6
  firebase_remote_config: 88ede24650cdf0916188dc2fe4138e3a03e62712
  FirebaseABTesting: 5e9d432834aebf27ab72100d37af44dfbe8d82f7
  FirebaseAnalytics: 6433dfd311ba78084fc93bdfc145e8cb75740eae
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreExtension: edbd30474b5ccf04e5f001470bdf6ea616af2435
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseCrashlytics: e09d0bc19aa54a51e45b8039c836ef73f32c039a
  FirebaseDynamicLinks: 639fc743dc9c9f01709139bf74319536a674f012
  FirebaseFirestore: 1e5fafdac2b2ef1ffc24034460b7b4821a15be96
  FirebaseFirestoreInternal: df9ab608a59a4e8eefd0796ed7652f3c1a88473a
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseMessaging: 3b26e2cee503815e01c3701236b020aa9b576f09
  FirebasePerformance: 8710877e6ba0f6ca4940d10624f5302431b406ea
  FirebaseRemoteConfig: b496646b82855e174a7f1e354c65e0e913085168
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: b9a92c1c51bbb81e78fc3142cda6d925d700f8e7
  FirebaseSharedSwift: e17c654ef1f1a616b0b33054e663ad1035c8fd40
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_app_badger: 2aa8087dbc93638dafeddaa5816532da229dfbaf
  flutter_custom_tabs_ios: dd647919edd75e82ba6b00009eb3460a28c011b8
  flutter_document_picker: 072c0cb92c910c470057b73c42813e5c7cf0e139
  flutter_html_to_pdf: e708342b16093c331bcef31b4dc0a469b9dfb1d6
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_innovatrics_vn: b65126237d21c9e5b68a37e26a81cbc29945d91d
  flutter_ios_calendar_events: d0066a45224cd67592db98fd9b2b8cfa600c6398
  flutter_ios_core_proxy: c1e45d6bf8b53ef068f68317d11cc3b2c1a054fb
  flutter_ios_iovation: 46e8e192b004e0e56fad92d23dbda9e687c000a4
  flutter_ios_media: 3dc002fb744145c9bdd1174be008a5427243dbd9
  flutter_libphonenumber: 66fe748999e52dbcfecdb3f5b8e06bc553889f97
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_native_contact_picker: ebff97ae8c8110e9de4590d6e363f974dca52a8c
  flutter_pdfview: 54e283d5851b0b247b3cc57877d35f1a05a204de
  flutter_pin_encryption: b666e4c82b9ee2aee6504e6bc5b85f3e9b71bf83
  flutter_secure_storage: 2c2ff13db9e0a5647389bff88b0ecac56e3f3418
  flutter_timezone: 7c838e17ffd4645d261e87037e5bebf6d38fe544
  flutter_true_call: fb0b259cdcb211c4cc80354e7e24cf05449f074b
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  FraudForce: 12b9b24ad2782332654a70da67f25c08c1720433
  geolocator_apple: d981750b9f47dbdb02427e1476d9a04397beb8d9
  gma_vault: ea43a24e9b9c71f97f63de61e02ddabf9a4e2c6e
  Google-Maps-iOS-Utils: 0a484b05ed21d88c9f9ebbacb007956edd508a96
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  google_mlkit_barcode_scanning: 8f5987f244a43fe1167689c548342a5174108159
  google_mlkit_commons: 2abe6a70e1824e431d16a51085cb475b672c8aab
  GoogleAdsOnDeviceConversion: 2be6297a4f048459e0ae17fad9bfd2844e10cf64
  GoogleAppMeasurement: 700dce7541804bec33db590a5c496b663fbe2539
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 0608099d4870cac8754bdba9b6953db543432438
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  "gRPC-C++": cc207623316fb041a7a3e774c252cf68a058b9e8
  gRPC-Core: 860978b7db482de8b4f5e10677216309b5ff6330
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  iface: 4767a90a11587115add991240183e96223848750
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  in_app_review: 8efcf4a4d3ba72d5d776d29e5a268f1abf64d184
  installer_checker: a08292af9d4aca844bc1cd81bfd667719ed80f4d
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  ios_teamid: 7b629d9be76edb487dfde980a12b86da55a1c13f
  launch_app_store: 4c7a9002f31fa353f9a96e7edc7bb80608448319
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  local_auth_darwin: d2e8c53ef0c4f43c646462e3415432c4dab3ae19
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  maps_launcher: edf829809ba9e894d70e569bab11c16352dedb45
  memory_info: 0c8ecafff5f646e2957972aee37801131affa512
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitBarcodeScanning: 0a3064da0a7f49ac24ceb3cb46a5bc67496facd2
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  NFCPassportReader: 98329180c6f7199ca52f15a81bd3344588e2b1bc
  onepay_custom_deeplink: 8bc8c5c5ab37dc95fcf2a3017d1bbcf11837e19d
  onnx: ff81ed3e7860e32c3e6addda60834c3ea4d955cb
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  pdf_combiner: d4df9f743bb6f639bfa83d57740a665873f66125
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PhoneNumberKit: 441e8b26ec88d598e3591de9061eff18f5dd12e8
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  restart_app: 9cda5378aacc5000e3f66ee76a9201534e7d3ecf
  rive_common: dd421daaf9ae69f0125aa761dd96abd278399952
  safe_device: 205ad04de30f4512ca889418d4783e1b6fdf8cfe
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  secure_application: 1b94e9be54ff82c3a2598e75e4ba559d5e14e2d3
  sensors_plus: 6a11ed0c2e1d0bd0b20b4029d3bad27d96e0c65b
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sms_autofill: b36b2147535657fea83d7f3898d7831de70bd8e4
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  vibration: 69774ad57825b11c951ee4c46155f455d7a592ce
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  vn_idcard_reader: d8cd64e5bcc85ca8183e17d6a8e6d1f8fb7e5686
  vnpay: e90f48746db8050b8ae0d71b7198a44a7a650a3f
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  workmanager: 62efda17a4f5acc91b92cb6ede0f03abaf019a71
  zalopay: 8e7b687438774d3cc9b310d49faf4ad01bd59690
  zikzak_inappwebview_ios: 4fb48497764b6363cfeffad6df0b07be59bc96d1

PODFILE CHECKSUM: d99d903bd0819449fec4005fb8bd4847a5b53d87

COCOAPODS: 1.16.2
