import UIKit
import Firebase
import FirebaseMessaging
import Flutter
import GoogleMaps
import DotFaceCore
import DotDocument
import A<PERSON>FlyerLib
import ThreatMark

@main
@objc class AppDelegate: FlutterAppDelegate {

  private var navigationController: UINavigationController!
  var methodChannel: FlutterMethodChannel!

  let TAG_BLUR_VIEW = 12345
  var blurView:UIVisualEffectView?
  
  private func setupDebugFirebaseAnalyticsIfNeeded() {
      let key = "flutter.is_debug_ios_firebase_analytics_enabled"
      if UserDefaults.standard.object(forKey: key) != nil {
          let isEnableDebugAnalytics = UserDefaults.standard.bool(forKey: key)
          if isEnableDebugAnalytics {
              CommandLine.arguments.append(contentsOf: ["-FIRDebugEnabled", "-FIRAnalyticsDebugEnabled"])
          }
          UserDefaults.standard.set(isEnableDebugAnalytics, forKey: "/google/measurement/debug_mode")
          UserDefaults.standard.set(isEnableDebugAnalytics, forKey: "/google/firebase/debug_mode")
      }
  }

  // MARK: - ThreatMark SDK Integration
  private func initThreatMarkSDK() {
      print("ThreatMark SDK initialization started")

      do {
          let keyPath = Bundle.main.path(forResource: "tmSdkKey", ofType: "pem")
          guard let keyPath = keyPath else {
              print("ThreatMark: tmSdkKey.pem file not found")
              return
          }

          let config = ThreatMarkConfiguration()
          config.url = "https://ios.c470.threatmark.com/application/gma-ios/ios"
          config.keyFilePath = keyPath
          config.loggingEnabled = true

          ThreatMark.start(with: config) { [weak self] cookies in
              self?.sendThreatMarkCookiesToBackend(cookies: cookies)
          }

          print("ThreatMark SDK started successfully")
      } catch {
          print("ThreatMark SDK initialization failed: \(error)")
      }
  }

  private func sendThreatMarkCookiesToBackend(cookies: [String: String]) {
      print("ThreatMark cookies received: \(cookies)")
      // TODO: Send cookies to backend
  }

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    setupDebugFirebaseAnalyticsIfNeeded()
    let controller = window?.rootViewController as! FlutterViewController
    GMSServices.provideAPIKey("AIzaSyC9ZncCWVVJBEav6zQDPeFY4HeMise0hC0")

    GeneratedPluginRegistrant.register(with: self)
    #if LIVE_ACTIVITY
    LiveActivitiesPlugin.register(with: self.registrar(forPlugin: "live_activities")!)
    #endif
    methodChannel = FlutterMethodChannel(name: "sas_collector", binaryMessenger: controller.binaryMessenger)
      
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
    }
      
    DotFaceCore.Localization.bundle = .main
    DotDocument.Localization.bundle = .main
    
    // create and then add a new UINavigationController
    navigationController = UINavigationController(rootViewController: controller)
    self.window.rootViewController = self.navigationController
    navigationController.setNavigationBarHidden(true, animated: false)
    self.window.makeKeyAndVisible()

    // Initialize ThreatMark SDK
    initThreatMarkSDK()

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
    
    
    override func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        #if EXPORT_FCM
                Messaging.serviceExtension().exportDeliveryMetricsToBigQuery(withMessageInfo:userInfo)
        #endif

        super.application(application, didReceiveRemoteNotification: userInfo, fetchCompletionHandler: completionHandler)
    }

    override func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {

        completionHandler()
        super.userNotificationCenter(center, didReceive: response, withCompletionHandler: completionHandler)
    }

  override func applicationWillResignActive(_ application: UIApplication) {
    if let view = self.window.rootViewController?.view.subviews.first(where: {$0.tag == TAG_BLUR_VIEW}){
        view.removeFromSuperview()
        blurView = nil
    }
    if blurView == nil{
        blurView = UIVisualEffectView(frame: UIScreen.main.bounds)
        blurView?.effect = UIBlurEffect(style: .light)
        blurView?.tag = TAG_BLUR_VIEW
    }
    self.window.rootViewController?.view.insertSubview(blurView!, at: 0)
  }

  override func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
      AppsFlyerLib.shared().registerUninstall(deviceToken)
  }

  override func applicationDidBecomeActive(_ application: UIApplication) {
    if let view = self.window.rootViewController?.view.subviews.first(where: {$0.tag == TAG_BLUR_VIEW}){
        view.removeFromSuperview()
        blurView = nil
    }
  }
}
