import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../integration_page_objects/index.dart';
import '../../integration_page_objects/loan_origination/id_card_instruction_page.dart';
import '../../integration_page_objects/loan_origination/selfie_introduction_page.dart';
import '../../integration_tests_tools/tester_helper.dart';
import 'common/auth_common_steps.dart';

const phoneNumber = '**********';
const password = '166437';

Future<void> authenticationOnboardingV4NoVerificationTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final homePage = HomeTestPage(tester);
  final accountPage = AccountTestPage(tester);
  final pinPage = PinPage(tester);
  final mainPage = MainTestPage(tester);

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // continue as guest
  await authenticationInitialPage.continueAsGuestButton.tap(settleAfterPump: false);
  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  authenticationInitialPage.isAbsent();

  // back to home page
  await tester.pumpAndSettle(const Duration(seconds: 1));
  homePage.isReady();

  // open account tab
  await mainPage.bottomBarProfileButton.tap();
  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  accountPage.isReady();

  // open authentication screen
  await accountPage.scrollToLogintRow();
  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  await accountPage.loginMenuItem.tap();
  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('**********');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter PIN
  pinPage.isReady();
  await pinPage.enterPin(isFourDigitPinEnabled ? '1664' : '166437');

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4VerificationUserFoundInIdsTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final pinPage = PinPage(tester);

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('**********');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // enter PIN
  pinPage.isReady();
  await pinPage.enterPin(isFourDigitPinEnabled ? '1664' : '166437');

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4VerificationUserFoundInIdsEscapeTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final setPinPage = SetPasswordPage(tester);

  testerHelper
    ..toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true)
    ..changeAuthenticationOtpResponse(
      const AuthenticationOtpResponse(
        allowed2ndIds: [UserSecondIdType.loanAccount],
        allowedAlternativeSteps: AllowedAlternativeSteps(escape: true, enforceIdentify: false),
      ),
    );

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('**********');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.escapeButton.tap();

  // set up PIN
  setPinPage.isReady();
  await setPinPage.enterPasscode(isFourDigitPinEnabled ? '1664' : '166437', settleAfterPump: true);
  await setPinPage.enterPasscode(isFourDigitPinEnabled ? '1664' : '166437');
  await testerHelper.pumpHome();

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4VerificationUserNotFoundInIdsTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final pinPage = PinPage(tester);

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('**********');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // enter PIN
  pinPage.isReady();
  await pinPage.enterPin(isFourDigitPinEnabled ? '1664' : '166437');

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4VerificationUserNotFoundInIdsBosApprovedCuidInIdsTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
  bool skipAuthenticateVerificationPage = false,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final authenticateVerificationPage = AuthenticateVerificationPage(tester);
  final pinPage = PinPage(tester);

  testerHelper
    ..toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true)
    ..changeAuthenticationVerify2ndIdResponse(
      const AuthenticationSecondIdVerificationResponse(),
    )
    ..changeAuthenticationAuthenticateResultResponse(
      AuthenticationAuthenticateResultResponse(
        loginSessionId: '5671',
        passwordLength: isFourDigitPinEnabled ? 4 : 6,
        bosResult: AuthenticateBosResult.approved,
        allowed2ndIds: null,
        allowedNextSteps: null,
        callAgainInterval: null,
        changePasswordOptions: null,
      ),
    );

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('**********');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // second identifier verification
  if (!skipAuthenticateVerificationPage) {
    authenticateVerificationPage.isReady();
    await testerHelper.pumpHome();
  } else {
    await testerHelper.tester.pumpAndSettle(const Duration(seconds: 4));
  }

  // enter PIN
  pinPage.isReady();
  await pinPage.enterPin(isFourDigitPinEnabled ? '1664' : '166437');

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4VerificationUserNotFoundInIdsBosApprovedNoCuidInIdsTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
  bool skipAuthenticateVerificationPage = false,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final authenticateVerificationPage = AuthenticateVerificationPage(tester);
  final setPinPage = SetPasswordPage(tester);

  testerHelper
    ..toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true)
    ..changeAuthenticationVerify2ndIdResponse(
      const AuthenticationSecondIdVerificationResponse(),
    )
    ..changeAuthenticationAuthenticateResultResponse(
      AuthenticationAuthenticateResultResponse(
        loginSessionId: null,
        passwordLength: isFourDigitPinEnabled ? 4 : 6,
        bosResult: AuthenticateBosResult.approved,
        allowed2ndIds: null,
        allowedNextSteps: null,
        callAgainInterval: null,
        changePasswordOptions: null,
      ),
    );

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('**********');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // second identifier verification
  if (!skipAuthenticateVerificationPage) {
    authenticateVerificationPage.isReady();
    await testerHelper.pumpHome();
  } else {
    await testerHelper.tester.pumpAndSettle(const Duration(seconds: 4));
  }

  // set up PIN
  setPinPage.isReady();
  await setPinPage.enterPasscode(isFourDigitPinEnabled ? '1664' : '166437', settleAfterPump: true);
  await setPinPage.enterPasscode(isFourDigitPinEnabled ? '1664' : '166437');
  await testerHelper.pumpHome();

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4VerificationUserNotFoundInIdsBosNotApprovedTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
  bool skipAuthenticateVerificationPage = false,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final authenticateVerificationPage = AuthenticateVerificationPage(tester);
  final secondIdentifierCrossroadsPage = SecondIdentfierCrossroadsPage(tester);

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('9778889998');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // second identifier verification
  if (!skipAuthenticateVerificationPage) {
    authenticateVerificationPage.isReady();
    await testerHelper.pumpHome();

    // second identifier crossroads
    secondIdentifierCrossroadsPage.isReady();
  } else {
    await testerHelper.tester.pumpAndSettle(const Duration(seconds: 4));
    secondIdentifierPage.isReady();
  }
}

Future<void> authenticationOnboardingV4VerificationUserNotFoundInIdsBosFailedTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
  bool skipAuthenticateVerificationPage = false,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final authenticateVerificationPage = AuthenticateVerificationPage(tester);
  final pinPage = PinPage(tester);
  final homePage = HomeTestPage(tester);

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('9778889997');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // second identifier verification
  if (!skipAuthenticateVerificationPage) {
    authenticateVerificationPage.isReady();
    await testerHelper.pumpHome();
    authenticateVerificationPage.errorDialog.isReady();
    await authenticateVerificationPage.errorDialogOkButton.tap(settleAfterPump: false);
    await testerHelper.pumpHome();
  } else {
    await testerHelper.tester.pumpAndSettle(const Duration(seconds: 4));
    secondIdentifierPage.isReady();
  }

  testerHelper.changeAuthenticationAuthenticateResultResponse(
    AuthenticationAuthenticateResultResponse(
      loginSessionId: '5671',
      passwordLength: isFourDigitPinEnabled ? 4 : 6,
      bosResult: AuthenticateBosResult.approved,
      allowed2ndIds: null,
      allowedNextSteps: AuthenticateAllowedNextSteps(
        retry: true,
        escape: false,
        identify: true,
        identifyEscape: false,
      ),
      callAgainInterval: null,
      changePasswordOptions: null,
    ),
  );

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  if (!skipAuthenticateVerificationPage) {
    // second identifier verification
    authenticateVerificationPage.isReady();
    await testerHelper.pumpHome();
    await testerHelper.tester.pumpAndSettle();
  } else {
    await testerHelper.tester.pumpAndSettle(const Duration(seconds: 4));
  }

  // enter PIN
  pinPage.isReady();
  await pinPage.enterPin(isFourDigitPinEnabled ? '1664' : '166437');

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4VerificationUserNotFoundInIdsBosNotApprovedEscapeTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final authenticateVerificationPage = AuthenticateVerificationPage(tester);
  final secondIdentifierCrossroadsPage = SecondIdentfierCrossroadsPage(tester);
  final setPinPage = SetPasswordPage(tester);
  final homePage = HomeTestPage(tester);

  testerHelper
    ..toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true)
    ..changeAuthenticationAuthenticateResultResponse(
      AuthenticationAuthenticateResultResponse(
        loginSessionId: null,
        passwordLength: isFourDigitPinEnabled ? 4 : 6,
        bosResult: AuthenticateBosResult.notApproved,
        allowed2ndIds: null,
        allowedNextSteps: AuthenticateAllowedNextSteps(
          retry: true,
          escape: true,
          identify: false,
          identifyEscape: false,
        ),
        callAgainInterval: null,
        changePasswordOptions: null,
      ),
    );

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('9778889998');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // second identifier verification
  authenticateVerificationPage.isReady();
  await testerHelper.pumpHome();

  // second identifier crossroads
  secondIdentifierCrossroadsPage.isReady();
  await secondIdentifierCrossroadsPage.escapeButton.tap();

  // set up PIN
  setPinPage.isReady();
  await setPinPage.enterPasscode(isFourDigitPinEnabled ? '1664' : '166437', settleAfterPump: true);
  await setPinPage.enterPasscode(isFourDigitPinEnabled ? '1664' : '166437');
  await testerHelper.pumpHome();

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4AuthenticateFailedIdentifyApprovedUserFoundInIds(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final authenticateVerificationPage = AuthenticateVerificationPage(tester);
  final secondIdentifierCrossroadsPage = SecondIdentfierCrossroadsPage(tester);
  final identifyVerificationPage = IdentifyVerificationPage(tester);
  final selfieIntroductionPage = LoanSelfieIntroductionPage(tester);
  final idCardInstructionPage = LoanIdCardInstructionPage(tester);
  final pinPage = PinPage(tester);
  final homePage = HomeTestPage(tester);

  testerHelper
    ..toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true)
    ..setMockInnovatricsBloc()
    ..changeAuthenticationIdentifyResultResponse(
      const AuthenticationIdentifyResultResponse(
        loginSessionId: '5671',
        bosResult: IdentifyBosResult.approved,
      ),
    );

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('9778889998');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // authenticate verification
  authenticateVerificationPage.isReady();
  await testerHelper.pumpHome();

  // second identifier crossroads
  secondIdentifierCrossroadsPage.isReady();
  // select identify
  await secondIdentifierCrossroadsPage.selectableOptionIdentify.tap(settleAfterPump: false);
  await secondIdentifierCrossroadsPage.confirmButton.tap(settleAfterPump: false);

  // scan selfie
  selfieIntroductionPage.isReady();
  await selfieIntroductionPage.tapContinueButton();
  await tester.pump(const Duration(seconds: 4));

  // scan id card
  idCardInstructionPage.isReady();
  await idCardInstructionPage.tapContinueButton(settleAfterPump: false);
  await tester.pump(const Duration(seconds: 1));

  // simulate id card success scan
  //await testerHelper.simulateIdCardScreenResult();

  // identify verification
  identifyVerificationPage.isReady();
  await testerHelper.pumpHome();

  // enter PIN
  pinPage.isReady();
  await pinPage.enterPin(isFourDigitPinEnabled ? '1664' : '166437');

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4AuthenticateFailedIdentifyApprovedUserNotFoundInIds(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final authenticateVerificationPage = AuthenticateVerificationPage(tester);
  final secondIdentifierCrossroadsPage = SecondIdentfierCrossroadsPage(tester);
  final identifyVerificationPage = IdentifyVerificationPage(tester);
  final selfieIntroductionPage = LoanSelfieIntroductionPage(tester);
  final idCardInstructionPage = LoanIdCardInstructionPage(tester);
  final setPinPage = SetPasswordPage(tester);
  final homePage = HomeTestPage(tester);

  testerHelper
    ..toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true)
    ..setMockInnovatricsBloc()
    ..changeAuthenticationIdentifyResultResponse(
      const AuthenticationIdentifyResultResponse(
        bosResult: IdentifyBosResult.approved,
      ),
    );

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('9778889998');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // authenticate verification
  authenticateVerificationPage.isReady();
  await testerHelper.pumpHome();

  // second identifier crossroads
  secondIdentifierCrossroadsPage.isReady();
  // select identify
  await secondIdentifierCrossroadsPage.selectableOptionIdentify.tap(settleAfterPump: false);
  await secondIdentifierCrossroadsPage.confirmButton.tap(settleAfterPump: false);

  selfieIntroductionPage.isReady();
  await selfieIntroductionPage.tapContinueButton();
  await tester.pump(const Duration(seconds: 4));

  // scan id card
  idCardInstructionPage.isReady();
  await idCardInstructionPage.tapContinueButton(settleAfterPump: false);
  await tester.pump(const Duration(seconds: 1));

  // simulate id card success scan
  // await testerHelper.simulateIdCardScreenResult();

  // identify verification
  identifyVerificationPage.isReady();
  await testerHelper.pumpHome();

  // set up PIN
  setPinPage.isReady();
  await setPinPage.enterPasscode(isFourDigitPinEnabled ? '1664' : '166437', settleAfterPump: true);
  await setPinPage.enterPasscode(isFourDigitPinEnabled ? '1664' : '166437');
  await testerHelper.pumpHome();

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4AuthenticateFailedIdentifyError(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final authenticateVerificationPage = AuthenticateVerificationPage(tester);
  final secondIdentifierCrossroadsPage = SecondIdentfierCrossroadsPage(tester);
  final identifyVerificationPage = IdentifyVerificationPage(tester);
  final selfieIntroductionPage = LoanSelfieIntroductionPage(tester);
  final idCardInstructionPage = LoanIdCardInstructionPage(tester);
  final identifyErrorPage = IdentifyErrorPage(tester);
  final homePage = HomeTestPage(tester);

  testerHelper
    ..toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true)
    ..setMockInnovatricsBloc();

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('9778889998');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // authenticate verification
  authenticateVerificationPage.isReady();
  await testerHelper.pumpHome();

  // second identifier crossroads
  secondIdentifierCrossroadsPage.isReady();
  // select identify
  await secondIdentifierCrossroadsPage.selectableOptionIdentify.tap(settleAfterPump: false);
  await secondIdentifierCrossroadsPage.confirmButton.tap(settleAfterPump: false);

  // scan selfie
  selfieIntroductionPage.isReady();
  await selfieIntroductionPage.tapContinueButton();
  await tester.pump(const Duration(seconds: 4));

  // scan id card
  idCardInstructionPage.isReady();
  await idCardInstructionPage.tapContinueButton(settleAfterPump: false);
  await tester.pump(const Duration(seconds: 1));

  // simulate id card success scan
//  await testerHelper.simulateIdCardScreenResult();

  // identify verification
  identifyVerificationPage.isReady();
  await testerHelper.pumpHome();

  // identify error
  identifyErrorPage.isReady();
  await identifyErrorPage.continueButton.tap();

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> authenticationOnboardingV4AuthenticateFailedIdentifyManualDeduplication(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authenticationInitialPage = AuthenticationInitialPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdentifierPage = SecondIdentifierPage(tester);
  final authenticateVerificationPage = AuthenticateVerificationPage(tester);
  final secondIdentifierCrossroadsPage = SecondIdentfierCrossroadsPage(tester);
  final identifyVerificationPage = IdentifyVerificationPage(tester);
  final selfieIntroductionPage = LoanSelfieIntroductionPage(tester);
  final idCardInstructionPage = LoanIdCardInstructionPage(tester);
  final identifyManualDeduplicationPage = IdentifyManualDeduplicationPage(tester);
  final homePage = HomeTestPage(tester);

  testerHelper
    ..toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true)
    ..setMockInnovatricsBloc()
    ..changeAuthenticationIdentifyResultResponse(
      const AuthenticationIdentifyResultResponse(),
    );

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authenticationInitialPage.isReady();

  // enter phone number
  await authenticationInitialPage.phoneNumberInputField.enterText('9778889998');
  await authenticationInitialPage.confirmPhoneNumberButton.tap();

  // enter otp
  otpPage.isReady();
  await otpPage.enterOtp('987789', settleAfterPump: true);

  // enter second identifier
  secondIdentifierPage.isReady();
  await secondIdentifierPage.lanInput.enterText('1234567891234567');
  await secondIdentifierPage.confirmButton.tap(settleAfterPump: false);

  // authenticate verification
  authenticateVerificationPage.isReady();
  await testerHelper.pumpHome();

  // second identifier crossroads
  secondIdentifierCrossroadsPage.isReady();
  // select identify
  await secondIdentifierCrossroadsPage.selectableOptionIdentify.tap(settleAfterPump: false);
  await secondIdentifierCrossroadsPage.confirmButton.tap(settleAfterPump: false);

  // scan selfie
  selfieIntroductionPage.isReady();
  await selfieIntroductionPage.tapContinueButton();
  await tester.pump(const Duration(seconds: 4));

  // scan id card
  idCardInstructionPage.isReady();
  await idCardInstructionPage.tapContinueButton(settleAfterPump: false);
  await tester.pump(const Duration(seconds: 1));

  // simulate id card success scan
  //await testerHelper.simulateIdCardScreenResult();

  // identify verification
  identifyVerificationPage.isReady();
  await testerHelper.pumpHome();

  // identify manual deduplication
  identifyManualDeduplicationPage.isReady();
  await identifyManualDeduplicationPage.continueButton.tap();

  await tester.pumpAndSettle(const Duration(milliseconds: 200));
  homePage.isReady();
}

Future<void> logoutOnboardingV4OnNotificationTest(
  WidgetTester tester,
  TestContext context, {
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthenticationInitialPage(tester);
  final homePage = HomeTestPage(tester);
  final logoutAnotherDeviceDialog = LogoutAnotherDeviceDialog(tester);
  final pinPage = PinPage(tester);

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.onboardingV4Bos, isEnabled: true);

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  testerHelper.enableSixDigitPin();
  authLandingPage.isReady();

  // enter phone number
  await authLandingPage.phoneNumberInputField.enterText('**********');
  await authLandingPage.confirmPhoneNumberButton.tap();

  // enter PIN
  pinPage.isReady();
  await pinPage.enterPin('166437');

  await tester.pumpAndSettle();
  homePage.isReady();

  // trigger logout notification due to signin on another device
  await testerHelper.simulateTapOnLogoutNotification();
  await tester.pumpAndSettle();
  await testerHelper.pumpHome();
  await tester.pumpAndSettle();

  // Logged out at authentication screen
  authLandingPage.isReady();
  // check for popup with logout reason
  logoutAnotherDeviceDialog.isReady();
  // close dialog
  await logoutAnotherDeviceDialog.okButton.tap();
  logoutAnotherDeviceDialog.isAbsent();
}
