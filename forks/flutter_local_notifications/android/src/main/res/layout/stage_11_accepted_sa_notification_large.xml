<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="15dp"
    android:orientation="vertical">

<TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="2"
        android:fontFamily="@font/roboto"
        android:text="@string/stage_11_accepted_sa_notification_title"
        android:textColor="@color/title_text_color"
        android:textSize="14sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:fontFamily="@font/roboto"
        android:text="@string/stage_11_accepted_sa_notification_cta"
        android:textStyle="bold"
        android:textColor="@color/content_text_color_red_cta"
        android:textSize="13sp" />


</LinearLayout>