<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:fontFamily="@font/roboto"
        android:maxLines="1"
        android:text="@string/stage_9_bod2_approved_big_ticket_notification_title"
        android:textColor="@color/title_text_color"
        android:textSize="14sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:ellipsize="end"
        android:fontFamily="@font/roboto"
        android:maxLines="1"
        android:text="@string/stage_9_bod2_approved_big_ticket_notification_content"
        android:textColor="@color/content_text_color"
        android:textSize="12sp" />

    <ImageView
        android:id="@+id/progressBar"
        android:layout_width="115dp"
        android:layout_height="10dp"
        android:layout_marginTop="1dp"
        android:src="@mipmap/pos_selection" />

</LinearLayout>