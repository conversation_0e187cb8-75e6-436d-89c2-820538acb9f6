// Autogenerated from <PERSON><PERSON> (v4.2.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

package dev.flutter.pigeon;

import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.flutter.plugin.common.BasicMessageChannel;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MessageCodec;
import io.flutter.plugin.common.StandardMessageCodec;
import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**Generated class from Pigeon. */
@SuppressWarnings({"unused", "unchecked", "CodeBlock2Expr", "RedundantSuppression"})
public class Pigeon {

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class OpenDataMessage {
    private @Nullable byte[] data;
    public @Nullable byte[] getData() { return data; }
    public void setData(@Nullable byte[] setterArg) {
      this.data = setterArg;
    }

    private @Nullable String password;
    public @Nullable String getPassword() { return password; }
    public void setPassword(@Nullable String setterArg) {
      this.password = setterArg;
    }

    public static final class Builder {
      private @Nullable byte[] data;
      public @NonNull Builder setData(@Nullable byte[] setterArg) {
        this.data = setterArg;
        return this;
      }
      private @Nullable String password;
      public @NonNull Builder setPassword(@Nullable String setterArg) {
        this.password = setterArg;
        return this;
      }
      public @NonNull OpenDataMessage build() {
        OpenDataMessage pigeonReturn = new OpenDataMessage();
        pigeonReturn.setData(data);
        pigeonReturn.setPassword(password);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("data", data);
      toMapResult.put("password", password);
      return toMapResult;
    }
    static @NonNull OpenDataMessage fromMap(@NonNull Map<String, Object> map) {
      OpenDataMessage pigeonResult = new OpenDataMessage();
      Object data = map.get("data");
      pigeonResult.setData((byte[])data);
      Object password = map.get("password");
      pigeonResult.setPassword((String)password);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class OpenPathMessage {
    private @Nullable String path;
    public @Nullable String getPath() { return path; }
    public void setPath(@Nullable String setterArg) {
      this.path = setterArg;
    }

    private @Nullable String password;
    public @Nullable String getPassword() { return password; }
    public void setPassword(@Nullable String setterArg) {
      this.password = setterArg;
    }

    public static final class Builder {
      private @Nullable String path;
      public @NonNull Builder setPath(@Nullable String setterArg) {
        this.path = setterArg;
        return this;
      }
      private @Nullable String password;
      public @NonNull Builder setPassword(@Nullable String setterArg) {
        this.password = setterArg;
        return this;
      }
      public @NonNull OpenPathMessage build() {
        OpenPathMessage pigeonReturn = new OpenPathMessage();
        pigeonReturn.setPath(path);
        pigeonReturn.setPassword(password);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("path", path);
      toMapResult.put("password", password);
      return toMapResult;
    }
    static @NonNull OpenPathMessage fromMap(@NonNull Map<String, Object> map) {
      OpenPathMessage pigeonResult = new OpenPathMessage();
      Object path = map.get("path");
      pigeonResult.setPath((String)path);
      Object password = map.get("password");
      pigeonResult.setPassword((String)password);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class OpenReply {
    private @Nullable String id;
    public @Nullable String getId() { return id; }
    public void setId(@Nullable String setterArg) {
      this.id = setterArg;
    }

    private @Nullable Long pagesCount;
    public @Nullable Long getPagesCount() { return pagesCount; }
    public void setPagesCount(@Nullable Long setterArg) {
      this.pagesCount = setterArg;
    }

    public static final class Builder {
      private @Nullable String id;
      public @NonNull Builder setId(@Nullable String setterArg) {
        this.id = setterArg;
        return this;
      }
      private @Nullable Long pagesCount;
      public @NonNull Builder setPagesCount(@Nullable Long setterArg) {
        this.pagesCount = setterArg;
        return this;
      }
      public @NonNull OpenReply build() {
        OpenReply pigeonReturn = new OpenReply();
        pigeonReturn.setId(id);
        pigeonReturn.setPagesCount(pagesCount);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("id", id);
      toMapResult.put("pagesCount", pagesCount);
      return toMapResult;
    }
    static @NonNull OpenReply fromMap(@NonNull Map<String, Object> map) {
      OpenReply pigeonResult = new OpenReply();
      Object id = map.get("id");
      pigeonResult.setId((String)id);
      Object pagesCount = map.get("pagesCount");
      pigeonResult.setPagesCount((pagesCount == null) ? null : ((pagesCount instanceof Integer) ? (Integer)pagesCount : (Long)pagesCount));
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class IdMessage {
    private @Nullable String id;
    public @Nullable String getId() { return id; }
    public void setId(@Nullable String setterArg) {
      this.id = setterArg;
    }

    public static final class Builder {
      private @Nullable String id;
      public @NonNull Builder setId(@Nullable String setterArg) {
        this.id = setterArg;
        return this;
      }
      public @NonNull IdMessage build() {
        IdMessage pigeonReturn = new IdMessage();
        pigeonReturn.setId(id);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("id", id);
      return toMapResult;
    }
    static @NonNull IdMessage fromMap(@NonNull Map<String, Object> map) {
      IdMessage pigeonResult = new IdMessage();
      Object id = map.get("id");
      pigeonResult.setId((String)id);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class GetPageMessage {
    private @Nullable String documentId;
    public @Nullable String getDocumentId() { return documentId; }
    public void setDocumentId(@Nullable String setterArg) {
      this.documentId = setterArg;
    }

    private @Nullable Long pageNumber;
    public @Nullable Long getPageNumber() { return pageNumber; }
    public void setPageNumber(@Nullable Long setterArg) {
      this.pageNumber = setterArg;
    }

    private @Nullable Boolean autoCloseAndroid;
    public @Nullable Boolean getAutoCloseAndroid() { return autoCloseAndroid; }
    public void setAutoCloseAndroid(@Nullable Boolean setterArg) {
      this.autoCloseAndroid = setterArg;
    }

    public static final class Builder {
      private @Nullable String documentId;
      public @NonNull Builder setDocumentId(@Nullable String setterArg) {
        this.documentId = setterArg;
        return this;
      }
      private @Nullable Long pageNumber;
      public @NonNull Builder setPageNumber(@Nullable Long setterArg) {
        this.pageNumber = setterArg;
        return this;
      }
      private @Nullable Boolean autoCloseAndroid;
      public @NonNull Builder setAutoCloseAndroid(@Nullable Boolean setterArg) {
        this.autoCloseAndroid = setterArg;
        return this;
      }
      public @NonNull GetPageMessage build() {
        GetPageMessage pigeonReturn = new GetPageMessage();
        pigeonReturn.setDocumentId(documentId);
        pigeonReturn.setPageNumber(pageNumber);
        pigeonReturn.setAutoCloseAndroid(autoCloseAndroid);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("documentId", documentId);
      toMapResult.put("pageNumber", pageNumber);
      toMapResult.put("autoCloseAndroid", autoCloseAndroid);
      return toMapResult;
    }
    static @NonNull GetPageMessage fromMap(@NonNull Map<String, Object> map) {
      GetPageMessage pigeonResult = new GetPageMessage();
      Object documentId = map.get("documentId");
      pigeonResult.setDocumentId((String)documentId);
      Object pageNumber = map.get("pageNumber");
      pigeonResult.setPageNumber((pageNumber == null) ? null : ((pageNumber instanceof Integer) ? (Integer)pageNumber : (Long)pageNumber));
      Object autoCloseAndroid = map.get("autoCloseAndroid");
      pigeonResult.setAutoCloseAndroid((Boolean)autoCloseAndroid);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class GetPageReply {
    private @Nullable String id;
    public @Nullable String getId() { return id; }
    public void setId(@Nullable String setterArg) {
      this.id = setterArg;
    }

    private @Nullable Double width;
    public @Nullable Double getWidth() { return width; }
    public void setWidth(@Nullable Double setterArg) {
      this.width = setterArg;
    }

    private @Nullable Double height;
    public @Nullable Double getHeight() { return height; }
    public void setHeight(@Nullable Double setterArg) {
      this.height = setterArg;
    }

    public static final class Builder {
      private @Nullable String id;
      public @NonNull Builder setId(@Nullable String setterArg) {
        this.id = setterArg;
        return this;
      }
      private @Nullable Double width;
      public @NonNull Builder setWidth(@Nullable Double setterArg) {
        this.width = setterArg;
        return this;
      }
      private @Nullable Double height;
      public @NonNull Builder setHeight(@Nullable Double setterArg) {
        this.height = setterArg;
        return this;
      }
      public @NonNull GetPageReply build() {
        GetPageReply pigeonReturn = new GetPageReply();
        pigeonReturn.setId(id);
        pigeonReturn.setWidth(width);
        pigeonReturn.setHeight(height);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("id", id);
      toMapResult.put("width", width);
      toMapResult.put("height", height);
      return toMapResult;
    }
    static @NonNull GetPageReply fromMap(@NonNull Map<String, Object> map) {
      GetPageReply pigeonResult = new GetPageReply();
      Object id = map.get("id");
      pigeonResult.setId((String)id);
      Object width = map.get("width");
      pigeonResult.setWidth((Double)width);
      Object height = map.get("height");
      pigeonResult.setHeight((Double)height);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class RenderPageMessage {
    private @Nullable String pageId;
    public @Nullable String getPageId() { return pageId; }
    public void setPageId(@Nullable String setterArg) {
      this.pageId = setterArg;
    }

    private @Nullable Long width;
    public @Nullable Long getWidth() { return width; }
    public void setWidth(@Nullable Long setterArg) {
      this.width = setterArg;
    }

    private @Nullable Long height;
    public @Nullable Long getHeight() { return height; }
    public void setHeight(@Nullable Long setterArg) {
      this.height = setterArg;
    }

    private @Nullable Long format;
    public @Nullable Long getFormat() { return format; }
    public void setFormat(@Nullable Long setterArg) {
      this.format = setterArg;
    }

    private @Nullable String backgroundColor;
    public @Nullable String getBackgroundColor() { return backgroundColor; }
    public void setBackgroundColor(@Nullable String setterArg) {
      this.backgroundColor = setterArg;
    }

    private @Nullable Boolean crop;
    public @Nullable Boolean getCrop() { return crop; }
    public void setCrop(@Nullable Boolean setterArg) {
      this.crop = setterArg;
    }

    private @Nullable Long cropX;
    public @Nullable Long getCropX() { return cropX; }
    public void setCropX(@Nullable Long setterArg) {
      this.cropX = setterArg;
    }

    private @Nullable Long cropY;
    public @Nullable Long getCropY() { return cropY; }
    public void setCropY(@Nullable Long setterArg) {
      this.cropY = setterArg;
    }

    private @Nullable Long cropHeight;
    public @Nullable Long getCropHeight() { return cropHeight; }
    public void setCropHeight(@Nullable Long setterArg) {
      this.cropHeight = setterArg;
    }

    private @Nullable Long cropWidth;
    public @Nullable Long getCropWidth() { return cropWidth; }
    public void setCropWidth(@Nullable Long setterArg) {
      this.cropWidth = setterArg;
    }

    private @Nullable Long quality;
    public @Nullable Long getQuality() { return quality; }
    public void setQuality(@Nullable Long setterArg) {
      this.quality = setterArg;
    }

    private @Nullable Boolean forPrint;
    public @Nullable Boolean getForPrint() { return forPrint; }
    public void setForPrint(@Nullable Boolean setterArg) {
      this.forPrint = setterArg;
    }

    public static final class Builder {
      private @Nullable String pageId;
      public @NonNull Builder setPageId(@Nullable String setterArg) {
        this.pageId = setterArg;
        return this;
      }
      private @Nullable Long width;
      public @NonNull Builder setWidth(@Nullable Long setterArg) {
        this.width = setterArg;
        return this;
      }
      private @Nullable Long height;
      public @NonNull Builder setHeight(@Nullable Long setterArg) {
        this.height = setterArg;
        return this;
      }
      private @Nullable Long format;
      public @NonNull Builder setFormat(@Nullable Long setterArg) {
        this.format = setterArg;
        return this;
      }
      private @Nullable String backgroundColor;
      public @NonNull Builder setBackgroundColor(@Nullable String setterArg) {
        this.backgroundColor = setterArg;
        return this;
      }
      private @Nullable Boolean crop;
      public @NonNull Builder setCrop(@Nullable Boolean setterArg) {
        this.crop = setterArg;
        return this;
      }
      private @Nullable Long cropX;
      public @NonNull Builder setCropX(@Nullable Long setterArg) {
        this.cropX = setterArg;
        return this;
      }
      private @Nullable Long cropY;
      public @NonNull Builder setCropY(@Nullable Long setterArg) {
        this.cropY = setterArg;
        return this;
      }
      private @Nullable Long cropHeight;
      public @NonNull Builder setCropHeight(@Nullable Long setterArg) {
        this.cropHeight = setterArg;
        return this;
      }
      private @Nullable Long cropWidth;
      public @NonNull Builder setCropWidth(@Nullable Long setterArg) {
        this.cropWidth = setterArg;
        return this;
      }
      private @Nullable Long quality;
      public @NonNull Builder setQuality(@Nullable Long setterArg) {
        this.quality = setterArg;
        return this;
      }
      private @Nullable Boolean forPrint;
      public @NonNull Builder setForPrint(@Nullable Boolean setterArg) {
        this.forPrint = setterArg;
        return this;
      }
      public @NonNull RenderPageMessage build() {
        RenderPageMessage pigeonReturn = new RenderPageMessage();
        pigeonReturn.setPageId(pageId);
        pigeonReturn.setWidth(width);
        pigeonReturn.setHeight(height);
        pigeonReturn.setFormat(format);
        pigeonReturn.setBackgroundColor(backgroundColor);
        pigeonReturn.setCrop(crop);
        pigeonReturn.setCropX(cropX);
        pigeonReturn.setCropY(cropY);
        pigeonReturn.setCropHeight(cropHeight);
        pigeonReturn.setCropWidth(cropWidth);
        pigeonReturn.setQuality(quality);
        pigeonReturn.setForPrint(forPrint);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("pageId", pageId);
      toMapResult.put("width", width);
      toMapResult.put("height", height);
      toMapResult.put("format", format);
      toMapResult.put("backgroundColor", backgroundColor);
      toMapResult.put("crop", crop);
      toMapResult.put("cropX", cropX);
      toMapResult.put("cropY", cropY);
      toMapResult.put("cropHeight", cropHeight);
      toMapResult.put("cropWidth", cropWidth);
      toMapResult.put("quality", quality);
      toMapResult.put("forPrint", forPrint);
      return toMapResult;
    }
    static @NonNull RenderPageMessage fromMap(@NonNull Map<String, Object> map) {
      RenderPageMessage pigeonResult = new RenderPageMessage();
      Object pageId = map.get("pageId");
      pigeonResult.setPageId((String)pageId);
      Object width = map.get("width");
      pigeonResult.setWidth((width == null) ? null : ((width instanceof Integer) ? (Integer)width : (Long)width));
      Object height = map.get("height");
      pigeonResult.setHeight((height == null) ? null : ((height instanceof Integer) ? (Integer)height : (Long)height));
      Object format = map.get("format");
      pigeonResult.setFormat((format == null) ? null : ((format instanceof Integer) ? (Integer)format : (Long)format));
      Object backgroundColor = map.get("backgroundColor");
      pigeonResult.setBackgroundColor((String)backgroundColor);
      Object crop = map.get("crop");
      pigeonResult.setCrop((Boolean)crop);
      Object cropX = map.get("cropX");
      pigeonResult.setCropX((cropX == null) ? null : ((cropX instanceof Integer) ? (Integer)cropX : (Long)cropX));
      Object cropY = map.get("cropY");
      pigeonResult.setCropY((cropY == null) ? null : ((cropY instanceof Integer) ? (Integer)cropY : (Long)cropY));
      Object cropHeight = map.get("cropHeight");
      pigeonResult.setCropHeight((cropHeight == null) ? null : ((cropHeight instanceof Integer) ? (Integer)cropHeight : (Long)cropHeight));
      Object cropWidth = map.get("cropWidth");
      pigeonResult.setCropWidth((cropWidth == null) ? null : ((cropWidth instanceof Integer) ? (Integer)cropWidth : (Long)cropWidth));
      Object quality = map.get("quality");
      pigeonResult.setQuality((quality == null) ? null : ((quality instanceof Integer) ? (Integer)quality : (Long)quality));
      Object forPrint = map.get("forPrint");
      pigeonResult.setForPrint((Boolean)forPrint);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class RenderPageReply {
    private @Nullable Long width;
    public @Nullable Long getWidth() { return width; }
    public void setWidth(@Nullable Long setterArg) {
      this.width = setterArg;
    }

    private @Nullable Long height;
    public @Nullable Long getHeight() { return height; }
    public void setHeight(@Nullable Long setterArg) {
      this.height = setterArg;
    }

    private @Nullable String path;
    public @Nullable String getPath() { return path; }
    public void setPath(@Nullable String setterArg) {
      this.path = setterArg;
    }

    private @Nullable byte[] data;
    public @Nullable byte[] getData() { return data; }
    public void setData(@Nullable byte[] setterArg) {
      this.data = setterArg;
    }

    public static final class Builder {
      private @Nullable Long width;
      public @NonNull Builder setWidth(@Nullable Long setterArg) {
        this.width = setterArg;
        return this;
      }
      private @Nullable Long height;
      public @NonNull Builder setHeight(@Nullable Long setterArg) {
        this.height = setterArg;
        return this;
      }
      private @Nullable String path;
      public @NonNull Builder setPath(@Nullable String setterArg) {
        this.path = setterArg;
        return this;
      }
      private @Nullable byte[] data;
      public @NonNull Builder setData(@Nullable byte[] setterArg) {
        this.data = setterArg;
        return this;
      }
      public @NonNull RenderPageReply build() {
        RenderPageReply pigeonReturn = new RenderPageReply();
        pigeonReturn.setWidth(width);
        pigeonReturn.setHeight(height);
        pigeonReturn.setPath(path);
        pigeonReturn.setData(data);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("width", width);
      toMapResult.put("height", height);
      toMapResult.put("path", path);
      toMapResult.put("data", data);
      return toMapResult;
    }
    static @NonNull RenderPageReply fromMap(@NonNull Map<String, Object> map) {
      RenderPageReply pigeonResult = new RenderPageReply();
      Object width = map.get("width");
      pigeonResult.setWidth((width == null) ? null : ((width instanceof Integer) ? (Integer)width : (Long)width));
      Object height = map.get("height");
      pigeonResult.setHeight((height == null) ? null : ((height instanceof Integer) ? (Integer)height : (Long)height));
      Object path = map.get("path");
      pigeonResult.setPath((String)path);
      Object data = map.get("data");
      pigeonResult.setData((byte[])data);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class RegisterTextureReply {
    private @Nullable Long id;
    public @Nullable Long getId() { return id; }
    public void setId(@Nullable Long setterArg) {
      this.id = setterArg;
    }

    public static final class Builder {
      private @Nullable Long id;
      public @NonNull Builder setId(@Nullable Long setterArg) {
        this.id = setterArg;
        return this;
      }
      public @NonNull RegisterTextureReply build() {
        RegisterTextureReply pigeonReturn = new RegisterTextureReply();
        pigeonReturn.setId(id);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("id", id);
      return toMapResult;
    }
    static @NonNull RegisterTextureReply fromMap(@NonNull Map<String, Object> map) {
      RegisterTextureReply pigeonResult = new RegisterTextureReply();
      Object id = map.get("id");
      pigeonResult.setId((id == null) ? null : ((id instanceof Integer) ? (Integer)id : (Long)id));
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class UpdateTextureMessage {
    private @Nullable String documentId;
    public @Nullable String getDocumentId() { return documentId; }
    public void setDocumentId(@Nullable String setterArg) {
      this.documentId = setterArg;
    }

    private @Nullable Long pageNumber;
    public @Nullable Long getPageNumber() { return pageNumber; }
    public void setPageNumber(@Nullable Long setterArg) {
      this.pageNumber = setterArg;
    }

    private @Nullable String pageId;
    public @Nullable String getPageId() { return pageId; }
    public void setPageId(@Nullable String setterArg) {
      this.pageId = setterArg;
    }

    private @Nullable Long textureId;
    public @Nullable Long getTextureId() { return textureId; }
    public void setTextureId(@Nullable Long setterArg) {
      this.textureId = setterArg;
    }

    private @Nullable Long width;
    public @Nullable Long getWidth() { return width; }
    public void setWidth(@Nullable Long setterArg) {
      this.width = setterArg;
    }

    private @Nullable Long height;
    public @Nullable Long getHeight() { return height; }
    public void setHeight(@Nullable Long setterArg) {
      this.height = setterArg;
    }

    private @Nullable String backgroundColor;
    public @Nullable String getBackgroundColor() { return backgroundColor; }
    public void setBackgroundColor(@Nullable String setterArg) {
      this.backgroundColor = setterArg;
    }

    private @Nullable Long sourceX;
    public @Nullable Long getSourceX() { return sourceX; }
    public void setSourceX(@Nullable Long setterArg) {
      this.sourceX = setterArg;
    }

    private @Nullable Long sourceY;
    public @Nullable Long getSourceY() { return sourceY; }
    public void setSourceY(@Nullable Long setterArg) {
      this.sourceY = setterArg;
    }

    private @Nullable Long destinationX;
    public @Nullable Long getDestinationX() { return destinationX; }
    public void setDestinationX(@Nullable Long setterArg) {
      this.destinationX = setterArg;
    }

    private @Nullable Long destinationY;
    public @Nullable Long getDestinationY() { return destinationY; }
    public void setDestinationY(@Nullable Long setterArg) {
      this.destinationY = setterArg;
    }

    private @Nullable Double fullWidth;
    public @Nullable Double getFullWidth() { return fullWidth; }
    public void setFullWidth(@Nullable Double setterArg) {
      this.fullWidth = setterArg;
    }

    private @Nullable Double fullHeight;
    public @Nullable Double getFullHeight() { return fullHeight; }
    public void setFullHeight(@Nullable Double setterArg) {
      this.fullHeight = setterArg;
    }

    private @Nullable Long textureWidth;
    public @Nullable Long getTextureWidth() { return textureWidth; }
    public void setTextureWidth(@Nullable Long setterArg) {
      this.textureWidth = setterArg;
    }

    private @Nullable Long textureHeight;
    public @Nullable Long getTextureHeight() { return textureHeight; }
    public void setTextureHeight(@Nullable Long setterArg) {
      this.textureHeight = setterArg;
    }

    private @Nullable Boolean allowAntiAliasing;
    public @Nullable Boolean getAllowAntiAliasing() { return allowAntiAliasing; }
    public void setAllowAntiAliasing(@Nullable Boolean setterArg) {
      this.allowAntiAliasing = setterArg;
    }

    public static final class Builder {
      private @Nullable String documentId;
      public @NonNull Builder setDocumentId(@Nullable String setterArg) {
        this.documentId = setterArg;
        return this;
      }
      private @Nullable Long pageNumber;
      public @NonNull Builder setPageNumber(@Nullable Long setterArg) {
        this.pageNumber = setterArg;
        return this;
      }
      private @Nullable String pageId;
      public @NonNull Builder setPageId(@Nullable String setterArg) {
        this.pageId = setterArg;
        return this;
      }
      private @Nullable Long textureId;
      public @NonNull Builder setTextureId(@Nullable Long setterArg) {
        this.textureId = setterArg;
        return this;
      }
      private @Nullable Long width;
      public @NonNull Builder setWidth(@Nullable Long setterArg) {
        this.width = setterArg;
        return this;
      }
      private @Nullable Long height;
      public @NonNull Builder setHeight(@Nullable Long setterArg) {
        this.height = setterArg;
        return this;
      }
      private @Nullable String backgroundColor;
      public @NonNull Builder setBackgroundColor(@Nullable String setterArg) {
        this.backgroundColor = setterArg;
        return this;
      }
      private @Nullable Long sourceX;
      public @NonNull Builder setSourceX(@Nullable Long setterArg) {
        this.sourceX = setterArg;
        return this;
      }
      private @Nullable Long sourceY;
      public @NonNull Builder setSourceY(@Nullable Long setterArg) {
        this.sourceY = setterArg;
        return this;
      }
      private @Nullable Long destinationX;
      public @NonNull Builder setDestinationX(@Nullable Long setterArg) {
        this.destinationX = setterArg;
        return this;
      }
      private @Nullable Long destinationY;
      public @NonNull Builder setDestinationY(@Nullable Long setterArg) {
        this.destinationY = setterArg;
        return this;
      }
      private @Nullable Double fullWidth;
      public @NonNull Builder setFullWidth(@Nullable Double setterArg) {
        this.fullWidth = setterArg;
        return this;
      }
      private @Nullable Double fullHeight;
      public @NonNull Builder setFullHeight(@Nullable Double setterArg) {
        this.fullHeight = setterArg;
        return this;
      }
      private @Nullable Long textureWidth;
      public @NonNull Builder setTextureWidth(@Nullable Long setterArg) {
        this.textureWidth = setterArg;
        return this;
      }
      private @Nullable Long textureHeight;
      public @NonNull Builder setTextureHeight(@Nullable Long setterArg) {
        this.textureHeight = setterArg;
        return this;
      }
      private @Nullable Boolean allowAntiAliasing;
      public @NonNull Builder setAllowAntiAliasing(@Nullable Boolean setterArg) {
        this.allowAntiAliasing = setterArg;
        return this;
      }
      public @NonNull UpdateTextureMessage build() {
        UpdateTextureMessage pigeonReturn = new UpdateTextureMessage();
        pigeonReturn.setDocumentId(documentId);
        pigeonReturn.setPageNumber(pageNumber);
        pigeonReturn.setPageId(pageId);
        pigeonReturn.setTextureId(textureId);
        pigeonReturn.setWidth(width);
        pigeonReturn.setHeight(height);
        pigeonReturn.setBackgroundColor(backgroundColor);
        pigeonReturn.setSourceX(sourceX);
        pigeonReturn.setSourceY(sourceY);
        pigeonReturn.setDestinationX(destinationX);
        pigeonReturn.setDestinationY(destinationY);
        pigeonReturn.setFullWidth(fullWidth);
        pigeonReturn.setFullHeight(fullHeight);
        pigeonReturn.setTextureWidth(textureWidth);
        pigeonReturn.setTextureHeight(textureHeight);
        pigeonReturn.setAllowAntiAliasing(allowAntiAliasing);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("documentId", documentId);
      toMapResult.put("pageNumber", pageNumber);
      toMapResult.put("pageId", pageId);
      toMapResult.put("textureId", textureId);
      toMapResult.put("width", width);
      toMapResult.put("height", height);
      toMapResult.put("backgroundColor", backgroundColor);
      toMapResult.put("sourceX", sourceX);
      toMapResult.put("sourceY", sourceY);
      toMapResult.put("destinationX", destinationX);
      toMapResult.put("destinationY", destinationY);
      toMapResult.put("fullWidth", fullWidth);
      toMapResult.put("fullHeight", fullHeight);
      toMapResult.put("textureWidth", textureWidth);
      toMapResult.put("textureHeight", textureHeight);
      toMapResult.put("allowAntiAliasing", allowAntiAliasing);
      return toMapResult;
    }
    static @NonNull UpdateTextureMessage fromMap(@NonNull Map<String, Object> map) {
      UpdateTextureMessage pigeonResult = new UpdateTextureMessage();
      Object documentId = map.get("documentId");
      pigeonResult.setDocumentId((String)documentId);
      Object pageNumber = map.get("pageNumber");
      pigeonResult.setPageNumber((pageNumber == null) ? null : ((pageNumber instanceof Integer) ? (Integer)pageNumber : (Long)pageNumber));
      Object pageId = map.get("pageId");
      pigeonResult.setPageId((String)pageId);
      Object textureId = map.get("textureId");
      pigeonResult.setTextureId((textureId == null) ? null : ((textureId instanceof Integer) ? (Integer)textureId : (Long)textureId));
      Object width = map.get("width");
      pigeonResult.setWidth((width == null) ? null : ((width instanceof Integer) ? (Integer)width : (Long)width));
      Object height = map.get("height");
      pigeonResult.setHeight((height == null) ? null : ((height instanceof Integer) ? (Integer)height : (Long)height));
      Object backgroundColor = map.get("backgroundColor");
      pigeonResult.setBackgroundColor((String)backgroundColor);
      Object sourceX = map.get("sourceX");
      pigeonResult.setSourceX((sourceX == null) ? null : ((sourceX instanceof Integer) ? (Integer)sourceX : (Long)sourceX));
      Object sourceY = map.get("sourceY");
      pigeonResult.setSourceY((sourceY == null) ? null : ((sourceY instanceof Integer) ? (Integer)sourceY : (Long)sourceY));
      Object destinationX = map.get("destinationX");
      pigeonResult.setDestinationX((destinationX == null) ? null : ((destinationX instanceof Integer) ? (Integer)destinationX : (Long)destinationX));
      Object destinationY = map.get("destinationY");
      pigeonResult.setDestinationY((destinationY == null) ? null : ((destinationY instanceof Integer) ? (Integer)destinationY : (Long)destinationY));
      Object fullWidth = map.get("fullWidth");
      pigeonResult.setFullWidth((Double)fullWidth);
      Object fullHeight = map.get("fullHeight");
      pigeonResult.setFullHeight((Double)fullHeight);
      Object textureWidth = map.get("textureWidth");
      pigeonResult.setTextureWidth((textureWidth == null) ? null : ((textureWidth instanceof Integer) ? (Integer)textureWidth : (Long)textureWidth));
      Object textureHeight = map.get("textureHeight");
      pigeonResult.setTextureHeight((textureHeight == null) ? null : ((textureHeight instanceof Integer) ? (Integer)textureHeight : (Long)textureHeight));
      Object allowAntiAliasing = map.get("allowAntiAliasing");
      pigeonResult.setAllowAntiAliasing((Boolean)allowAntiAliasing);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class ResizeTextureMessage {
    private @Nullable Long textureId;
    public @Nullable Long getTextureId() { return textureId; }
    public void setTextureId(@Nullable Long setterArg) {
      this.textureId = setterArg;
    }

    private @Nullable Long width;
    public @Nullable Long getWidth() { return width; }
    public void setWidth(@Nullable Long setterArg) {
      this.width = setterArg;
    }

    private @Nullable Long height;
    public @Nullable Long getHeight() { return height; }
    public void setHeight(@Nullable Long setterArg) {
      this.height = setterArg;
    }

    public static final class Builder {
      private @Nullable Long textureId;
      public @NonNull Builder setTextureId(@Nullable Long setterArg) {
        this.textureId = setterArg;
        return this;
      }
      private @Nullable Long width;
      public @NonNull Builder setWidth(@Nullable Long setterArg) {
        this.width = setterArg;
        return this;
      }
      private @Nullable Long height;
      public @NonNull Builder setHeight(@Nullable Long setterArg) {
        this.height = setterArg;
        return this;
      }
      public @NonNull ResizeTextureMessage build() {
        ResizeTextureMessage pigeonReturn = new ResizeTextureMessage();
        pigeonReturn.setTextureId(textureId);
        pigeonReturn.setWidth(width);
        pigeonReturn.setHeight(height);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("textureId", textureId);
      toMapResult.put("width", width);
      toMapResult.put("height", height);
      return toMapResult;
    }
    static @NonNull ResizeTextureMessage fromMap(@NonNull Map<String, Object> map) {
      ResizeTextureMessage pigeonResult = new ResizeTextureMessage();
      Object textureId = map.get("textureId");
      pigeonResult.setTextureId((textureId == null) ? null : ((textureId instanceof Integer) ? (Integer)textureId : (Long)textureId));
      Object width = map.get("width");
      pigeonResult.setWidth((width == null) ? null : ((width instanceof Integer) ? (Integer)width : (Long)width));
      Object height = map.get("height");
      pigeonResult.setHeight((height == null) ? null : ((height instanceof Integer) ? (Integer)height : (Long)height));
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static class UnregisterTextureMessage {
    private @Nullable Long id;
    public @Nullable Long getId() { return id; }
    public void setId(@Nullable Long setterArg) {
      this.id = setterArg;
    }

    public static final class Builder {
      private @Nullable Long id;
      public @NonNull Builder setId(@Nullable Long setterArg) {
        this.id = setterArg;
        return this;
      }
      public @NonNull UnregisterTextureMessage build() {
        UnregisterTextureMessage pigeonReturn = new UnregisterTextureMessage();
        pigeonReturn.setId(id);
        return pigeonReturn;
      }
    }
    @NonNull Map<String, Object> toMap() {
      Map<String, Object> toMapResult = new HashMap<>();
      toMapResult.put("id", id);
      return toMapResult;
    }
    static @NonNull UnregisterTextureMessage fromMap(@NonNull Map<String, Object> map) {
      UnregisterTextureMessage pigeonResult = new UnregisterTextureMessage();
      Object id = map.get("id");
      pigeonResult.setId((id == null) ? null : ((id instanceof Integer) ? (Integer)id : (Long)id));
      return pigeonResult;
    }
  }

  public interface Result<T> {
    void success(T result);
    void error(Throwable error);
  }
  private static class PdfxApiCodec extends StandardMessageCodec {
    public static final PdfxApiCodec INSTANCE = new PdfxApiCodec();
    private PdfxApiCodec() {}
    @Override
    protected Object readValueOfType(byte type, @NonNull ByteBuffer buffer) {
      switch (type) {
        case (byte)128:         
          return GetPageMessage.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)129:         
          return GetPageReply.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)130:         
          return IdMessage.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)131:         
          return OpenDataMessage.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)132:         
          return OpenPathMessage.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)133:         
          return OpenReply.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)134:         
          return RegisterTextureReply.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)135:         
          return RenderPageMessage.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)136:         
          return RenderPageReply.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)137:         
          return ResizeTextureMessage.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)138:         
          return UnregisterTextureMessage.fromMap((Map<String, Object>) readValue(buffer));
        
        case (byte)139:         
          return UpdateTextureMessage.fromMap((Map<String, Object>) readValue(buffer));
        
        default:        
          return super.readValueOfType(type, buffer);
        
      }
    }
    @Override
    protected void writeValue(@NonNull ByteArrayOutputStream stream, Object value)     {
      if (value instanceof GetPageMessage) {
        stream.write(128);
        writeValue(stream, ((GetPageMessage) value).toMap());
      } else 
      if (value instanceof GetPageReply) {
        stream.write(129);
        writeValue(stream, ((GetPageReply) value).toMap());
      } else 
      if (value instanceof IdMessage) {
        stream.write(130);
        writeValue(stream, ((IdMessage) value).toMap());
      } else 
      if (value instanceof OpenDataMessage) {
        stream.write(131);
        writeValue(stream, ((OpenDataMessage) value).toMap());
      } else 
      if (value instanceof OpenPathMessage) {
        stream.write(132);
        writeValue(stream, ((OpenPathMessage) value).toMap());
      } else 
      if (value instanceof OpenReply) {
        stream.write(133);
        writeValue(stream, ((OpenReply) value).toMap());
      } else 
      if (value instanceof RegisterTextureReply) {
        stream.write(134);
        writeValue(stream, ((RegisterTextureReply) value).toMap());
      } else 
      if (value instanceof RenderPageMessage) {
        stream.write(135);
        writeValue(stream, ((RenderPageMessage) value).toMap());
      } else 
      if (value instanceof RenderPageReply) {
        stream.write(136);
        writeValue(stream, ((RenderPageReply) value).toMap());
      } else 
      if (value instanceof ResizeTextureMessage) {
        stream.write(137);
        writeValue(stream, ((ResizeTextureMessage) value).toMap());
      } else 
      if (value instanceof UnregisterTextureMessage) {
        stream.write(138);
        writeValue(stream, ((UnregisterTextureMessage) value).toMap());
      } else 
      if (value instanceof UpdateTextureMessage) {
        stream.write(139);
        writeValue(stream, ((UpdateTextureMessage) value).toMap());
      } else 
{
        super.writeValue(stream, value);
      }
    }
  }

  /**
   * Rebuild: `flutter pub run pigeon --input pigeons/message.dart`
   * After build edit ios/Classes/pigeon/messages.m
   * replace `#import <Flutter/Flutter.h>` to
   * ````
   * ````
   *
   *
   * Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface PdfxApi {
    void openDocumentData(@NonNull OpenDataMessage message, Result<OpenReply> result);
    void openDocumentFile(@NonNull OpenPathMessage message, Result<OpenReply> result);
    void openDocumentAsset(@NonNull OpenPathMessage message, Result<OpenReply> result);
    void closeDocument(@NonNull IdMessage message);
    void getPage(@NonNull GetPageMessage message, Result<GetPageReply> result);
    void renderPage(@NonNull RenderPageMessage message, Result<RenderPageReply> result);
    void closePage(@NonNull IdMessage message);
    @NonNull RegisterTextureReply registerTexture();
    void updateTexture(@NonNull UpdateTextureMessage message, Result<Void> result);
    void resizeTexture(@NonNull ResizeTextureMessage message, Result<Void> result);
    void unregisterTexture(@NonNull UnregisterTextureMessage message);

    /** The codec used by PdfxApi. */
    static MessageCodec<Object> getCodec() {
      return PdfxApiCodec.INSTANCE;
    }

    /**Sets up an instance of `PdfxApi` to handle messages through the `binaryMessenger`. */
    static void setup(BinaryMessenger binaryMessenger, PdfxApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.openDocumentData", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              OpenDataMessage messageArg = (OpenDataMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              Result<OpenReply> resultCallback = new Result<OpenReply>() {
                public void success(OpenReply result) {
                  wrapped.put("result", result);
                  reply.reply(wrapped);
                }
                public void error(Throwable error) {
                  wrapped.put("error", wrapError(error));
                  reply.reply(wrapped);
                }
              };

              api.openDocumentData(messageArg, resultCallback);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
              reply.reply(wrapped);
            }
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.openDocumentFile", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              OpenPathMessage messageArg = (OpenPathMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              Result<OpenReply> resultCallback = new Result<OpenReply>() {
                public void success(OpenReply result) {
                  wrapped.put("result", result);
                  reply.reply(wrapped);
                }
                public void error(Throwable error) {
                  wrapped.put("error", wrapError(error));
                  reply.reply(wrapped);
                }
              };

              api.openDocumentFile(messageArg, resultCallback);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
              reply.reply(wrapped);
            }
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.openDocumentAsset", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              OpenPathMessage messageArg = (OpenPathMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              Result<OpenReply> resultCallback = new Result<OpenReply>() {
                public void success(OpenReply result) {
                  wrapped.put("result", result);
                  reply.reply(wrapped);
                }
                public void error(Throwable error) {
                  wrapped.put("error", wrapError(error));
                  reply.reply(wrapped);
                }
              };

              api.openDocumentAsset(messageArg, resultCallback);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
              reply.reply(wrapped);
            }
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.closeDocument", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              IdMessage messageArg = (IdMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              api.closeDocument(messageArg);
              wrapped.put("result", null);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
            }
            reply.reply(wrapped);
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.getPage", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              GetPageMessage messageArg = (GetPageMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              Result<GetPageReply> resultCallback = new Result<GetPageReply>() {
                public void success(GetPageReply result) {
                  wrapped.put("result", result);
                  reply.reply(wrapped);
                }
                public void error(Throwable error) {
                  wrapped.put("error", wrapError(error));
                  reply.reply(wrapped);
                }
              };

              api.getPage(messageArg, resultCallback);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
              reply.reply(wrapped);
            }
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.renderPage", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              RenderPageMessage messageArg = (RenderPageMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              Result<RenderPageReply> resultCallback = new Result<RenderPageReply>() {
                public void success(RenderPageReply result) {
                  wrapped.put("result", result);
                  reply.reply(wrapped);
                }
                public void error(Throwable error) {
                  wrapped.put("error", wrapError(error));
                  reply.reply(wrapped);
                }
              };

              api.renderPage(messageArg, resultCallback);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
              reply.reply(wrapped);
            }
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.closePage", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              IdMessage messageArg = (IdMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              api.closePage(messageArg);
              wrapped.put("result", null);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
            }
            reply.reply(wrapped);
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.registerTexture", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              RegisterTextureReply output = api.registerTexture();
              wrapped.put("result", output);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
            }
            reply.reply(wrapped);
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.updateTexture", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              UpdateTextureMessage messageArg = (UpdateTextureMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              Result<Void> resultCallback = new Result<Void>() {
                public void success(Void result) {
                  wrapped.put("result", null);
                  reply.reply(wrapped);
                }
                public void error(Throwable error) {
                  wrapped.put("error", wrapError(error));
                  reply.reply(wrapped);
                }
              };

              api.updateTexture(messageArg, resultCallback);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
              reply.reply(wrapped);
            }
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.resizeTexture", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              ResizeTextureMessage messageArg = (ResizeTextureMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              Result<Void> resultCallback = new Result<Void>() {
                public void success(Void result) {
                  wrapped.put("result", null);
                  reply.reply(wrapped);
                }
                public void error(Throwable error) {
                  wrapped.put("error", wrapError(error));
                  reply.reply(wrapped);
                }
              };

              api.resizeTexture(messageArg, resultCallback);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
              reply.reply(wrapped);
            }
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(binaryMessenger, "dev.flutter.pigeon.PdfxApi.unregisterTexture", getCodec());
        if (api != null) {
          channel.setMessageHandler((message, reply) -> {
            Map<String, Object> wrapped = new HashMap<>();
            try {
              ArrayList<Object> args = (ArrayList<Object>)message;
              UnregisterTextureMessage messageArg = (UnregisterTextureMessage)args.get(0);
              if (messageArg == null) {
                throw new NullPointerException("messageArg unexpectedly null.");
              }
              api.unregisterTexture(messageArg);
              wrapped.put("result", null);
            }
            catch (Error | RuntimeException exception) {
              wrapped.put("error", wrapError(exception));
            }
            reply.reply(wrapped);
          });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  @NonNull private static Map<String, Object> wrapError(@NonNull Throwable exception) {
    Map<String, Object> errorMap = new HashMap<>();
    errorMap.put("message", exception.toString());
    errorMap.put("code", exception.getClass().getSimpleName());
    errorMap.put("details", "Cause: " + exception.getCause() + ", Stacktrace: " + Log.getStackTraceString(exception));
    return errorMap;
  }
}
