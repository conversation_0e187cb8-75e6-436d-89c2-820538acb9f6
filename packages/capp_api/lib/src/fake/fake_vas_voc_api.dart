import 'package:dio/dio.dart';
import 'package:selfcareapi/model/voc_activation_eligibility_response.dart';
import 'package:selfcareapi/model/voc_card_payment_transaction_response.dart';
import 'package:selfcareapi/model/voc_card_transaction_request.dart';
import 'package:selfcareapi/model/voc_insurance_detail_response.dart';
import 'package:selfcareapi/model/voc_package_response.dart';
import 'package:selfcareapi/model/voc_payment_summary_response.dart';
import 'package:selfcareapi/model/voc_payment_transaction_details_response.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../capp_api.dart';

class FakeVasVocApi extends FakeApiBase implements VasVocControllerApi {
  @override
  Future<VOCActivationEligibilityResponse> checkCardInsuranceActivationEligibility(
    String contractCode, {
    Options? options,
  }) async {
    await waitBeforeRequest();
    return VOCActivationEligibilityResponse.fromJson(_insurActivateEliRespJson);
  }

  @override
  Future<VOCPaymentSummaryResponse> createCardInsurancePayment(String contractCode, {Options? options}) {
    // TODO(RetD): implement createCardInsurancePayment
    throw UnimplementedError();
  }

  @override
  Future<VOCInsuranceDetailResponse> getCardInsuranceDetails(String contractCode, {Options? options}) {
    // TODO(RetD): implement getCardInsuranceDetails
    throw UnimplementedError();
  }

  @override
  Future<VOCPaymentTransactionDetailsResponse> getPaymentTransactionDetails(String referenceId, {Options? options}) {
    // TODO(RetD): implement getPaymentTransactionDetails
    throw UnimplementedError();
  }

  @override
  Future<VOCPackageResponse> getVOCPackages({String? refProductCode, Options? options}) {
    // TODO(RetD): implement getVOCPackages
    throw UnimplementedError();
  }

  @override
  Future<VOCCardPaymentTransactionResponse> transactCardInsurancePayment(
    String contractCode, {
    VOCCardTransactionRequest? vOCCardTransactionRequest,
    Options? options,
  }) {
    // TODO(RetD): implement transactCardInsurancePayment
    throw UnimplementedError();
  }
}

final Map<String, dynamic> _insurActivateEliRespJson = <String, dynamic>{
  'code': 0,
  'message': 'Successful',
  'stringCode': 'Successful',
  'data': {
    'activationEligibility': true,
  },
};
