import 'package:dio/dio.dart';
import 'package:selfcareapi/model/card_security_api_response.dart';
import 'package:selfcareapi/model/card_security_card_provisioning_data_request.dart';
import 'package:selfcareapi/model/card_security_card_provisioning_data_response.dart';
import 'package:selfcareapi/model/card_security_card_provisioning_data_result_response.dart';
import 'package:selfcareapi/model/card_security_token_provisioning_attempts_response.dart';
import 'package:selfcareapi/model/card_security_token_provisioning_send_result_request.dart';
import 'package:selfcareapi/model/tofu_mock_msg_data_request.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../capp_api.dart';

class FakeWalletPayApi extends FakeApiBase implements ApplePayTokenProvisioningApi {
  @override
  Future<CardSecurityCardProvisioningDataResponse> createApplePayTokenProvisioningRequest(
    CardSecurityCardProvisioningDataRequest cardSecurityCardProvisioningDataRequest, {
    Options? options,
  }) {
    // TODO(retD): implement createApplePayTokenProvisioningRequest
    throw UnimplementedError();
  }

  @override
  Future<CardSecurityCardProvisioningDataResultResponse> getApplePayTokenProvisioningResults(
    String gmaTPCorrelationId, {
    Options? options,
  }) async {
    await waitBeforeRequest();
    return CardSecurityCardProvisioningDataResultResponse.fromJson(
      gmaTPCorrelationId == '1' ? _walletPayResultSuccessJson : _walletPayResultFailureJson,
    );
  }

  @override
  Future<CardSecurityTokenProvisioningAttemptsResponse> getTokenProvisioningAttempts(
    String userId, {
    Options? options,
  }) {
    // TODO(retD): implement getTokenProvisioningAttempts
    throw UnimplementedError();
  }

  @override
  Future<CardSecurityAPIResponse> createMockTofuMsgTest(
    TofuMockMsgDataRequest tofuMockMsgDataRequest, {
    Options? options,
  }) {
    // TODO(retD): implement createMockTofuMsgTest
    throw UnimplementedError();
  }

  @override
  Future<CardSecurityAPIResponse> sendTokenProvisioningResults(
    CardSecurityTokenProvisioningSendResultRequest cardSecurityTokenProvisioningSendResultRequest, {
    Options? options,
  }) {
    // TODO(retD): implement sendTokenProvisioningResults
    throw UnimplementedError();
  }
}

final Map<String, dynamic> _walletPayResultFailureJson = <String, dynamic>{
  'code': 0,
  'message': 'Successful',
  'stringCode': 'Successful',
  'data': {
    'correlationId': '1234567',
    'panReferenceID': 'V-3815023863409817870482',
    'tokenRequestorID': '***********',
    'tokenReferenceID': 'DNITHE381502386342002358',
    'panLast4': '1234',
    'deviceID': 'DEiOiJBMjU2R_0NNS1-ciLCJiI',
    'walletAccountID': 'AiOiJBMjU-2_R0NNS1ciLCJiI6',
  },
};

final Map<String, dynamic> _walletPayResultSuccessJson = <String, dynamic>{
  'code': 0,
  'message': 'Successful',
  'stringCode': 'Successful',
  'data': {
    'correlationId': '1234567',
    'panReferenceID': 'V-3815023863409817870482',
    'tokenRequestorID': '***********',
    'tokenReferenceID': 'DNITHE381502386342002358',
    'panLast4': '1234',
    'deviceID': 'DEiOiJBMjU2R_0NNS1-ciLCJiI',
    'walletAccountID': 'AiOiJBMjU-2_R0NNS1ciLCJiI6',
    'tofuResult': 'REJECT',
    'finalResult': 'Approved 1',
  },
};
