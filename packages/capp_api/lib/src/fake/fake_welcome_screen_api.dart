// ignore_for_file: non_constant_identifier_names

import 'package:dio/dio.dart';
import 'package:selfcareapi/model/models.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../capp_api.dart';

final class FakeWelcomeScreenApiConsts {
  const FakeWelcomeScreenApiConsts._();

  static UserWelcomeScreenResponse get fakeResponse => UserWelcomeScreenResponse(
        id: '123',
        templateId: 1,
        layoutId: 'scoringOffer',
        amount: '100000',
        descriptions: [
          UserWelcomeScreenDescription(
            index: 1,
            icon: 'https://files.vn.hcgma.com/static/bapp_icons/ab_discount_ph.svg',
            content: 'Chỉ áp dụng khi vay trên app',
          ),
        ],
      );
}

class FakeWelcomeScreenApi extends FakeApiBase implements WelcomeScreenApi {
  @override
  Future<UserWelcomeScreenResponse> welcomeScreenTemplateIdGet(
    String templateId,
    String contentLanguage, {
    bool? X_UAT,
    Options? options,
  }) async {
    return FakeWelcomeScreenApiConsts.fakeResponse;
  }
}
