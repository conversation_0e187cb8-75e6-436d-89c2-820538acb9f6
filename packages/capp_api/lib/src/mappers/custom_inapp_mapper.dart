import 'package:capp_domain/capp_domain.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:selfcareapi/model/models.dart' as capp_api;

extension CustomInAppExtension on capp_api.UserSasInAppMessageResponse {
  CustomInApp toDomain(ImageServiceBase imageService) => CustomInApp(
        id: id ?? '',
        templateId: sasId ?? 0,
        title: title ?? '',
        bodyText: bodyText ?? '',
        imageId: imageService.getUrlFromId(imageId) ?? '',
        ctaButtonText: ctaButtonText ?? '',
        ctaButtonUrl: ctaButtonUrl ?? '',
        tertiaryCtaButtonText: tertiaryCtaButtonText ?? '',
      );
}
