import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class AuthenticationAuthenticateVerificationScreen extends StatelessScreen with RouteWrapper {
  final AuthenticationAuthenticateVerificationScreenArguments args;

  AuthenticationAuthenticateVerificationScreen({
    Key? key,
    required AuthenticationAuthenticateVerificationScreenArguments arguments,
  })  : args = arguments,
        super(key: key, arguments: arguments);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider<AuthenticationAuthenticateVerificationCubit>(
        create: (context) => context.get<AuthenticationAuthenticateVerificationCubit>()
          ..startBosAuthenticateHandling(
            args.sessionId,
            secondIdType: args.verifySecondIdentifier ? args.secondIdentifierType : null,
            secondIdValue: args.verifySecondIdentifier ? args.secondIdentifierValue : null,
            initialWaitTime: args.initialWaitTime,
          ),
        child: this,
      );

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationAuthenticateVerificationCubit, AuthenticationAuthenticateVerificationState>(
      listener: (context, state) {
        if (state is AuthenticationAuthenticateStartSuccess) {
          _handleStartSuccessState(state, context);
        } else if (state is AuthenticationAuthenticateVerificationApprove) {
          _handleApproveState(state, context);
        } else if (state is AuthenticationAuthenticateVerificationNotApprove) {
          _handleNotApproveState(state, context);
        } else if (state is AuthenticationAuthenticateVerificationError) {
          _handleErrorState(state, context);
        }
      },
      child: KoyalWillPopScope(
        onWillPop: () async => false,
        child: KoyalScaffold(
          key: const Key('__AuthenticationAuthenticateVerificationScreen__'),
          body: InfoScreen(
            title: L10nCappAuth.of(context).secondIdentifierAuthenticateVerificationTitle,
            subTitle: L10nCappAuth.of(context).secondIdentifierAuthenticateVerificationSubtitle,
            image: const LoadingWidget(),
            alignContentCenter: true,
            showButtonsWithoutFooter: true,
          ),
        ),
      ),
    );
  }

  void _handleStartSuccessState(AuthenticationAuthenticateStartSuccess state, BuildContext context) {
    context.navigator.pushReplacement(
      path: NavigatorPath.cappAuth.loginPinScreen,
      arguments: PinScreenArguments(
        loginSessionId: state.loginSessionId,
        passwordLength: state.passwordLength,
        prefilledInfo: args.prefilledInfo,
        challenge: state.challenge ?? args.loginChallenge,
        publicKey: state.publicKey ?? args.loginPublicKey,
      ),
    );
  }

  void _handleApproveState(AuthenticationAuthenticateVerificationApprove state, BuildContext context) {
    ChangePasswordShortcut? shortcut;
    if (state.loginSessionId != null) {
      if (state.changePasswordOptions != null &&
          state.changePasswordOptions!.changePasswordValidityUntil!.isAfter(DateTime.now())) {
        shortcut = state.changePasswordOptions?.secondIdentifierType == UserSecondIdType.idCard
            ? ChangePasswordShortcut.toSecondId
            : ChangePasswordShortcut.toPassword;
      }

      // User with CUID -> login screen
      context.navigator.pushReplacement(
        path: NavigatorPath.cappAuth.loginPinScreen,
        arguments: PinScreenArguments(
          loginSessionId: state.loginSessionId!,
          passwordLength: state.passwordLength,
          prefilledInfo: args.prefilledInfo,
          shortcutSessionId: args.sessionId,
          changePasswordOptions: state.changePasswordOptions,
          changePasswordShortcut: shortcut,
          challenge: state.challenge,
          publicKey: state.publicKey,
          shortcutChallenge: args.loginChallenge,
          shortcutPublicKey: args.loginPublicKey,
        ),
      );
    } else {
      // User without CUID -> create prospect
      context.navigator.pushReplacement(
        path: NavigatorPath.cappAuth.registrationSetPasswordScreen,
        arguments: SetPasswordScreenArguments(
          sessionId: args.sessionId,
          challenge: state.challenge ?? args.loginChallenge,
          publicKey: state.publicKey ?? args.loginPublicKey,
        ),
      );
    }
  }

  void _handleNotApproveState(AuthenticationAuthenticateVerificationNotApprove state, BuildContext context) {
    context.navigator.pushReplacement(
      path: NavigatorPath.cappAuth.authenticationCrossroadsScreen,
      arguments: DecisionCrossroadsScreenArguments(
        usedSecondIndetifier: args.secondIdentifierType,
        secondIdentifierValue: args.secondIdentifierValue,
        bosResult: state.bosResult,
        nextSteps: state.nextSteps,
        sessionId: args.sessionId,
        prefilledInfo: args.prefilledInfo,
        challenge: state.challenge ?? args.loginChallenge,
        publicKey: state.publicKey ?? args.loginPublicKey,
      ),
    );
  }

  void _handleErrorState(AuthenticationAuthenticateVerificationError state, BuildContext context) {
    showKoyalOverlay<bool?>(
      context,
      key: const Key('__AuthenticationAuthenticateVerificationErrorDialog__'),
      title: L10nCappAuth.of(context).authenticateErrorTitle,
      body: KoyalText.body2(
        L10nCappAuth.of(context).authenticateErrorSubtitle,
        color: ColorTheme.of(context).foreground60Color,
        textAlign: TextAlign.center,
      ),
      dismissible: false,
      primaryButtonBuilder: (context) => PrimaryButton(
        key: const Key('__AuthenticationAuthenticateVerificationErrorDialogOkButton__'),
        text: L10nCappAuth.of(context).okGotIt,
        onPressed: () {
          context.navigator.pop(true);
        },
      ),
    ).then((value) {
      if (value == true) {
        if (!context.mounted) return;
        context.navigator.pop();
      }
    });
  }
}
