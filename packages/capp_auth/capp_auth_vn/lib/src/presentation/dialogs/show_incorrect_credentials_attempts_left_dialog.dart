import 'package:capp_auth_core/l10n/i18n.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

Future showIncorrectCredentialsAttemptsLeftDialog({
  required BuildContext context,
  int? attemptsLeft,
  String? maskedUsername,
}) async {
  return showKoyalOverlay<void>(
    context,
    key: const Key('__incorrectCredentialsAttemptsLeftDialog__'),
    dismissible: false,
    title: L10nCappAuth.of(context).incorrectCredentialsResetPasswordTitle,
    body: KoyalText.body2(
      color: ColorTheme.of(context).foreground60Color,
      attemptsLeft != null && attemptsLeft > 1
          ? L10nCappAuth.of(context).incorrectCredentialsAttemptsLeft(attemptsLeft)
          : L10nCappAuth.of(context).incorrectCredentialsAttemptsLeftOne,
      textAlign: TextAlign.center,
    ),
    primaryButtonBuilder: (c) => PrimaryButton(
      key: const Key('__incorrectCredentialsAttemptsLeftDialogOkButton__'),
      text: L10nCappAuth.of(context).okGotIt,
      onPressed: () => c.navigator.pop(),
    ),
  );
}
