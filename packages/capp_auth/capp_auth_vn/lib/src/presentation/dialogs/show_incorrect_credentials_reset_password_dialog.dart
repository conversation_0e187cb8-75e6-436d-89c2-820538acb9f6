import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

Future showIncorrectCredentialsResetPassword({
  required BuildContext context,
  String? maskedUsername,
  String? unmaskedUsername,
  bool isFromSettings = false,
}) async {
  return showKoyalOverlay<void>(
    context,
    key: const Key('__incorrectCredentialsResetPasswordDialog__'),
    dismissible: false,
    title: L10nCappAuth.of(context).incorrectCredentialsResetPasswordTitle,
    body: KoyalText.body2(
      color: ColorTheme.of(context).foreground60Color,
      L10nCappAuth.of(context).incorrectCredentialsResetPasswordSubtitle,
      textAlign: TextAlign.center,
    ),
    primaryButtonBuilder: (c) => PrimaryButton(
      key: const Key('__incorrectCredentialsResetPasswordDialogOkButton__'),
      text: L10nCappAuth.of(context).incorrectCredentialsResetPasswordResetPasswordButton,
      onPressed: () {
        c.navigator.pushReplacementNamed(
          NavigatorPath.cappAuth.pinPassRecoveryEntryScreen,
          arguments: PinPassRecoveryPhoneScreenArguments(
            prefilledInfo: PrefilledInfo(
              maskedPhoneNumber: maskedUsername ?? '',
              phoneNumber: unmaskedUsername ?? '',
            ),
          ),
        );
      },
    ),
    tertiaryButtonBuilder: (p0) => TertiaryButton(
      key: const Key('__incorrectCredentialsResetPasswordDialogCancelButton__'),
      text: L10nCappAuth.of(context).incorrectCredentialsResetPasswordDoItLaterButton,
      onPressed: () =>
          isFromSettings ? p0.navigator.popUntilFromPackage('CappHome', 'SettingsMenuScreen') : p0.navigator.pop(),
    ),
  );
}
