import 'package:capp_auth_core/capp_auth_core.dart' hide LoginPinScreen;
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth.dart';
import '../../dialogs/show_incorrect_credentials_attempts_left_dialog.dart';
import '../../dialogs/show_incorrect_credentials_reset_password_dialog.dart';

class PasswordScreenBase<T> extends StatelessScreen with RouteWrapper {
  final PinScreenArguments args;
  final bool getBigData;

  PasswordScreenBase({
    Key? key,
    this.getBigData = false,
    required PinScreenArguments arguments,
  })  : args = arguments,
        super(key: key, arguments: arguments);

  Future<void> onSuccess(T r, BuildContext context) async {}
  void onForbiddenAccess(BuildContext context) {
    showAuthErrorUnexpectedErrorDialog(context);
  }

  void trackPasswordScreenOnGoBackClick(BuildContext context) {}
  void trackTemporaryBlockDialogView(BuildContext context) {}
  void trackPermanentBlockDialogView(BuildContext context) {}
  void trackIncorrectDialogView(BuildContext context) {}
  void trackForgotPasswordClick(BuildContext context) {}

  void trackLoginButtonClick(BuildContext context) {}
  String loginButtonLabel(BuildContext context) => L10nCappAuth.of(context).login;

  @override
  Widget wrappedRoute(BuildContext context) => this;

  @override
  Widget build(BuildContext context) {
    final invalidatePassword = context.isFlagEnabledRead(FeatureFlag.invalidatePassword);

    return BlocProvider(
      create: (ctx) => context.get<AuthErrorHandlerCubit>(),
      child: MultiBlocListener(
        listeners: [
          AuthErrorListener(
            trackTemporaryBlockDialogView: trackTemporaryBlockDialogView,
            trackPermanentBlockDialogView: trackPermanentBlockDialogView,
            trackIncorrectDialogView: trackIncorrectDialogView,
          ),
          BlocListener<PasswordBloc<T>, PasswordState>(
            listener: (context, state) {
              if (state.state == LoadingState.isCompleted) {
                state.failureOrSuccess.fold(
                  () {},
                  (a) => {
                    a.fold((l) {
                      context.get<IFirebasePerformanceMonitoring>().stopTrace(TraceType.login);

                      l.maybeMap(
                        orElse: () {
                          showAuthErrorUnexpectedErrorDialog(context);
                        },
                        // will have new auth error for the error handling when user uses all attempts
                        unauthorized: (response) {
                          if (invalidatePassword) {
                            if (response.authErrorResponse?.enforceChangePassword == true ||
                                state.failedAttempts == 0) {
                              showIncorrectCredentialsResetPassword(
                                context: context,
                                unmaskedUsername: args.prefilledInfo?.phoneNumber,
                                maskedUsername: args.prefilledInfo?.maskedPhoneNumber,
                              );
                            } else {
                              showIncorrectCredentialsAttemptsLeftDialog(
                                context: context,
                                attemptsLeft: state.failedAttempts,
                              );
                            }
                          } else {
                            context.read<AuthErrorHandlerCubit>().setErrorResponse(
                                  AuthErrorResponseWrapper(
                                    authErrorResponse: response.authErrorResponse,
                                    prefilledInfo: args.prefilledInfo,
                                  ),
                                );
                          }
                        },
                        verificationRequired: (response) {
                          context.read<AuthErrorHandlerCubit>().setErrorResponse(
                                AuthErrorResponseWrapper(
                                  authErrorResponse: response.authErrorResponse,
                                  prefilledInfo: args.prefilledInfo,
                                ),
                              );
                        },
                        userAccessForbiden: (_) {
                          onForbiddenAccess(context);
                        },
                        reachedMaximumAccountAllowed: (_) => showMaximumAccountAllowedDialog(context),
                      );
                    }, (dynamic r) {
                      onSuccess(r as T, context);
                    }),
                  },
                );
              }
            },
          ),
        ],
        child: BlocBuilder<PasswordBloc<T>, PasswordState<T>>(
          builder: (context, state) => KoyalWillPopScope(
            onWillPop: () async {
              trackPasswordScreenOnGoBackClick(context);
              return true;
            },
            //ignore: Koyal-Scaffold
            child: Scaffold(
              appBar: KoyalAppBar(),
              key: const Key('__passwordScreen__'),
              body: SafeArea(
                child: FixedBottomContentScreen(
                  upperContent: [
                    ...upperContentBuilder(context),
                    KoyalPadding.normalVertical(
                      child: CappSecureInputText(
                        key: const Key('__passwordScreenPasswordInput__'),
                        initialValue: state.password,
                        labelText: L10nCappAuth.of(context).password,
                        enabled: state.state != LoadingState.isLoading,
                        onChanged: (newPassword) =>
                            context.read<PasswordBloc<T>>().add(PasswordEvent.passwordChanged(newPassword)),
                      ),
                    ),
                    if (context.isFlagEnabledWatch(FeatureFlag.passwordRecoveryV2))
                      TertiaryButton(
                        key: const Key('__forgotPasswordButton__'),
                        text: L10nCappAuth.of(context).forgotYourPassword,
                        onPressed: state.state == LoadingState.isLoading
                            ? null
                            : () {
                                trackForgotPasswordClick(context);
                                if (args.changePasswordOptions != null) {
                                  if (args.changePasswordOptions!.changePasswordValidityUntil!.isBefore(
                                    DateTime.now(),
                                  )) {
                                    context.navigator.push(path: NavigatorPath.cappAuth.pinPassRecoveryEntryScreen);
                                  } else if (args.changePasswordShortcut == ChangePasswordShortcut.toPassword) {
                                    context.navigator.push(
                                      path: NavigatorPath.cappAuth.passwordRecoverySetPasswordShortcutScreen,
                                      arguments: SetPasswordScreenArguments(
                                        sessionId: args.shortcutSessionId,
                                        challenge: args.shortcutChallenge,
                                        publicKey: args.shortcutPublicKey,
                                      ),
                                    );
                                  } else if (args.changePasswordShortcut == ChangePasswordShortcut.toSecondId) {
                                    context.navigator.push(
                                      path: NavigatorPath.cappAuth.pinPassRecovery2ndIdShortcutScreen,
                                      arguments: SecondIdVerificationScreenArguments(
                                        sessionId: args.shortcutSessionId,
                                        allowedSecondIds: [UserSecondIdType.idCard],
                                        challenge: args.shortcutChallenge,
                                        publicKey: args.shortcutPublicKey,
                                      ),
                                    );
                                  }
                                } else {
                                  context.navigator.push(path: NavigatorPath.cappAuth.pinPassRecoveryEntryScreen);
                                }
                              },
                      ),
                  ],
                  fixedBottomContent: [
                    KoyalPadding.normalAll(
                      child: state.timetoBlockAccount == null
                          ? _LoginButton<T>(
                              state: state,
                              getBigData: getBigData,
                              label: loginButtonLabel(context),
                              trackLoginButtonClick: trackLoginButtonClick,
                            )
                          : CountdownFormatted(
                              key: const Key('__countdownLock__'),
                              duration: state.timetoBlockAccount!,
                              builder: (ctx, remaining, isFinished) {
                                //ignore: buttons-layout
                                return PrimaryButton(
                                  text: L10nCappAuth.of(context).unblockAccount,
                                  onPressed: () {
                                    context.navigator.pushFromPackage(
                                      package: 'CappAuth',
                                      screen: 'UnblockAccountEntryScreen',
                                      arguments: UnblockAccountPhoneScreenArguments(
                                        prefilledInfo: args.prefilledInfo,
                                      ),
                                    );
                                  },
                                );
                              },
                              onFinish: () => context.read<PasswordBloc<T>>().add(const PasswordEvent.refresh()),
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> upperContentBuilder(BuildContext context) {
    return const [];
  }
}

class _LoginButton<T> extends StatelessWidget {
  final PasswordState state;
  final String label;
  final bool getBigData;
  const _LoginButton({
    Key? key,
    required this.state,
    required this.trackLoginButtonClick,
    required this.label,
    required this.getBigData,
  }) : super(key: key);

  final void Function(BuildContext context) trackLoginButtonClick;

  @override
  Widget build(BuildContext context) {
    // TODO(XX): Please check if we can wrap PrimaryButton by VerticalButtonsLayout/HorizontalButtonsLayout here
    //ignore: buttons-layout
    return PrimaryButton(
      key: const Key('__passwordScreenLoginButton__'),
      text: label,
      isInProgress: state.state == LoadingState.isLoading,
      onPressed: state.password.isEmpty
          ? null
          : () {
              trackLoginButtonClick(context);
              if (getBigData) {
                context.get<BigDataCollectionBloc>().add(
                      BigDataCollectionEvent.process(
                        flow: BigDataFlow.login.getEventCode(),
                        displayData: DeviceUtils.getDisplayData(context),
                      ),
                    );
              }
              context.read<PasswordBloc<T>>().add(const PasswordEvent.submit());
              context.get<IFirebasePerformanceMonitoring>().startTrace(TraceType.login);
            },
    );
  }
}
