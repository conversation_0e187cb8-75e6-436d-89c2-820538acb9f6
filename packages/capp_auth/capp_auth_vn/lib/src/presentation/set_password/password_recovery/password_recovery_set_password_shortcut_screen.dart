import 'package:capp_auth_core/capp_auth_core.dart' hide SetPasswordScreenBase;
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigator/navigator.dart';

import '../base/set_password_screen_base.dart';

class PasswordRecoverySetPasswordShortcutScreen extends SetPasswordScreenBase<AuthenticationCompleteChangePasswordR?> {
  @override
  void trackForgotPasswordResetPassCriteriaViolationView(BuildContext context) {
    context.get<CappAuthTrackingService>().trackForgotPasswordResetPassCriteriaViolationView();
  }

  @override
  void trackForgotPasswordResetPassOnConfirmClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackForgotPasswordResetPassOnConfirmClick();
  }

  @override
  void trackForgotPasswordResetPassOnGoBackClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackForgotPasswordResetPassOnGoBackClick();
  }

  @override
  void onSuccess(AuthenticationCompleteChangePasswordR? r, String password, BuildContext context) {
    context.navigator.pushNamedAndRemoveUntil(
      NavigatorPath.cappAuth.pinPassRecoverySuccessScreen,
      NavigatorPath.cappAuth.pinPassRecoverySuccessScreen,
      arguments: PinPassRecoverySuccessScreenArguments(
        loginSessionId: r?.sessionId,
        password: password,
        challenge: r?.challenge,
        publicKey: r?.publicKey,
        hashPassword: context.get<IFeatureFlagRepository>().isEnabledCached(FeatureFlag.cleartextPassword),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider<SetPasswordBloc>(
        create: (context) => context.get<PasswordRecoverySetPasswordShortcutBloc>()
          ..add(
            SetPasswordEvent.init(
              sessionId: args.sessionId,
              challenge: args.challenge,
              publicKey: args.publicKey,
            ),
          ),
        child: this,
      );

  @override
  String titleBuilder(BuildContext c) {
    return L10nCappAuth.of(c).pinPassRecoverySetPassTitle;
  }

  @override
  MainHeading mainHeadingBuilder(BuildContext c) {
    return MainHeading(
      centerAlign: false,
      title: L10nCappAuth.of(c).pinPassRecoverySetPassHeadingTitle,
      subtitle: L10nCappAuth.of(c).pinPassRecoverySetPassHeadingSubtitle,
    );
  }

  PasswordRecoverySetPasswordShortcutScreen({
    Key? key,
    required SetPasswordScreenArguments arguments,
  }) : super(
          key: key,
          showFullPageLoading: false,
          arguments: SetPasswordScreenArguments(
            sessionId: arguments.sessionId,
            challenge: arguments.challenge,
            publicKey: arguments.publicKey,
          ),
        );
}
