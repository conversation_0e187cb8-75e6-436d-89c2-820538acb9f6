import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'card_allow_recurring_bloc.freezed.dart';
part 'card_allow_recurring_event.dart';
part 'card_allow_recurring_state.dart';

class CardAllowRecurringBloc extends Bloc<CardAllowRecurringEvent, CardAllowRecurringState> {
  CardAllowRecurringBloc() : super(CardAllowRecurringState.initialize()) {
    on<_Initialize>(_initialize);
    on<_SetAllowRecurring>(_setAllowRecurring);
  }

  void _initialize(_Initialize e, Emitter<CardAllowRecurringState> emit) {}
  void _setAllowRecurring(_SetAllowRecurring e, Emitter<CardAllowRecurringState> emit) {
    emit(state.copyWith(isAllowRecurring: e.value));
  }
}
