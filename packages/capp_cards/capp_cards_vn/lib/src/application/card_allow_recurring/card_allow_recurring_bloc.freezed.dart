// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card_allow_recurring_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CardAllowRecurringEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(bool value) setAllowRecurring,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(bool value)? setAllowRecurring,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(bool value)? setAllowRecurring,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SetAllowRecurring value) setAllowRecurring,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SetAllowRecurring value)? setAllowRecurring,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SetAllowRecurring value)? setAllowRecurring,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardAllowRecurringEventCopyWith<$Res> {
  factory $CardAllowRecurringEventCopyWith(CardAllowRecurringEvent value,
          $Res Function(CardAllowRecurringEvent) then) =
      _$CardAllowRecurringEventCopyWithImpl<$Res, CardAllowRecurringEvent>;
}

/// @nodoc
class _$CardAllowRecurringEventCopyWithImpl<$Res,
        $Val extends CardAllowRecurringEvent>
    implements $CardAllowRecurringEventCopyWith<$Res> {
  _$CardAllowRecurringEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$CardAllowRecurringEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize();

  @override
  String toString() {
    return 'CardAllowRecurringEvent.initialize()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Initialize);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(bool value) setAllowRecurring,
  }) {
    return initialize();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(bool value)? setAllowRecurring,
  }) {
    return initialize?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(bool value)? setAllowRecurring,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SetAllowRecurring value) setAllowRecurring,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SetAllowRecurring value)? setAllowRecurring,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SetAllowRecurring value)? setAllowRecurring,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements CardAllowRecurringEvent {
  const factory _Initialize() = _$_Initialize;
}

/// @nodoc
abstract class _$$_SetAllowRecurringCopyWith<$Res> {
  factory _$$_SetAllowRecurringCopyWith(_$_SetAllowRecurring value,
          $Res Function(_$_SetAllowRecurring) then) =
      __$$_SetAllowRecurringCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$_SetAllowRecurringCopyWithImpl<$Res>
    extends _$CardAllowRecurringEventCopyWithImpl<$Res, _$_SetAllowRecurring>
    implements _$$_SetAllowRecurringCopyWith<$Res> {
  __$$_SetAllowRecurringCopyWithImpl(
      _$_SetAllowRecurring _value, $Res Function(_$_SetAllowRecurring) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$_SetAllowRecurring(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_SetAllowRecurring implements _SetAllowRecurring {
  const _$_SetAllowRecurring({required this.value});

  @override
  final bool value;

  @override
  String toString() {
    return 'CardAllowRecurringEvent.setAllowRecurring(value: $value)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetAllowRecurring &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetAllowRecurringCopyWith<_$_SetAllowRecurring> get copyWith =>
      __$$_SetAllowRecurringCopyWithImpl<_$_SetAllowRecurring>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(bool value) setAllowRecurring,
  }) {
    return setAllowRecurring(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(bool value)? setAllowRecurring,
  }) {
    return setAllowRecurring?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(bool value)? setAllowRecurring,
    required TResult orElse(),
  }) {
    if (setAllowRecurring != null) {
      return setAllowRecurring(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SetAllowRecurring value) setAllowRecurring,
  }) {
    return setAllowRecurring(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SetAllowRecurring value)? setAllowRecurring,
  }) {
    return setAllowRecurring?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SetAllowRecurring value)? setAllowRecurring,
    required TResult orElse(),
  }) {
    if (setAllowRecurring != null) {
      return setAllowRecurring(this);
    }
    return orElse();
  }
}

abstract class _SetAllowRecurring implements CardAllowRecurringEvent {
  const factory _SetAllowRecurring({required final bool value}) =
      _$_SetAllowRecurring;

  bool get value;
  @JsonKey(ignore: true)
  _$$_SetAllowRecurringCopyWith<_$_SetAllowRecurring> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$CardAllowRecurringState {
  bool get isAllowRecurring => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CardAllowRecurringStateCopyWith<CardAllowRecurringState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardAllowRecurringStateCopyWith<$Res> {
  factory $CardAllowRecurringStateCopyWith(CardAllowRecurringState value,
          $Res Function(CardAllowRecurringState) then) =
      _$CardAllowRecurringStateCopyWithImpl<$Res, CardAllowRecurringState>;
  @useResult
  $Res call({bool isAllowRecurring});
}

/// @nodoc
class _$CardAllowRecurringStateCopyWithImpl<$Res,
        $Val extends CardAllowRecurringState>
    implements $CardAllowRecurringStateCopyWith<$Res> {
  _$CardAllowRecurringStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAllowRecurring = null,
  }) {
    return _then(_value.copyWith(
      isAllowRecurring: null == isAllowRecurring
          ? _value.isAllowRecurring
          : isAllowRecurring // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CardAllowRecurringStateCopyWith<$Res>
    implements $CardAllowRecurringStateCopyWith<$Res> {
  factory _$$_CardAllowRecurringStateCopyWith(_$_CardAllowRecurringState value,
          $Res Function(_$_CardAllowRecurringState) then) =
      __$$_CardAllowRecurringStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isAllowRecurring});
}

/// @nodoc
class __$$_CardAllowRecurringStateCopyWithImpl<$Res>
    extends _$CardAllowRecurringStateCopyWithImpl<$Res,
        _$_CardAllowRecurringState>
    implements _$$_CardAllowRecurringStateCopyWith<$Res> {
  __$$_CardAllowRecurringStateCopyWithImpl(_$_CardAllowRecurringState _value,
      $Res Function(_$_CardAllowRecurringState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAllowRecurring = null,
  }) {
    return _then(_$_CardAllowRecurringState(
      isAllowRecurring: null == isAllowRecurring
          ? _value.isAllowRecurring
          : isAllowRecurring // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_CardAllowRecurringState implements _CardAllowRecurringState {
  const _$_CardAllowRecurringState({this.isAllowRecurring = true});

  @override
  @JsonKey()
  final bool isAllowRecurring;

  @override
  String toString() {
    return 'CardAllowRecurringState(isAllowRecurring: $isAllowRecurring)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CardAllowRecurringState &&
            (identical(other.isAllowRecurring, isAllowRecurring) ||
                other.isAllowRecurring == isAllowRecurring));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isAllowRecurring);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CardAllowRecurringStateCopyWith<_$_CardAllowRecurringState>
      get copyWith =>
          __$$_CardAllowRecurringStateCopyWithImpl<_$_CardAllowRecurringState>(
              this, _$identity);
}

abstract class _CardAllowRecurringState implements CardAllowRecurringState {
  const factory _CardAllowRecurringState({final bool isAllowRecurring}) =
      _$_CardAllowRecurringState;

  @override
  bool get isAllowRecurring;
  @override
  @JsonKey(ignore: true)
  _$$_CardAllowRecurringStateCopyWith<_$_CardAllowRecurringState>
      get copyWith => throw _privateConstructorUsedError;
}
