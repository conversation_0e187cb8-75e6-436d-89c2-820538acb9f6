import 'package:bloc/bloc.dart';
import 'package:capp_cards_core/capp_cards_core.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'card_subsequent_status_bloc.freezed.dart';
part 'card_subsequent_status_event.dart';
part 'card_subsequent_status_state.dart';

class CardSubsequentStatusBloc extends Bloc<CardSubsequentStatusEvent, CardSubsequentStatusState> {
  final ICardContractRepository cardContractRepository;

  CardSubsequentStatusBloc({
    required this.cardContractRepository,
  }) : super(CardSubsequentStatusState.initialize()) {
    on<_Initialize>(_initialize);
    on<_GetCardSubsequentStatus>(_getCardSubsequentStatus);
  }

  void _initialize(_Initialize e, Emitter<CardSubsequentStatusState> emit) {}
  Future<void> _getCardSubsequentStatus(_GetCardSubsequentStatus e, Emitter<CardSubsequentStatusState> emit) async {
    emit(state.copyWith(isLoading: true, isLoaded: false, isError: false));
    final response = await cardContractRepository.getCardsSubsequentStatus(contractCode: e.contractCode);
    emit(
      response.fold((l) {
        return state.copyWith(
          isLoading: false,
          isLoaded: true,
          isError: true,
          isSubsequentCard: null,
        );
      }, (r) {
        return state.copyWith(
          isLoading: false,
          isLoaded: true,
          isError: false,
          isSubsequentCard: r.isSubsequentCard,
        );
      }),
    );
  }
}
