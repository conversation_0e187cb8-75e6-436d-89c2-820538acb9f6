// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card_subsequent_status_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CardSubsequentStatusEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(String contractCode) getCardSubsequentStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(String contractCode)? getCardSubsequentStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(String contractCode)? getCardSubsequentStatus,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_GetCardSubsequentStatus value)
        getCardSubsequentStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_GetCardSubsequentStatus value)? getCardSubsequentStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_GetCardSubsequentStatus value)? getCardSubsequentStatus,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardSubsequentStatusEventCopyWith<$Res> {
  factory $CardSubsequentStatusEventCopyWith(CardSubsequentStatusEvent value,
          $Res Function(CardSubsequentStatusEvent) then) =
      _$CardSubsequentStatusEventCopyWithImpl<$Res, CardSubsequentStatusEvent>;
}

/// @nodoc
class _$CardSubsequentStatusEventCopyWithImpl<$Res,
        $Val extends CardSubsequentStatusEvent>
    implements $CardSubsequentStatusEventCopyWith<$Res> {
  _$CardSubsequentStatusEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$CardSubsequentStatusEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize();

  @override
  String toString() {
    return 'CardSubsequentStatusEvent.initialize()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Initialize);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(String contractCode) getCardSubsequentStatus,
  }) {
    return initialize();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(String contractCode)? getCardSubsequentStatus,
  }) {
    return initialize?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(String contractCode)? getCardSubsequentStatus,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_GetCardSubsequentStatus value)
        getCardSubsequentStatus,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_GetCardSubsequentStatus value)? getCardSubsequentStatus,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_GetCardSubsequentStatus value)? getCardSubsequentStatus,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements CardSubsequentStatusEvent {
  const factory _Initialize() = _$_Initialize;
}

/// @nodoc
abstract class _$$_GetCardSubsequentStatusCopyWith<$Res> {
  factory _$$_GetCardSubsequentStatusCopyWith(_$_GetCardSubsequentStatus value,
          $Res Function(_$_GetCardSubsequentStatus) then) =
      __$$_GetCardSubsequentStatusCopyWithImpl<$Res>;
  @useResult
  $Res call({String contractCode});
}

/// @nodoc
class __$$_GetCardSubsequentStatusCopyWithImpl<$Res>
    extends _$CardSubsequentStatusEventCopyWithImpl<$Res,
        _$_GetCardSubsequentStatus>
    implements _$$_GetCardSubsequentStatusCopyWith<$Res> {
  __$$_GetCardSubsequentStatusCopyWithImpl(_$_GetCardSubsequentStatus _value,
      $Res Function(_$_GetCardSubsequentStatus) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractCode = null,
  }) {
    return _then(_$_GetCardSubsequentStatus(
      contractCode: null == contractCode
          ? _value.contractCode
          : contractCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_GetCardSubsequentStatus implements _GetCardSubsequentStatus {
  const _$_GetCardSubsequentStatus({required this.contractCode});

  @override
  final String contractCode;

  @override
  String toString() {
    return 'CardSubsequentStatusEvent.getCardSubsequentStatus(contractCode: $contractCode)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GetCardSubsequentStatus &&
            (identical(other.contractCode, contractCode) ||
                other.contractCode == contractCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contractCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GetCardSubsequentStatusCopyWith<_$_GetCardSubsequentStatus>
      get copyWith =>
          __$$_GetCardSubsequentStatusCopyWithImpl<_$_GetCardSubsequentStatus>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(String contractCode) getCardSubsequentStatus,
  }) {
    return getCardSubsequentStatus(contractCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(String contractCode)? getCardSubsequentStatus,
  }) {
    return getCardSubsequentStatus?.call(contractCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(String contractCode)? getCardSubsequentStatus,
    required TResult orElse(),
  }) {
    if (getCardSubsequentStatus != null) {
      return getCardSubsequentStatus(contractCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_GetCardSubsequentStatus value)
        getCardSubsequentStatus,
  }) {
    return getCardSubsequentStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_GetCardSubsequentStatus value)? getCardSubsequentStatus,
  }) {
    return getCardSubsequentStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_GetCardSubsequentStatus value)? getCardSubsequentStatus,
    required TResult orElse(),
  }) {
    if (getCardSubsequentStatus != null) {
      return getCardSubsequentStatus(this);
    }
    return orElse();
  }
}

abstract class _GetCardSubsequentStatus implements CardSubsequentStatusEvent {
  const factory _GetCardSubsequentStatus({required final String contractCode}) =
      _$_GetCardSubsequentStatus;

  String get contractCode;
  @JsonKey(ignore: true)
  _$$_GetCardSubsequentStatusCopyWith<_$_GetCardSubsequentStatus>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$CardSubsequentStatusState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isLoaded => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;
  bool? get isSubsequentCard => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CardSubsequentStatusStateCopyWith<CardSubsequentStatusState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardSubsequentStatusStateCopyWith<$Res> {
  factory $CardSubsequentStatusStateCopyWith(CardSubsequentStatusState value,
          $Res Function(CardSubsequentStatusState) then) =
      _$CardSubsequentStatusStateCopyWithImpl<$Res, CardSubsequentStatusState>;
  @useResult
  $Res call(
      {bool isLoading, bool isLoaded, bool isError, bool? isSubsequentCard});
}

/// @nodoc
class _$CardSubsequentStatusStateCopyWithImpl<$Res,
        $Val extends CardSubsequentStatusState>
    implements $CardSubsequentStatusStateCopyWith<$Res> {
  _$CardSubsequentStatusStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isLoaded = null,
    Object? isError = null,
    Object? isSubsequentCard = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoaded: null == isLoaded
          ? _value.isLoaded
          : isLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubsequentCard: freezed == isSubsequentCard
          ? _value.isSubsequentCard
          : isSubsequentCard // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CardSubsequentStatusStateCopyWith<$Res>
    implements $CardSubsequentStatusStateCopyWith<$Res> {
  factory _$$_CardSubsequentStatusStateCopyWith(
          _$_CardSubsequentStatusState value,
          $Res Function(_$_CardSubsequentStatusState) then) =
      __$$_CardSubsequentStatusStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading, bool isLoaded, bool isError, bool? isSubsequentCard});
}

/// @nodoc
class __$$_CardSubsequentStatusStateCopyWithImpl<$Res>
    extends _$CardSubsequentStatusStateCopyWithImpl<$Res,
        _$_CardSubsequentStatusState>
    implements _$$_CardSubsequentStatusStateCopyWith<$Res> {
  __$$_CardSubsequentStatusStateCopyWithImpl(
      _$_CardSubsequentStatusState _value,
      $Res Function(_$_CardSubsequentStatusState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isLoaded = null,
    Object? isError = null,
    Object? isSubsequentCard = freezed,
  }) {
    return _then(_$_CardSubsequentStatusState(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoaded: null == isLoaded
          ? _value.isLoaded
          : isLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubsequentCard: freezed == isSubsequentCard
          ? _value.isSubsequentCard
          : isSubsequentCard // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$_CardSubsequentStatusState implements _CardSubsequentStatusState {
  const _$_CardSubsequentStatusState(
      {this.isLoading = false,
      this.isLoaded = false,
      this.isError = false,
      this.isSubsequentCard = false});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isLoaded;
  @override
  @JsonKey()
  final bool isError;
  @override
  @JsonKey()
  final bool? isSubsequentCard;

  @override
  String toString() {
    return 'CardSubsequentStatusState(isLoading: $isLoading, isLoaded: $isLoaded, isError: $isError, isSubsequentCard: $isSubsequentCard)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CardSubsequentStatusState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoaded, isLoaded) ||
                other.isLoaded == isLoaded) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.isSubsequentCard, isSubsequentCard) ||
                other.isSubsequentCard == isSubsequentCard));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isLoading, isLoaded, isError, isSubsequentCard);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CardSubsequentStatusStateCopyWith<_$_CardSubsequentStatusState>
      get copyWith => __$$_CardSubsequentStatusStateCopyWithImpl<
          _$_CardSubsequentStatusState>(this, _$identity);
}

abstract class _CardSubsequentStatusState implements CardSubsequentStatusState {
  const factory _CardSubsequentStatusState(
      {final bool isLoading,
      final bool isLoaded,
      final bool isError,
      final bool? isSubsequentCard}) = _$_CardSubsequentStatusState;

  @override
  bool get isLoading;
  @override
  bool get isLoaded;
  @override
  bool get isError;
  @override
  bool? get isSubsequentCard;
  @override
  @JsonKey(ignore: true)
  _$$_CardSubsequentStatusStateCopyWith<_$_CardSubsequentStatusState>
      get copyWith => throw _privateConstructorUsedError;
}
