part of 'card_subsequent_status_bloc.dart';

@freezed
class CardSubsequentStatusState with _$CardSubsequentStatusState {
  const factory CardSubsequentStatusState({
    @Default(false) bool isLoading,
    @Default(false) bool isLoaded,
    @Default(false) bool isError,
    @Default(false) bool? isSubsequentCard,
  }) = _CardSubsequentStatusState;

  factory CardSubsequentStatusState.initialize() => const CardSubsequentStatusState();
}
