import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../capp_cards.dart';

part 'wallet_pay_handle_result_bloc.freezed.dart';
part 'wallet_pay_handle_result_event.dart';
part 'wallet_pay_handle_result_state.dart';

class WalletPayResultHandlerBloc extends Bloc<WalletPayResultHandlerEvent, WalletPayResultHandlerState> {
  final IWalletPayRepository walletPayRepository;
  Timer? _timer;

  WalletPayResultHandlerBloc({
    required this.walletPayRepository,
  }) : super(WalletPayResultHandlerState.initialize()) {
    on<_Initialize>(_initialize);
    on<_StartPolling>(_startPolling);
    on<_UpdateRemaining>(_updateRemaining);
    on<_ResultReceived>(_resultReceived);
    on<_StopPolling>(_stopPolling);
    on<_Timeout>(_timeout);
    on<_SubmitTimeoutResult>(_submitTimeoutResult);
  }

  Future<void> _initialize(_Initialize e, Emitter<WalletPayResultHandlerState> emit) async {
    emit(state.copyWith(correlationId: e.correlationId));
    add(const WalletPayResultHandlerEvent.startPolling());
  }

  Future<void> _startPolling(_StartPolling e, Emitter<WalletPayResultHandlerState> emit) async {
    emit(state.copyWith(loadingState: WalletPayResultHandlerStateIds.loading));
    if (state.correlationId != null) {
      await _handlePollingLogic();
      _timer = Timer.periodic(const Duration(seconds: 3), (timer) async {
        if (state.remaining <= 0) {
          add(const WalletPayResultHandlerEvent.timeout());
        } else {
          await _handlePollingLogic();
        }
      });
    } else {
      emit(state.copyWith(loadingState: WalletPayResultHandlerStateIds.failure));
    }
  }

  Future<void> _handlePollingLogic() async {
    if (state.remaining <= 0) {
      add(const WalletPayResultHandlerEvent.timeout());
    } else {
      final result = (await walletPayRepository.getWalletPayResults(
        correlationId: state.correlationId!,
      ))
          .fold((l) => null, (r) => r);

      if (result?.tofuResult != null) {
        add(WalletPayResultHandlerEvent.resultReceived(result: result));
      } else {
        add(const WalletPayResultHandlerEvent.updateRemaining());
      }
    }
  }

  Future<void> _updateRemaining(_UpdateRemaining e, Emitter<WalletPayResultHandlerState> emit) async {
    emit(state.copyWith(remaining: state.remaining - 1));
  }

  Future<void> _resultReceived(_ResultReceived e, Emitter<WalletPayResultHandlerState> emit) async {
    add(const WalletPayResultHandlerEvent.stopPolling());
    emit(state.copyWith(result: e.result, loadingState: WalletPayResultHandlerStateIds.complete));
  }

  Future<void> _timeout(_Timeout e, Emitter<WalletPayResultHandlerState> emit) async {
    add(const WalletPayResultHandlerEvent.stopPolling());
    add(const WalletPayResultHandlerEvent.submitTimeoutResult());
    emit(state.copyWith(loadingState: WalletPayResultHandlerStateIds.timeout));
  }

  Future<void> _stopPolling(_StopPolling e, Emitter<WalletPayResultHandlerState> emit) async {
    _timer?.cancel();
    _timer = null;
  }

  Future<void> _submitTimeoutResult(_SubmitTimeoutResult e, Emitter<WalletPayResultHandlerState> emit) async {
    unawaited(
      walletPayRepository.sendTokenProvisioningResults(
        bosResult: BosResult.approved.getValue(),
        tofuResult: TofuResult.timeout.getValue(),
        gmaTPCorrelationId: state.correlationId ?? '',
      ),
    );
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
