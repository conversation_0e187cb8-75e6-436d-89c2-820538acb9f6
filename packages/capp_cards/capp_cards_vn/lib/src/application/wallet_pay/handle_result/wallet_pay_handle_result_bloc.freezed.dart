// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_pay_handle_result_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$WalletPayResultHandlerEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String correlationId) initialize,
    required TResult Function() startPolling,
    required TResult Function() updateRemaining,
    required TResult Function(WalletPayFinalResultInfo? result) resultReceived,
    required TResult Function() stopPolling,
    required TResult Function() timeout,
    required TResult Function() submitTimeoutResult,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String correlationId)? initialize,
    TResult? Function()? startPolling,
    TResult? Function()? updateRemaining,
    TResult? Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult? Function()? stopPolling,
    TResult? Function()? timeout,
    TResult? Function()? submitTimeoutResult,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String correlationId)? initialize,
    TResult Function()? startPolling,
    TResult Function()? updateRemaining,
    TResult Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult Function()? stopPolling,
    TResult Function()? timeout,
    TResult Function()? submitTimeoutResult,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_StartPolling value) startPolling,
    required TResult Function(_UpdateRemaining value) updateRemaining,
    required TResult Function(_ResultReceived value) resultReceived,
    required TResult Function(_StopPolling value) stopPolling,
    required TResult Function(_Timeout value) timeout,
    required TResult Function(_SubmitTimeoutResult value) submitTimeoutResult,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_StartPolling value)? startPolling,
    TResult? Function(_UpdateRemaining value)? updateRemaining,
    TResult? Function(_ResultReceived value)? resultReceived,
    TResult? Function(_StopPolling value)? stopPolling,
    TResult? Function(_Timeout value)? timeout,
    TResult? Function(_SubmitTimeoutResult value)? submitTimeoutResult,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_StartPolling value)? startPolling,
    TResult Function(_UpdateRemaining value)? updateRemaining,
    TResult Function(_ResultReceived value)? resultReceived,
    TResult Function(_StopPolling value)? stopPolling,
    TResult Function(_Timeout value)? timeout,
    TResult Function(_SubmitTimeoutResult value)? submitTimeoutResult,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletPayResultHandlerEventCopyWith<$Res> {
  factory $WalletPayResultHandlerEventCopyWith(
          WalletPayResultHandlerEvent value,
          $Res Function(WalletPayResultHandlerEvent) then) =
      _$WalletPayResultHandlerEventCopyWithImpl<$Res,
          WalletPayResultHandlerEvent>;
}

/// @nodoc
class _$WalletPayResultHandlerEventCopyWithImpl<$Res,
        $Val extends WalletPayResultHandlerEvent>
    implements $WalletPayResultHandlerEventCopyWith<$Res> {
  _$WalletPayResultHandlerEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call({String correlationId});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$WalletPayResultHandlerEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? correlationId = null,
  }) {
    return _then(_$_Initialize(
      correlationId: null == correlationId
          ? _value.correlationId
          : correlationId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.correlationId});

  @override
  final String correlationId;

  @override
  String toString() {
    return 'WalletPayResultHandlerEvent.initialize(correlationId: $correlationId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.correlationId, correlationId) ||
                other.correlationId == correlationId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, correlationId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String correlationId) initialize,
    required TResult Function() startPolling,
    required TResult Function() updateRemaining,
    required TResult Function(WalletPayFinalResultInfo? result) resultReceived,
    required TResult Function() stopPolling,
    required TResult Function() timeout,
    required TResult Function() submitTimeoutResult,
  }) {
    return initialize(correlationId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String correlationId)? initialize,
    TResult? Function()? startPolling,
    TResult? Function()? updateRemaining,
    TResult? Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult? Function()? stopPolling,
    TResult? Function()? timeout,
    TResult? Function()? submitTimeoutResult,
  }) {
    return initialize?.call(correlationId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String correlationId)? initialize,
    TResult Function()? startPolling,
    TResult Function()? updateRemaining,
    TResult Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult Function()? stopPolling,
    TResult Function()? timeout,
    TResult Function()? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(correlationId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_StartPolling value) startPolling,
    required TResult Function(_UpdateRemaining value) updateRemaining,
    required TResult Function(_ResultReceived value) resultReceived,
    required TResult Function(_StopPolling value) stopPolling,
    required TResult Function(_Timeout value) timeout,
    required TResult Function(_SubmitTimeoutResult value) submitTimeoutResult,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_StartPolling value)? startPolling,
    TResult? Function(_UpdateRemaining value)? updateRemaining,
    TResult? Function(_ResultReceived value)? resultReceived,
    TResult? Function(_StopPolling value)? stopPolling,
    TResult? Function(_Timeout value)? timeout,
    TResult? Function(_SubmitTimeoutResult value)? submitTimeoutResult,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_StartPolling value)? startPolling,
    TResult Function(_UpdateRemaining value)? updateRemaining,
    TResult Function(_ResultReceived value)? resultReceived,
    TResult Function(_StopPolling value)? stopPolling,
    TResult Function(_Timeout value)? timeout,
    TResult Function(_SubmitTimeoutResult value)? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements WalletPayResultHandlerEvent {
  const factory _Initialize({required final String correlationId}) =
      _$_Initialize;

  String get correlationId;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_StartPollingCopyWith<$Res> {
  factory _$$_StartPollingCopyWith(
          _$_StartPolling value, $Res Function(_$_StartPolling) then) =
      __$$_StartPollingCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_StartPollingCopyWithImpl<$Res>
    extends _$WalletPayResultHandlerEventCopyWithImpl<$Res, _$_StartPolling>
    implements _$$_StartPollingCopyWith<$Res> {
  __$$_StartPollingCopyWithImpl(
      _$_StartPolling _value, $Res Function(_$_StartPolling) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_StartPolling implements _StartPolling {
  const _$_StartPolling();

  @override
  String toString() {
    return 'WalletPayResultHandlerEvent.startPolling()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_StartPolling);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String correlationId) initialize,
    required TResult Function() startPolling,
    required TResult Function() updateRemaining,
    required TResult Function(WalletPayFinalResultInfo? result) resultReceived,
    required TResult Function() stopPolling,
    required TResult Function() timeout,
    required TResult Function() submitTimeoutResult,
  }) {
    return startPolling();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String correlationId)? initialize,
    TResult? Function()? startPolling,
    TResult? Function()? updateRemaining,
    TResult? Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult? Function()? stopPolling,
    TResult? Function()? timeout,
    TResult? Function()? submitTimeoutResult,
  }) {
    return startPolling?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String correlationId)? initialize,
    TResult Function()? startPolling,
    TResult Function()? updateRemaining,
    TResult Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult Function()? stopPolling,
    TResult Function()? timeout,
    TResult Function()? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (startPolling != null) {
      return startPolling();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_StartPolling value) startPolling,
    required TResult Function(_UpdateRemaining value) updateRemaining,
    required TResult Function(_ResultReceived value) resultReceived,
    required TResult Function(_StopPolling value) stopPolling,
    required TResult Function(_Timeout value) timeout,
    required TResult Function(_SubmitTimeoutResult value) submitTimeoutResult,
  }) {
    return startPolling(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_StartPolling value)? startPolling,
    TResult? Function(_UpdateRemaining value)? updateRemaining,
    TResult? Function(_ResultReceived value)? resultReceived,
    TResult? Function(_StopPolling value)? stopPolling,
    TResult? Function(_Timeout value)? timeout,
    TResult? Function(_SubmitTimeoutResult value)? submitTimeoutResult,
  }) {
    return startPolling?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_StartPolling value)? startPolling,
    TResult Function(_UpdateRemaining value)? updateRemaining,
    TResult Function(_ResultReceived value)? resultReceived,
    TResult Function(_StopPolling value)? stopPolling,
    TResult Function(_Timeout value)? timeout,
    TResult Function(_SubmitTimeoutResult value)? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (startPolling != null) {
      return startPolling(this);
    }
    return orElse();
  }
}

abstract class _StartPolling implements WalletPayResultHandlerEvent {
  const factory _StartPolling() = _$_StartPolling;
}

/// @nodoc
abstract class _$$_UpdateRemainingCopyWith<$Res> {
  factory _$$_UpdateRemainingCopyWith(
          _$_UpdateRemaining value, $Res Function(_$_UpdateRemaining) then) =
      __$$_UpdateRemainingCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_UpdateRemainingCopyWithImpl<$Res>
    extends _$WalletPayResultHandlerEventCopyWithImpl<$Res, _$_UpdateRemaining>
    implements _$$_UpdateRemainingCopyWith<$Res> {
  __$$_UpdateRemainingCopyWithImpl(
      _$_UpdateRemaining _value, $Res Function(_$_UpdateRemaining) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_UpdateRemaining implements _UpdateRemaining {
  const _$_UpdateRemaining();

  @override
  String toString() {
    return 'WalletPayResultHandlerEvent.updateRemaining()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_UpdateRemaining);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String correlationId) initialize,
    required TResult Function() startPolling,
    required TResult Function() updateRemaining,
    required TResult Function(WalletPayFinalResultInfo? result) resultReceived,
    required TResult Function() stopPolling,
    required TResult Function() timeout,
    required TResult Function() submitTimeoutResult,
  }) {
    return updateRemaining();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String correlationId)? initialize,
    TResult? Function()? startPolling,
    TResult? Function()? updateRemaining,
    TResult? Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult? Function()? stopPolling,
    TResult? Function()? timeout,
    TResult? Function()? submitTimeoutResult,
  }) {
    return updateRemaining?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String correlationId)? initialize,
    TResult Function()? startPolling,
    TResult Function()? updateRemaining,
    TResult Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult Function()? stopPolling,
    TResult Function()? timeout,
    TResult Function()? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (updateRemaining != null) {
      return updateRemaining();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_StartPolling value) startPolling,
    required TResult Function(_UpdateRemaining value) updateRemaining,
    required TResult Function(_ResultReceived value) resultReceived,
    required TResult Function(_StopPolling value) stopPolling,
    required TResult Function(_Timeout value) timeout,
    required TResult Function(_SubmitTimeoutResult value) submitTimeoutResult,
  }) {
    return updateRemaining(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_StartPolling value)? startPolling,
    TResult? Function(_UpdateRemaining value)? updateRemaining,
    TResult? Function(_ResultReceived value)? resultReceived,
    TResult? Function(_StopPolling value)? stopPolling,
    TResult? Function(_Timeout value)? timeout,
    TResult? Function(_SubmitTimeoutResult value)? submitTimeoutResult,
  }) {
    return updateRemaining?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_StartPolling value)? startPolling,
    TResult Function(_UpdateRemaining value)? updateRemaining,
    TResult Function(_ResultReceived value)? resultReceived,
    TResult Function(_StopPolling value)? stopPolling,
    TResult Function(_Timeout value)? timeout,
    TResult Function(_SubmitTimeoutResult value)? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (updateRemaining != null) {
      return updateRemaining(this);
    }
    return orElse();
  }
}

abstract class _UpdateRemaining implements WalletPayResultHandlerEvent {
  const factory _UpdateRemaining() = _$_UpdateRemaining;
}

/// @nodoc
abstract class _$$_ResultReceivedCopyWith<$Res> {
  factory _$$_ResultReceivedCopyWith(
          _$_ResultReceived value, $Res Function(_$_ResultReceived) then) =
      __$$_ResultReceivedCopyWithImpl<$Res>;
  @useResult
  $Res call({WalletPayFinalResultInfo? result});
}

/// @nodoc
class __$$_ResultReceivedCopyWithImpl<$Res>
    extends _$WalletPayResultHandlerEventCopyWithImpl<$Res, _$_ResultReceived>
    implements _$$_ResultReceivedCopyWith<$Res> {
  __$$_ResultReceivedCopyWithImpl(
      _$_ResultReceived _value, $Res Function(_$_ResultReceived) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? result = freezed,
  }) {
    return _then(_$_ResultReceived(
      result: freezed == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as WalletPayFinalResultInfo?,
    ));
  }
}

/// @nodoc

class _$_ResultReceived implements _ResultReceived {
  const _$_ResultReceived({required this.result});

  @override
  final WalletPayFinalResultInfo? result;

  @override
  String toString() {
    return 'WalletPayResultHandlerEvent.resultReceived(result: $result)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ResultReceived &&
            (identical(other.result, result) || other.result == result));
  }

  @override
  int get hashCode => Object.hash(runtimeType, result);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ResultReceivedCopyWith<_$_ResultReceived> get copyWith =>
      __$$_ResultReceivedCopyWithImpl<_$_ResultReceived>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String correlationId) initialize,
    required TResult Function() startPolling,
    required TResult Function() updateRemaining,
    required TResult Function(WalletPayFinalResultInfo? result) resultReceived,
    required TResult Function() stopPolling,
    required TResult Function() timeout,
    required TResult Function() submitTimeoutResult,
  }) {
    return resultReceived(result);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String correlationId)? initialize,
    TResult? Function()? startPolling,
    TResult? Function()? updateRemaining,
    TResult? Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult? Function()? stopPolling,
    TResult? Function()? timeout,
    TResult? Function()? submitTimeoutResult,
  }) {
    return resultReceived?.call(result);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String correlationId)? initialize,
    TResult Function()? startPolling,
    TResult Function()? updateRemaining,
    TResult Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult Function()? stopPolling,
    TResult Function()? timeout,
    TResult Function()? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (resultReceived != null) {
      return resultReceived(result);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_StartPolling value) startPolling,
    required TResult Function(_UpdateRemaining value) updateRemaining,
    required TResult Function(_ResultReceived value) resultReceived,
    required TResult Function(_StopPolling value) stopPolling,
    required TResult Function(_Timeout value) timeout,
    required TResult Function(_SubmitTimeoutResult value) submitTimeoutResult,
  }) {
    return resultReceived(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_StartPolling value)? startPolling,
    TResult? Function(_UpdateRemaining value)? updateRemaining,
    TResult? Function(_ResultReceived value)? resultReceived,
    TResult? Function(_StopPolling value)? stopPolling,
    TResult? Function(_Timeout value)? timeout,
    TResult? Function(_SubmitTimeoutResult value)? submitTimeoutResult,
  }) {
    return resultReceived?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_StartPolling value)? startPolling,
    TResult Function(_UpdateRemaining value)? updateRemaining,
    TResult Function(_ResultReceived value)? resultReceived,
    TResult Function(_StopPolling value)? stopPolling,
    TResult Function(_Timeout value)? timeout,
    TResult Function(_SubmitTimeoutResult value)? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (resultReceived != null) {
      return resultReceived(this);
    }
    return orElse();
  }
}

abstract class _ResultReceived implements WalletPayResultHandlerEvent {
  const factory _ResultReceived(
      {required final WalletPayFinalResultInfo? result}) = _$_ResultReceived;

  WalletPayFinalResultInfo? get result;
  @JsonKey(ignore: true)
  _$$_ResultReceivedCopyWith<_$_ResultReceived> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_StopPollingCopyWith<$Res> {
  factory _$$_StopPollingCopyWith(
          _$_StopPolling value, $Res Function(_$_StopPolling) then) =
      __$$_StopPollingCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_StopPollingCopyWithImpl<$Res>
    extends _$WalletPayResultHandlerEventCopyWithImpl<$Res, _$_StopPolling>
    implements _$$_StopPollingCopyWith<$Res> {
  __$$_StopPollingCopyWithImpl(
      _$_StopPolling _value, $Res Function(_$_StopPolling) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_StopPolling implements _StopPolling {
  const _$_StopPolling();

  @override
  String toString() {
    return 'WalletPayResultHandlerEvent.stopPolling()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_StopPolling);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String correlationId) initialize,
    required TResult Function() startPolling,
    required TResult Function() updateRemaining,
    required TResult Function(WalletPayFinalResultInfo? result) resultReceived,
    required TResult Function() stopPolling,
    required TResult Function() timeout,
    required TResult Function() submitTimeoutResult,
  }) {
    return stopPolling();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String correlationId)? initialize,
    TResult? Function()? startPolling,
    TResult? Function()? updateRemaining,
    TResult? Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult? Function()? stopPolling,
    TResult? Function()? timeout,
    TResult? Function()? submitTimeoutResult,
  }) {
    return stopPolling?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String correlationId)? initialize,
    TResult Function()? startPolling,
    TResult Function()? updateRemaining,
    TResult Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult Function()? stopPolling,
    TResult Function()? timeout,
    TResult Function()? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (stopPolling != null) {
      return stopPolling();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_StartPolling value) startPolling,
    required TResult Function(_UpdateRemaining value) updateRemaining,
    required TResult Function(_ResultReceived value) resultReceived,
    required TResult Function(_StopPolling value) stopPolling,
    required TResult Function(_Timeout value) timeout,
    required TResult Function(_SubmitTimeoutResult value) submitTimeoutResult,
  }) {
    return stopPolling(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_StartPolling value)? startPolling,
    TResult? Function(_UpdateRemaining value)? updateRemaining,
    TResult? Function(_ResultReceived value)? resultReceived,
    TResult? Function(_StopPolling value)? stopPolling,
    TResult? Function(_Timeout value)? timeout,
    TResult? Function(_SubmitTimeoutResult value)? submitTimeoutResult,
  }) {
    return stopPolling?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_StartPolling value)? startPolling,
    TResult Function(_UpdateRemaining value)? updateRemaining,
    TResult Function(_ResultReceived value)? resultReceived,
    TResult Function(_StopPolling value)? stopPolling,
    TResult Function(_Timeout value)? timeout,
    TResult Function(_SubmitTimeoutResult value)? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (stopPolling != null) {
      return stopPolling(this);
    }
    return orElse();
  }
}

abstract class _StopPolling implements WalletPayResultHandlerEvent {
  const factory _StopPolling() = _$_StopPolling;
}

/// @nodoc
abstract class _$$_TimeoutCopyWith<$Res> {
  factory _$$_TimeoutCopyWith(
          _$_Timeout value, $Res Function(_$_Timeout) then) =
      __$$_TimeoutCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_TimeoutCopyWithImpl<$Res>
    extends _$WalletPayResultHandlerEventCopyWithImpl<$Res, _$_Timeout>
    implements _$$_TimeoutCopyWith<$Res> {
  __$$_TimeoutCopyWithImpl(_$_Timeout _value, $Res Function(_$_Timeout) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Timeout implements _Timeout {
  const _$_Timeout();

  @override
  String toString() {
    return 'WalletPayResultHandlerEvent.timeout()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Timeout);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String correlationId) initialize,
    required TResult Function() startPolling,
    required TResult Function() updateRemaining,
    required TResult Function(WalletPayFinalResultInfo? result) resultReceived,
    required TResult Function() stopPolling,
    required TResult Function() timeout,
    required TResult Function() submitTimeoutResult,
  }) {
    return timeout();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String correlationId)? initialize,
    TResult? Function()? startPolling,
    TResult? Function()? updateRemaining,
    TResult? Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult? Function()? stopPolling,
    TResult? Function()? timeout,
    TResult? Function()? submitTimeoutResult,
  }) {
    return timeout?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String correlationId)? initialize,
    TResult Function()? startPolling,
    TResult Function()? updateRemaining,
    TResult Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult Function()? stopPolling,
    TResult Function()? timeout,
    TResult Function()? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (timeout != null) {
      return timeout();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_StartPolling value) startPolling,
    required TResult Function(_UpdateRemaining value) updateRemaining,
    required TResult Function(_ResultReceived value) resultReceived,
    required TResult Function(_StopPolling value) stopPolling,
    required TResult Function(_Timeout value) timeout,
    required TResult Function(_SubmitTimeoutResult value) submitTimeoutResult,
  }) {
    return timeout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_StartPolling value)? startPolling,
    TResult? Function(_UpdateRemaining value)? updateRemaining,
    TResult? Function(_ResultReceived value)? resultReceived,
    TResult? Function(_StopPolling value)? stopPolling,
    TResult? Function(_Timeout value)? timeout,
    TResult? Function(_SubmitTimeoutResult value)? submitTimeoutResult,
  }) {
    return timeout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_StartPolling value)? startPolling,
    TResult Function(_UpdateRemaining value)? updateRemaining,
    TResult Function(_ResultReceived value)? resultReceived,
    TResult Function(_StopPolling value)? stopPolling,
    TResult Function(_Timeout value)? timeout,
    TResult Function(_SubmitTimeoutResult value)? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (timeout != null) {
      return timeout(this);
    }
    return orElse();
  }
}

abstract class _Timeout implements WalletPayResultHandlerEvent {
  const factory _Timeout() = _$_Timeout;
}

/// @nodoc
abstract class _$$_SubmitTimeoutResultCopyWith<$Res> {
  factory _$$_SubmitTimeoutResultCopyWith(_$_SubmitTimeoutResult value,
          $Res Function(_$_SubmitTimeoutResult) then) =
      __$$_SubmitTimeoutResultCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SubmitTimeoutResultCopyWithImpl<$Res>
    extends _$WalletPayResultHandlerEventCopyWithImpl<$Res,
        _$_SubmitTimeoutResult>
    implements _$$_SubmitTimeoutResultCopyWith<$Res> {
  __$$_SubmitTimeoutResultCopyWithImpl(_$_SubmitTimeoutResult _value,
      $Res Function(_$_SubmitTimeoutResult) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SubmitTimeoutResult implements _SubmitTimeoutResult {
  const _$_SubmitTimeoutResult();

  @override
  String toString() {
    return 'WalletPayResultHandlerEvent.submitTimeoutResult()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SubmitTimeoutResult);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String correlationId) initialize,
    required TResult Function() startPolling,
    required TResult Function() updateRemaining,
    required TResult Function(WalletPayFinalResultInfo? result) resultReceived,
    required TResult Function() stopPolling,
    required TResult Function() timeout,
    required TResult Function() submitTimeoutResult,
  }) {
    return submitTimeoutResult();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String correlationId)? initialize,
    TResult? Function()? startPolling,
    TResult? Function()? updateRemaining,
    TResult? Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult? Function()? stopPolling,
    TResult? Function()? timeout,
    TResult? Function()? submitTimeoutResult,
  }) {
    return submitTimeoutResult?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String correlationId)? initialize,
    TResult Function()? startPolling,
    TResult Function()? updateRemaining,
    TResult Function(WalletPayFinalResultInfo? result)? resultReceived,
    TResult Function()? stopPolling,
    TResult Function()? timeout,
    TResult Function()? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (submitTimeoutResult != null) {
      return submitTimeoutResult();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_StartPolling value) startPolling,
    required TResult Function(_UpdateRemaining value) updateRemaining,
    required TResult Function(_ResultReceived value) resultReceived,
    required TResult Function(_StopPolling value) stopPolling,
    required TResult Function(_Timeout value) timeout,
    required TResult Function(_SubmitTimeoutResult value) submitTimeoutResult,
  }) {
    return submitTimeoutResult(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_StartPolling value)? startPolling,
    TResult? Function(_UpdateRemaining value)? updateRemaining,
    TResult? Function(_ResultReceived value)? resultReceived,
    TResult? Function(_StopPolling value)? stopPolling,
    TResult? Function(_Timeout value)? timeout,
    TResult? Function(_SubmitTimeoutResult value)? submitTimeoutResult,
  }) {
    return submitTimeoutResult?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_StartPolling value)? startPolling,
    TResult Function(_UpdateRemaining value)? updateRemaining,
    TResult Function(_ResultReceived value)? resultReceived,
    TResult Function(_StopPolling value)? stopPolling,
    TResult Function(_Timeout value)? timeout,
    TResult Function(_SubmitTimeoutResult value)? submitTimeoutResult,
    required TResult orElse(),
  }) {
    if (submitTimeoutResult != null) {
      return submitTimeoutResult(this);
    }
    return orElse();
  }
}

abstract class _SubmitTimeoutResult implements WalletPayResultHandlerEvent {
  const factory _SubmitTimeoutResult() = _$_SubmitTimeoutResult;
}

/// @nodoc
mixin _$WalletPayResultHandlerState {
  WalletPayResultHandlerStateIds get loadingState =>
      throw _privateConstructorUsedError;
  int get remaining => throw _privateConstructorUsedError;
  String? get correlationId => throw _privateConstructorUsedError;
  WalletPayFinalResultInfo? get result => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WalletPayResultHandlerStateCopyWith<WalletPayResultHandlerState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletPayResultHandlerStateCopyWith<$Res> {
  factory $WalletPayResultHandlerStateCopyWith(
          WalletPayResultHandlerState value,
          $Res Function(WalletPayResultHandlerState) then) =
      _$WalletPayResultHandlerStateCopyWithImpl<$Res,
          WalletPayResultHandlerState>;
  @useResult
  $Res call(
      {WalletPayResultHandlerStateIds loadingState,
      int remaining,
      String? correlationId,
      WalletPayFinalResultInfo? result});
}

/// @nodoc
class _$WalletPayResultHandlerStateCopyWithImpl<$Res,
        $Val extends WalletPayResultHandlerState>
    implements $WalletPayResultHandlerStateCopyWith<$Res> {
  _$WalletPayResultHandlerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? remaining = null,
    Object? correlationId = freezed,
    Object? result = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as WalletPayResultHandlerStateIds,
      remaining: null == remaining
          ? _value.remaining
          : remaining // ignore: cast_nullable_to_non_nullable
              as int,
      correlationId: freezed == correlationId
          ? _value.correlationId
          : correlationId // ignore: cast_nullable_to_non_nullable
              as String?,
      result: freezed == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as WalletPayFinalResultInfo?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_WalletPayResultHandlerStateCopyWith<$Res>
    implements $WalletPayResultHandlerStateCopyWith<$Res> {
  factory _$$_WalletPayResultHandlerStateCopyWith(
          _$_WalletPayResultHandlerState value,
          $Res Function(_$_WalletPayResultHandlerState) then) =
      __$$_WalletPayResultHandlerStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {WalletPayResultHandlerStateIds loadingState,
      int remaining,
      String? correlationId,
      WalletPayFinalResultInfo? result});
}

/// @nodoc
class __$$_WalletPayResultHandlerStateCopyWithImpl<$Res>
    extends _$WalletPayResultHandlerStateCopyWithImpl<$Res,
        _$_WalletPayResultHandlerState>
    implements _$$_WalletPayResultHandlerStateCopyWith<$Res> {
  __$$_WalletPayResultHandlerStateCopyWithImpl(
      _$_WalletPayResultHandlerState _value,
      $Res Function(_$_WalletPayResultHandlerState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? remaining = null,
    Object? correlationId = freezed,
    Object? result = freezed,
  }) {
    return _then(_$_WalletPayResultHandlerState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as WalletPayResultHandlerStateIds,
      remaining: null == remaining
          ? _value.remaining
          : remaining // ignore: cast_nullable_to_non_nullable
              as int,
      correlationId: freezed == correlationId
          ? _value.correlationId
          : correlationId // ignore: cast_nullable_to_non_nullable
              as String?,
      result: freezed == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as WalletPayFinalResultInfo?,
    ));
  }
}

/// @nodoc

class _$_WalletPayResultHandlerState implements _WalletPayResultHandlerState {
  const _$_WalletPayResultHandlerState(
      {required this.loadingState,
      required this.remaining,
      this.correlationId,
      this.result});

  @override
  final WalletPayResultHandlerStateIds loadingState;
  @override
  final int remaining;
  @override
  final String? correlationId;
  @override
  final WalletPayFinalResultInfo? result;

  @override
  String toString() {
    return 'WalletPayResultHandlerState(loadingState: $loadingState, remaining: $remaining, correlationId: $correlationId, result: $result)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_WalletPayResultHandlerState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.remaining, remaining) ||
                other.remaining == remaining) &&
            (identical(other.correlationId, correlationId) ||
                other.correlationId == correlationId) &&
            (identical(other.result, result) || other.result == result));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, loadingState, remaining, correlationId, result);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_WalletPayResultHandlerStateCopyWith<_$_WalletPayResultHandlerState>
      get copyWith => __$$_WalletPayResultHandlerStateCopyWithImpl<
          _$_WalletPayResultHandlerState>(this, _$identity);
}

abstract class _WalletPayResultHandlerState
    implements WalletPayResultHandlerState {
  const factory _WalletPayResultHandlerState(
      {required final WalletPayResultHandlerStateIds loadingState,
      required final int remaining,
      final String? correlationId,
      final WalletPayFinalResultInfo? result}) = _$_WalletPayResultHandlerState;

  @override
  WalletPayResultHandlerStateIds get loadingState;
  @override
  int get remaining;
  @override
  String? get correlationId;
  @override
  WalletPayFinalResultInfo? get result;
  @override
  @JsonKey(ignore: true)
  _$$_WalletPayResultHandlerStateCopyWith<_$_WalletPayResultHandlerState>
      get copyWith => throw _privateConstructorUsedError;
}
