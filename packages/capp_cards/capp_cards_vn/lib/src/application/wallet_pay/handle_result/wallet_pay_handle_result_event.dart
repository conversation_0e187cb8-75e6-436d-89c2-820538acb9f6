part of 'wallet_pay_handle_result_bloc.dart';

@freezed
class WalletPayResultHandlerEvent with _$WalletPayResultHandlerEvent {
  const factory WalletPayResultHandlerEvent.initialize({required String correlationId}) = _Initialize;
  const factory WalletPayResultHandlerEvent.startPolling() = _StartPolling;
  const factory WalletPayResultHandlerEvent.updateRemaining() = _UpdateRemaining;
  const factory WalletPayResultHandlerEvent.resultReceived({required WalletPayFinalResultInfo? result}) =
      _ResultReceived;
  const factory WalletPayResultHandlerEvent.stopPolling() = _StopPolling;
  const factory WalletPayResultHandlerEvent.timeout() = _Timeout;
  const factory WalletPayResultHandlerEvent.submitTimeoutResult() = _SubmitTimeoutResult;
}
