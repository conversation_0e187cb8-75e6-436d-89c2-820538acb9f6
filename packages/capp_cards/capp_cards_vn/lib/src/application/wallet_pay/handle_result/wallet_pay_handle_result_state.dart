part of 'wallet_pay_handle_result_bloc.dart';

enum WalletPayResultHandlerStateIds {
  initial,
  loading,
  complete,
  timeout,
  failure,
}

@freezed
class WalletPayResultHandlerState with _$WalletPayResultHandlerState {
  const factory WalletPayResultHandlerState({
    required WalletPayResultHandlerStateIds loadingState,
    required int remaining,
    String? correlationId,
    WalletPayFinalResultInfo? result,
  }) = _WalletPayResultHandlerState;

  factory WalletPayResultHandlerState.initialize() => const WalletPayResultHandlerState(
        loadingState: WalletPayResultHandlerStateIds.initial,
        remaining: 10,
      );
}
