import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../capp_cards.dart';

part 'wallet_pay_send_result_bloc.freezed.dart';
part 'wallet_pay_send_result_event.dart';
part 'wallet_pay_send_result_state.dart';

class WalletPaySendResultBloc extends Bloc<WalletPaySendResultEvent, WalletPaySendResultState> {
  final IWalletPayRepository walletPayRepository;

  WalletPaySendResultBloc({
    required this.walletPayRepository,
  }) : super(WalletPaySendResultState.initialize()) {
    on<_Initialize>(_initialize);
    on<_SendResult>(_sendResult);
  }

  void _initialize(_Initialize e, Emitter<WalletPaySendResultState> emit) {}
  Future<void> _sendResult(_SendResult e, Emitter<WalletPaySendResultState> emit) async {
    emit(state.copyWith(isLoading: true, isLoaded: false, isError: false));
    final bosResult = e.bosResult;
    final tofuResult = e.tofuResult.isEmpty ? null : e.tofuResult;
    final gmaTPCorrelationId = e.gmaTPCorrelationId;
    final response = await walletPayRepository.sendTokenProvisioningResults(
      bosResult: bosResult,
      tofuResult: tofuResult,
      gmaTPCorrelationId: gmaTPCorrelationId,
    );
    emit(
      response.fold((l) {
        return state.copyWith(
          isLoading: false,
          isLoaded: true,
          isError: true,
        );
      }, (r) {
        return state.copyWith(
          isLoading: false,
          isLoaded: true,
          isError: false,
        );
      }),
    );
  }
}
