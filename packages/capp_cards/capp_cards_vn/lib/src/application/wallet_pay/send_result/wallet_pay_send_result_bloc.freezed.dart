// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_pay_send_result_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$WalletPaySendResultEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(
            String gmaTPCorrelationId, String bosResult, String tofuResult)
        send,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(
            String gmaTPCorrelationId, String bosResult, String tofuResult)?
        send,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(
            String gmaTPCorrelationId, String bosResult, String tofuResult)?
        send,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SendResult value) send,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SendResult value)? send,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SendResult value)? send,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletPaySendResultEventCopyWith<$Res> {
  factory $WalletPaySendResultEventCopyWith(WalletPaySendResultEvent value,
          $Res Function(WalletPaySendResultEvent) then) =
      _$WalletPaySendResultEventCopyWithImpl<$Res, WalletPaySendResultEvent>;
}

/// @nodoc
class _$WalletPaySendResultEventCopyWithImpl<$Res,
        $Val extends WalletPaySendResultEvent>
    implements $WalletPaySendResultEventCopyWith<$Res> {
  _$WalletPaySendResultEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$WalletPaySendResultEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize();

  @override
  String toString() {
    return 'WalletPaySendResultEvent.initialize()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Initialize);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(
            String gmaTPCorrelationId, String bosResult, String tofuResult)
        send,
  }) {
    return initialize();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(
            String gmaTPCorrelationId, String bosResult, String tofuResult)?
        send,
  }) {
    return initialize?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(
            String gmaTPCorrelationId, String bosResult, String tofuResult)?
        send,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SendResult value) send,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SendResult value)? send,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SendResult value)? send,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements WalletPaySendResultEvent {
  const factory _Initialize() = _$_Initialize;
}

/// @nodoc
abstract class _$$_SendResultCopyWith<$Res> {
  factory _$$_SendResultCopyWith(
          _$_SendResult value, $Res Function(_$_SendResult) then) =
      __$$_SendResultCopyWithImpl<$Res>;
  @useResult
  $Res call({String gmaTPCorrelationId, String bosResult, String tofuResult});
}

/// @nodoc
class __$$_SendResultCopyWithImpl<$Res>
    extends _$WalletPaySendResultEventCopyWithImpl<$Res, _$_SendResult>
    implements _$$_SendResultCopyWith<$Res> {
  __$$_SendResultCopyWithImpl(
      _$_SendResult _value, $Res Function(_$_SendResult) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gmaTPCorrelationId = null,
    Object? bosResult = null,
    Object? tofuResult = null,
  }) {
    return _then(_$_SendResult(
      null == gmaTPCorrelationId
          ? _value.gmaTPCorrelationId
          : gmaTPCorrelationId // ignore: cast_nullable_to_non_nullable
              as String,
      null == bosResult
          ? _value.bosResult
          : bosResult // ignore: cast_nullable_to_non_nullable
              as String,
      null == tofuResult
          ? _value.tofuResult
          : tofuResult // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_SendResult implements _SendResult {
  const _$_SendResult(this.gmaTPCorrelationId, this.bosResult, this.tofuResult);

  @override
  final String gmaTPCorrelationId;
  @override
  final String bosResult;
  @override
  final String tofuResult;

  @override
  String toString() {
    return 'WalletPaySendResultEvent.send(gmaTPCorrelationId: $gmaTPCorrelationId, bosResult: $bosResult, tofuResult: $tofuResult)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SendResult &&
            (identical(other.gmaTPCorrelationId, gmaTPCorrelationId) ||
                other.gmaTPCorrelationId == gmaTPCorrelationId) &&
            (identical(other.bosResult, bosResult) ||
                other.bosResult == bosResult) &&
            (identical(other.tofuResult, tofuResult) ||
                other.tofuResult == tofuResult));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, gmaTPCorrelationId, bosResult, tofuResult);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SendResultCopyWith<_$_SendResult> get copyWith =>
      __$$_SendResultCopyWithImpl<_$_SendResult>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(
            String gmaTPCorrelationId, String bosResult, String tofuResult)
        send,
  }) {
    return send(gmaTPCorrelationId, bosResult, tofuResult);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(
            String gmaTPCorrelationId, String bosResult, String tofuResult)?
        send,
  }) {
    return send?.call(gmaTPCorrelationId, bosResult, tofuResult);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(
            String gmaTPCorrelationId, String bosResult, String tofuResult)?
        send,
    required TResult orElse(),
  }) {
    if (send != null) {
      return send(gmaTPCorrelationId, bosResult, tofuResult);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SendResult value) send,
  }) {
    return send(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SendResult value)? send,
  }) {
    return send?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SendResult value)? send,
    required TResult orElse(),
  }) {
    if (send != null) {
      return send(this);
    }
    return orElse();
  }
}

abstract class _SendResult implements WalletPaySendResultEvent {
  const factory _SendResult(final String gmaTPCorrelationId,
      final String bosResult, final String tofuResult) = _$_SendResult;

  String get gmaTPCorrelationId;
  String get bosResult;
  String get tofuResult;
  @JsonKey(ignore: true)
  _$$_SendResultCopyWith<_$_SendResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$WalletPaySendResultState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isLoaded => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WalletPaySendResultStateCopyWith<WalletPaySendResultState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletPaySendResultStateCopyWith<$Res> {
  factory $WalletPaySendResultStateCopyWith(WalletPaySendResultState value,
          $Res Function(WalletPaySendResultState) then) =
      _$WalletPaySendResultStateCopyWithImpl<$Res, WalletPaySendResultState>;
  @useResult
  $Res call({bool isLoading, bool isLoaded, bool isError});
}

/// @nodoc
class _$WalletPaySendResultStateCopyWithImpl<$Res,
        $Val extends WalletPaySendResultState>
    implements $WalletPaySendResultStateCopyWith<$Res> {
  _$WalletPaySendResultStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isLoaded = null,
    Object? isError = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoaded: null == isLoaded
          ? _value.isLoaded
          : isLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_WalletPaySendResultStateCopyWith<$Res>
    implements $WalletPaySendResultStateCopyWith<$Res> {
  factory _$$_WalletPaySendResultStateCopyWith(
          _$_WalletPaySendResultState value,
          $Res Function(_$_WalletPaySendResultState) then) =
      __$$_WalletPaySendResultStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isLoading, bool isLoaded, bool isError});
}

/// @nodoc
class __$$_WalletPaySendResultStateCopyWithImpl<$Res>
    extends _$WalletPaySendResultStateCopyWithImpl<$Res,
        _$_WalletPaySendResultState>
    implements _$$_WalletPaySendResultStateCopyWith<$Res> {
  __$$_WalletPaySendResultStateCopyWithImpl(_$_WalletPaySendResultState _value,
      $Res Function(_$_WalletPaySendResultState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isLoaded = null,
    Object? isError = null,
  }) {
    return _then(_$_WalletPaySendResultState(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoaded: null == isLoaded
          ? _value.isLoaded
          : isLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_WalletPaySendResultState implements _WalletPaySendResultState {
  const _$_WalletPaySendResultState(
      {this.isLoading = false, this.isLoaded = false, this.isError = false});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isLoaded;
  @override
  @JsonKey()
  final bool isError;

  @override
  String toString() {
    return 'WalletPaySendResultState(isLoading: $isLoading, isLoaded: $isLoaded, isError: $isError)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_WalletPaySendResultState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoaded, isLoaded) ||
                other.isLoaded == isLoaded) &&
            (identical(other.isError, isError) || other.isError == isError));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, isLoaded, isError);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_WalletPaySendResultStateCopyWith<_$_WalletPaySendResultState>
      get copyWith => __$$_WalletPaySendResultStateCopyWithImpl<
          _$_WalletPaySendResultState>(this, _$identity);
}

abstract class _WalletPaySendResultState implements WalletPaySendResultState {
  const factory _WalletPaySendResultState(
      {final bool isLoading,
      final bool isLoaded,
      final bool isError}) = _$_WalletPaySendResultState;

  @override
  bool get isLoading;
  @override
  bool get isLoaded;
  @override
  bool get isError;
  @override
  @JsonKey(ignore: true)
  _$$_WalletPaySendResultStateCopyWith<_$_WalletPaySendResultState>
      get copyWith => throw _privateConstructorUsedError;
}
