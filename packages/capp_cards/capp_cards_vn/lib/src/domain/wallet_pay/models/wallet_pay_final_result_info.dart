import 'package:equatable/equatable.dart';

class WalletPayFinalResultInfo extends Equatable {
  final String? gmaTPCorrelationId;
  final String? panReferenceId;
  final String? tokenRequestorId;
  final String? tokenReferenceId;
  final String? panLast4;
  final String? deviceId;
  final String? walletAccountId;
  final String? tofuResult;
  final String? finalResult;

  const WalletPayFinalResultInfo({
    required this.gmaTPCorrelationId,
    required this.panReferenceId,
    required this.tokenRequestorId,
    required this.tokenReferenceId,
    required this.panLast4,
    required this.deviceId,
    required this.walletAccountId,
    required this.tofuResult,
    required this.finalResult,
  });

  @override
  List<Object?> get props => [
        gmaTPCorrelationId,
        panReferenceId,
        tokenRequestorId,
        tokenReferenceId,
        panLast4,
        deviceId,
        walletAccountId,
        tofuResult,
        finalResult,
      ];

  WalletPayFinalResultInfo copyWith({
    String? gmaTPCorrelationId,
    String? panReferenceId,
    String? tokenRequestorId,
    String? tokenReferenceId,
    String? panLast4,
    String? deviceId,
    String? walletAccountId,
    String? tofuResult,
    String? finalResult,
  }) {
    return WalletPayFinalResultInfo(
      gmaTPCorrelationId: gmaTPCorrelationId ?? this.gmaTPCorrelationId,
      panReferenceId: panReferenceId ?? this.panReferenceId,
      tokenRequestorId: tokenRequestorId ?? this.tokenRequestorId,
      tokenReferenceId: tokenReferenceId ?? this.tokenReferenceId,
      panLast4: panLast4 ?? this.panLast4,
      deviceId: deviceId ?? this.deviceId,
      walletAccountId: walletAccountId ?? this.walletAccountId,
      tofuResult: tofuResult ?? this.tofuResult,
      finalResult: finalResult ?? this.finalResult,
    );
  }
}
