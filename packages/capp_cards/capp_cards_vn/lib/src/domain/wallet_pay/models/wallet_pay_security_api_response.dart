import 'package:equatable/equatable.dart';

class WalletPaySecurityApiResponse extends Equatable {
  final String? stringCode;
  final String? message;
  final num? code;

  const WalletPaySecurityApiResponse({
    required this.stringCode,
    required this.message,
    required this.code,
  });

  @override
  List<Object?> get props => [
        stringCode,
        message,
        code,
      ];

  WalletPaySecurityApiResponse copyWith({
    String? stringCode,
    String? message,
    num? code,
  }) {
    return WalletPaySecurityApiResponse(
      stringCode: stringCode ?? this.stringCode,
      message: message ?? this.message,
      code: code ?? this.code,
    );
  }
}

enum BosResult {
  approved,
  rejected,
  canceled,
  timeout,
  failedToFinish,
}

extension BosResultExt on BosResult {
  String getValue() {
    switch (this) {
      case BosResult.approved:
        return 'APPROVED';
      case BosResult.rejected:
        return 'REJECTED';
      case BosResult.canceled:
        return 'CANCELED';
      case BosResult.timeout:
        return 'TIME_OUT';
      case BosResult.failedToFinish:
        return 'FAILED_TO_FINISH';
    }
  }
}

enum TofuResult {
  timeout,
}

extension TofuResultExt on TofuResult {
  String getValue() {
    switch (this) {
      case TofuResult.timeout:
        return 'TIME_OUT';
    }
  }
}
