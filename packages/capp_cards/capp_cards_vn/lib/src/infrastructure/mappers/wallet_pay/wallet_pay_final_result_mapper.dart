import 'dart:core';

import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../../capp_cards.dart';

extension WalletPayFinalResultMapper on api_models.CardSecurityCardProvisioningDataResultInfoDTO {
  WalletPayFinalResultInfo toDomain() {
    return WalletPayFinalResultInfo(
      gmaTPCorrelationId: gmaTPCorrelationId,
      panReferenceId: panReferenceId,
      tokenRequestorId: tokenRequestorId,
      tokenReferenceId: tokenReferenceId,
      panLast4: panLast4,
      deviceId: deviceId,
      walletAccountId: walletAccountId,
      tofuResult: tofuResult,
      finalResult: finalResult,
    );
  }
}
