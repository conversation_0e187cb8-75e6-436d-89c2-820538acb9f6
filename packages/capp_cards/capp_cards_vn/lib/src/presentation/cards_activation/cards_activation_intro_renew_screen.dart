import 'package:capp_cards_core/capp_cards_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../application/card_allow_recurring/card_allow_recurring_bloc.dart';
import '../../application/card_subsequent_status/card_subsequent_status_bloc.dart';
import '../widgets/recurring_consent_widget.dart';

class CardsActivationIntroRenewScreen extends StatelessWidget with RouteWrapper {
  final CardsActivationIntroRenewRouteArgs arguments;

  const CardsActivationIntroRenewScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<CardSubsequentStatusBloc>(
          create: (ctx) => context.get<CardSubsequentStatusBloc>()
            ..add(CardSubsequentStatusEvent.getCardSubsequentStatus(contractCode: arguments.contractNumber)),
        ),
        BlocProvider<CardAllowRecurringBloc>(
          create: (ctx) => context.get<CardAllowRecurringBloc>()..add(const CardAllowRecurringEvent.initialize()),
        ),
      ],
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('KoyalScaffold'),
      appBar: KoyalAppBar(
        key: const Key('KoyalAppBar'),
        title: L10nCappCards.of(context).cappCardsCardActivationIntroRenewScreenTitle,
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Container(
      color: HciColors.supplementary25,
      child: StickyFooterShadow.slivers(
        key: const Key('__cardActivateInsurIntroScrollView__'),
        slivers: [
          SliverToBoxAdapter(
            child: _mainHeading(context),
          ),
          SliverToBoxAdapter(
            child: _content(context),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(
              height: KoyalPadding.paddingXSmall,
            ),
          ),
          SliverToBoxAdapter(
            child: _recurringConsent(context),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(
              height: KoyalPadding.paddingLarge,
            ),
          ),
        ],
        footer: _buildStickyBottomButton(context),
      ),
    );
  }

  Widget _mainHeading(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        bottomLeft: Radius.circular(KoyalPadding.paddingNormal),
        bottomRight: Radius.circular(KoyalPadding.paddingNormal),
      ),
      child: MainHeading(
        title: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2Title,
        subtitle: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2Subtitle,
        avatar: SizedBox.square(
          child: SvgPicture.asset(
            'assets/icons/ic_card_black.svg',
            package: 'capp_cards_core',
          ),
        ),
      ),
    );
  }

  Widget _recurringConsent(BuildContext context) {
    return BlocBuilder<CardSubsequentStatusBloc, CardSubsequentStatusState>(
      builder: (context, state) {
        if (state.isSubsequentCard == false) {
          return const SizedBox.shrink();
        } else {
          return RecurringConsentWidget(
            onChanged: (value) {
              context.read<CardAllowRecurringBloc>().add(CardAllowRecurringEvent.setAllowRecurring(value: value));
            },
          );
        }
      },
    );
  }

  Widget _content(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: KoyalPadding.paddingNormal, vertical: KoyalPadding.paddingSmall),
      decoration: BoxDecoration(
        color: ColorTheme.of(context).backgroundColor,
        borderRadius: BorderRadius.circular(KoyalPadding.paddingNormal),
      ),
      child: Column(
        children: [
          SectionHeading(title: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2MakeSure),
          KoyalStepsInformationGuide(
            parentPadding: KoyalPadding.paddingNormal * 2,
            items: [
              KoyalStepsInformationGuideData(
                title: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2Enter4Digits,
                desc: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2Enter4DigitsDesc,
                isChecked: false,
                localIcon: SvgPicture.asset(
                  'assets/icons/ic_cc_last_digits.svg',
                  package: 'capp_cards_core',
                ),
              ),
              KoyalStepsInformationGuideData(
                title: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2EnterCard,
                desc: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2EnterCardDesc,
                isChecked: false,
                localIcon: SvgPicture.asset(
                  'assets/icons/ic_card_spending_limit_step_2.svg',
                  package: 'capp_cards_core',
                ),
              ),
              KoyalStepsInformationGuideData(
                title: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2VerifyOtp,
                desc: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2VerifyOtpDesc,
                isChecked: false,
                localIcon: SvgPicture.asset(
                  'assets/icons/ic_phone.svg',
                  package: 'capp_cards_core',
                ),
              ),
              KoyalStepsInformationGuideData(
                title: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2SetPin,
                desc: L10nCappCards.of(context).cappCardsCardActivationIntroRenew2SetPinDesc,
                isChecked: false,
                localIcon: SvgPicture.asset(
                  'assets/icons/ic_lock_bg.svg',
                  package: 'capp_cards_core',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStickyBottomButton(BuildContext context) {
    return VerticalButtonsLayout(
      primaryButton: PrimaryButton(
        key: const Key('Continue'),
        text: L10nCappCards.of(context).cappCardsCardActivationIntroRenewButtonContinue,
        onPressed: () {
          final value = context.read<CardAllowRecurringBloc>().state.isAllowRecurring;
          context.navigator.pushFromCards(
            screen: 'CardsActivationRenewScreen',
            arguments: CardsActivationRenewRouteArgs(
              contractNumber: arguments.contractNumber,
              isAllowRecurring: value,
              card: arguments.cardInfo,
            ),
          );
        },
      ),
    );
  }
}
