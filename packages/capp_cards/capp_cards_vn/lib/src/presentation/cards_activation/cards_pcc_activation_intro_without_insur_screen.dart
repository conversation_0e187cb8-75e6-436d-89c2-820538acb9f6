import 'package:capp_cards_core/capp_cards_core.dart' as core;
import 'package:capp_cards_core/capp_cards_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../application/card_allow_recurring/card_allow_recurring_bloc.dart';
import '../../application/card_subsequent_status/card_subsequent_status_bloc.dart';
import '../../routes/card_pcc_activate_without_insur_intro_route_args.dart';
import '../widgets/recurring_consent_widget.dart';

class CardsPccActivationIntroWithoutInsurScreen extends StatelessWidget with RouteWrapper {
  final CardPccActivateWithoutInsurIntroRouteArgs arguments;
  CardsPccActivationIntroWithoutInsurScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<CardSubsequentStatusBloc>(
          create: (ctx) => context.get<CardSubsequentStatusBloc>()
            ..add(CardSubsequentStatusEvent.getCardSubsequentStatus(contractCode: arguments.contractNumber)),
        ),
        BlocProvider<CardAllowRecurringBloc>(
          create: (ctx) => context.get<CardAllowRecurringBloc>()..add(const CardAllowRecurringEvent.initialize()),
        ),
      ],
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return FirstShown(
      onShown: () {
        context
            .get<ICardTrackingService>()
            .trackCardActivationIntroScreenView(contractNumber: arguments.contractNumber);
      },
      child: KoyalScaffold(
        key: const Key('__cardsActivationIntroScaffold__'),
        appBar: KoyalAppBar(
          key: const Key('__cardsActivationIntroAppBar__'),
          title: context.strings.cappCardsCardActivationIntroTitle,
          onGoBack: () {
            context
                .get<core.ICardTrackingService>()
                .trackClickBackCardActivationIntro(contractNumber: arguments.contractNumber);
            context.navigator.maybePop();
          },
        ),
        body: buildActivationIntroBody(context),
      ),
    );
  }

  Widget buildActivationIntroBody(BuildContext context) {
    return buildBody(context, isInProgress: false);
  }

  Widget buildBody(BuildContext context, {required bool isInProgress}) {
    return Container(
      color: HciColors.supplementary25,
      child: StickyFooterShadow.slivers(
        key: const Key('__cardActivateInsurIntroScrollView__'),
        slivers: [
          SliverToBoxAdapter(
            child: _mainHeading(context),
          ),
          SliverToBoxAdapter(
            child: _content(context),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(
              height: KoyalPadding.paddingXSmall,
            ),
          ),
          SliverToBoxAdapter(
            child: _recurringConsent(context),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(
              height: KoyalPadding.paddingLarge,
            ),
          ),
        ],
        footer: _buildStickyBottomButton(context, isInProgress),
      ),
    );
  }

  Widget _mainHeading(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        bottomLeft: Radius.circular(KoyalPadding.paddingNormal),
        bottomRight: Radius.circular(KoyalPadding.paddingNormal),
      ),
      child: MainHeading(
        title: L10nCappCards.of(context).cappCardsActivateCc,
        subtitle: L10nCappCards.of(context).cappCardsActivateCcWithInsur,
        avatar: SizedBox.square(
          child: SvgPicture.asset(
            'assets/icons/ic_card_black.svg',
            package: 'capp_cards_core',
          ),
        ),
      ),
    );
  }

  Widget _recurringConsent(BuildContext context) {
    return BlocBuilder<CardSubsequentStatusBloc, CardSubsequentStatusState>(
      builder: (context, state) {
        if (state.isSubsequentCard == false) {
          return const SizedBox.shrink();
        } else {
          return RecurringConsentWidget(
            onChanged: (value) {
              context.read<CardAllowRecurringBloc>().add(CardAllowRecurringEvent.setAllowRecurring(value: value));
            },
          );
        }
      },
    );
  }

  Widget _content(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: KoyalPadding.paddingNormal, vertical: KoyalPadding.paddingSmall),
      decoration: BoxDecoration(
        color: ColorTheme.of(context).backgroundColor,
        borderRadius: BorderRadius.circular(KoyalPadding.paddingNormal),
      ),
      child: Column(
        children: [
          SectionHeading(title: L10nCappCards.of(context).cappCardsStepToActivate),
          KoyalStepsInformationGuide(
            parentPadding: KoyalPadding.paddingNormal * 2,
            items: [
              KoyalStepsInformationGuideData(
                title: L10nCappCards.of(context).cappCardsEnterLastDigitsTitle,
                desc: L10nCappCards.of(context).cappCardsEnterLastDigitsContent,
                isChecked: false,
                localIcon: SvgPicture.asset(
                  'assets/icons/ic_cc_last_digits.svg',
                  package: 'capp_cards_core',
                ),
              ),
              KoyalStepsInformationGuideData(
                title: L10nCappCards.of(context).cappCardsVerifyOtpCode,
                desc: L10nCappCards.of(context).cappCardsVerifyOtpCodeContent,
                isChecked: false,
                localIcon: SvgPicture.asset(
                  'assets/icons/ic_phone.svg',
                  package: 'capp_cards_core',
                ),
              ),
              KoyalStepsInformationGuideData(
                title: L10nCappCards.of(context).cappCardsSetCardPin,
                desc: L10nCappCards.of(context).cappCardsSetCardPinContent,
                isChecked: false,
                localIcon: SvgPicture.asset(
                  'assets/icons/ic_lock_bg.svg',
                  package: 'capp_cards_core',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStickyBottomButton(BuildContext context, bool isInProgress) {
    return Container(
      color: ColorTheme.of(context).backgroundColor,
      child: VerticalButtonsLayout(
        primaryButton: PrimaryButton(
          text: context.strings.cappCardsCardActivationIntroButtonContinue,
          isInProgress: isInProgress,
          onPressed: () {
            context
                .get<ICardTrackingService>()
                .trackClickContinueCardActivationIntro(contractNumber: arguments.contractNumber);
            onContinue(context, arguments.card, arguments.contractNumber);
          },
        ),
      ),
    );
  }

  void onContinue(BuildContext context, CardInfo cardInfo, String contractNumber) {
    navigateToFourDigitsCardInputValidation(context, cardInfo, contractNumber);
  }

  void navigateToFourDigitsCardInputValidation(BuildContext context, CardInfo cardInfo, String contractNumber) {
    context
        .get<ICardTrackingService>()
        .trackCardActivationLast4DigitNumberScreenView(contractNumber: arguments.contractNumber);
    final value = context.read<CardAllowRecurringBloc>().state.isAllowRecurring;
    context.navigator.pushReplacementFromCards(
      screen: 'CardsActivationScreen',
      arguments: CardsActivationRouteArgs(
        card: cardInfo,
        contractNumber: contractNumber,
        isAllowRecurring: value,
      ),
    );
  }
}
