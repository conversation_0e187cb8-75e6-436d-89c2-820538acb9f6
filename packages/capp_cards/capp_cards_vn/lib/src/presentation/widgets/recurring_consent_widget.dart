import 'package:capp_cards_core/capp_cards_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../capp_cards.dart';

class RecurringConsentWidget extends StatefulWidget {
  final ValueChanged<bool> onChanged;
  final bool initialValue;

  const RecurringConsentWidget({
    Key? key,
    required this.onChanged,
    this.initialValue = true,
  }) : super(key: key);

  @override
  State<RecurringConsentWidget> createState() => _RecurringConsentWidgetState();
}

class _RecurringConsentWidgetState extends State<RecurringConsentWidget> {
  late bool _value;
  final GlobalKey<CustomSwitchState> _toggleKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
  }

  Future<void> _handleToggleAttempt(bool currentValue) async {
    if (currentValue) {
      final confirmed = await showRecurringConfirmationPopup(context);
      if (!confirmed) return;
    }

    setState(() => _value = !currentValue);

    widget.onChanged(!currentValue);
  }

  @override
  Widget build(BuildContext context) {
    final theme = ColorTheme.of(context);
    final l10n = L10nCappCards.of(context);

    return KoyalPadding.normalHorizontal(
      child: Row(
        children: [
          Expanded(
            child: KoyalText.caption2(
              _value ? l10n.cappCardsRecurringConsentContentOn : l10n.cappCardsRecurringConsentContentOff,
              color: theme.foreground60Color,
              softwrap: true,
            ),
          ),
          const SizedBox(width: KoyalPadding.paddingXSmall),
          SlideToggle(
            key: _toggleKey,
            value: _value,
            onToggleTap: _handleToggleAttempt,
          ),
        ],
      ),
    );
  }
}
