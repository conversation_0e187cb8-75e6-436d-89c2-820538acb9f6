import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:gma_vault/gma_vault.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../capp_config_core.dart';
import '../application/deeplinks/pending_deeplink_handler.dart';
import '../application/tracking/app_deeplink_tracking_cubit.dart';
import '../infrastructure/environment_config/environment_config_repository.dart';
import '../infrastructure/inapp_tracking/default_inapp_personalized_tracking_service.dart';
import '../infrastructure/inapp_tracking/default_inapp_tracking_service.dart';
import '../initialization/capp_core_initialization_service.dart';

class CappConfig extends ChildPackage {
  @override
  void onNewTranslations(Translations translations) =>
      L10nCappUi.translations[translations.contentLanguage] = translations.forPackage('capp_ui');

  final String googlePlayId;
  final String appStoreId;
  final double defaultPositionLatitude;
  final double defaultPositionLongitude;
  final String appTraitValue;
  final ConfigurationRoot environmentConfigSeed;
  final String deeplinkUrl;
  final String dynamicLinkUrl;
  final String? onelinkUrl;
  final String? appGalleryId;

  CappConfig({
    required this.googlePlayId,
    required this.appStoreId,
    required this.defaultPositionLatitude,
    required this.defaultPositionLongitude,
    required this.appTraitValue,
    required this.environmentConfigSeed,
    required this.deeplinkUrl,
    required this.dynamicLinkUrl,
    this.onelinkUrl,
    this.appGalleryId,
  });

  @override
  List<KoyalRoute> get routes => [
        KoyalRoute(
          runtimeType,
          WebViewScreen,
          'general/web_view',
          (contex, arguments) => WebViewScreen(args: arguments! as WebViewArguments),
        ),
        KoyalRoute(
          runtimeType,
          GpsScreen,
          'general/gps',
          (contex, arguments) => BlocProvider<GpsBloc>(create: (context) => context.get<GpsBloc>(), child: GpsScreen()),
        ),
        KoyalRoute(
          runtimeType,
          OhNoScreen,
          'oh-no',
          (context, arguments) => CappOhNoScreen(arguments: arguments! as UnhappyPathRouteArguments),
        ),
        KoyalRoute(
          runtimeType,
          PendingActionsScreen,
          PendingActionsScreen.routeName,
          (context, args) => PendingActionsScreen(args: args as PendingActionsScreenArguments?).wrappedRoute(context),
          bussinesRouteName: 'Pending actions',
        ),
      ];

  @override
  void registerDependencies(GetIt container) {
    container.get<ITranslationOverrideRepository>().registerTranslation(() {
      L10nCappUi.showKeys = !L10nCappUi.showKeys;
    });

    registerRemoteConfig(container);

    container.registerLazySingleton<IAppVersion>(
      () => AppVersion(
        appVersionFlag: KoyalFeatureFlag.cappMinRequiredVersion,
        platformService: container<IPlatformService>(),
        featureFlagRepository: container<IFeatureFlagRepository>(),
        remoteConfigRepository: container<IRemoteConfigRepository>(),
        googlePlayId: googlePlayId,
        appStoreId: appStoreId,
        appGalleryId: appGalleryId,
        isHuawei: isHuawei,
      ),
    );

    registerPersistentStorages(container);
    registerPlatformServices(container);
    registerAnimation(container);

    registerPermissions(container);
    registerInAppTrackingService(container);
    registerVault(container);

    container.registerFactory<IUrlNavigator>(UrlNavigator.new);

    registerRepositories(container);
    _registerBlocs(container);

    container
      ..registerLazySingleton<GmaVault>(
        () => GmaVault(shared: container<IGmaVault>()),
      )
      ..registerLazySingleton<IDefaultPositionProvider>(
        () => DefaultPositionProvider(
          defaultLatitude: defaultPositionLatitude,
          defaultLongitude: defaultPositionLongitude,
        ),
      );
  }

  @override
  IModuleInitializationService getInitializationService(GetIt container) {
    return CappCoreInitializationService(
      remoteConfigRepository: container<IRemoteConfigRepository>(),
      deeplinkService: container<IDeeplinkService>(),
      apiLocker: container<ApiLocker>(),
      featureFlagInitService: container<IFeatureFlagInitService>(),
      appTrait: appTraitValue,
      retrofitCache: container<RetrofitCache>(instanceName: CappApi.cache),
      androidPlayReferrerService: container<AndroidPlayReferrerService>(),
      a2aIntentReceiverService:
          container.isRegistered<IA2AIntentReceiverService>() ? container<IA2AIntentReceiverService>() : null,
    );
  }

  void _registerBlocs(GetIt c) {
    c
      ..registerTrackingLazySingleton(
        () => DeeplinkResolverBloc(
          deeplinkService: c<IDeeplinkService>(),
          newPendingActionsRepository: c<INewPendingActionsRepository>(),
          utmCaptureRepository: c<IUTMCaptureRepository>(),
          currentUserRepository: c<ICurrentUserRepository>(),
          identityRepository: c<IIdentityRepository>(),
          deeplinkUrl: deeplinkUrl,
          qrDecodeExpandRepository: c<IDeeplinkDecodeExpandRepository>(),
          enumerationsRepository: c<IEnumerationsRepository>(),
          authCompleteRepository: c<IAuthCompleteRepository>(),
          pendingDeeplinkHandler: c<PendingDeeplinkHandler>(),
        ),
      )
      ..registerTrackingFactory(
        () => CategoryFilterBloc(
          repository: c<IEnumerationsRepository>(),
          localizationRepository: c<ILocalizationRepository>(),
          productSearchRepository: c<IProductSearchHistoryRepository>(),
        ),
      )
      ..registerTrackingLazySingleton<ThemeModeCubit>(() => ThemeModeCubit(logger: c<Logger>()))
      ..registerTrackingLazySingleton<SecureContentCubit>(SecureContentCubit.new)
      ..registerTrackingLazySingleton<PendingActionsBloc>(
        () => PendingActionsBloc(
          repo: c<INewPendingActionsRepository>(),
          identityRepository: c<IIdentityRepository>(),
          currentUserRepository: c<ICurrentUserRepository>(),
          featureFlagRepository: c<IFeatureFlagRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => AppDeeplinkTrackingCubit(
          trackingService: c<CappTrackingService>(),
          logger: c<Logger>(),
          appStateRepository: c<IAppStateRepository>(),
        ),
      );
  }

  void registerPersistentStorages(GetIt c) {
    c.registerLazySingleton(() => PermissionsStorage(storage: c<GmaStorageProvider>()));
  }

  void registerAnimation(GetIt c) {}

  void registerPlatformServices(GetIt c) {}

  bool get isInDebugMode {
    return !kReleaseMode;
  }

  bool get isHuawei => appGalleryId != null;

  void registerRepositories(GetIt c) {
    c
      ..registerLazySingleton<IEnvironmentConfigRepository>(
        () => EnvironmentConfigRepository(
          logger: c<Logger>(),
          api: c<EnvironmentConfigApi>(),
          environmentConfigSeed: environmentConfigSeed,
        ),
      )
      ..registerLazySingleton<IMaintenanceWindowRepository>(
        () => MaintenanceWindowRepository(
          remoteConfigRepository: c<IRemoteConfigRepository>(),
          currentUserRepository: c<ICurrentUserRepository>(),
          logger: c<Logger>(),
        ),
      )
      ..registerLazySingleton<PermissionsRepository>(
        () => PermissionsRepository(
          storage: c<PermissionsStorage>(),
        ),
      )
      ..registerLazySingleton<IEnumerationsRepository>(
        () => EnumerationsRepository(api: c<EnumerationsApi>(), logger: c<Logger>()),
      )
      ..registerLazySingleton<IDiagnosticsRepository>(
        () => DiagnosticsRepository(diagnosticsApi: c<DiagnosticsApi>(), platformService: c<IPlatformService>()),
      )
      ..registerLazySingleton(
        () => DeeplinkCoordsStorage(
          storage: c<GmaStorageProvider>(),
        ),
      )
      ..registerTrackingLazySingleton<IFileRepository>(FileRepository.new)
      ..registerLazySingleton<IDeeplinkDecodeExpandRepository>(
        () => DeeplinkDecodeExpandRepository(
          apiExpand: c<IdDeeplinkExpandApi>(),
          apiDecode: c<IdDeeplinkDecodeApi>(),
          storage: c<DeeplinkCoordsStorage>(),
        ),
      );
  }

  void registerPermissions(GetIt c) {
    c.registerLazySingleton<IPermissionsService>(
      () => PermissionsService(
        permissionsRepository: c<PermissionsRepository>(),
      ),
    );
  }

  void registerInAppTrackingService(GetIt c) {
    c..registerLazySingleton<IInAppTracking>(DefaultInAppTrackingService.new)
    ..registerLazySingleton<IInAppPersonalizedTracking>(DefaultInAppPersonalizedTrackingService.new);
  }

  void registerVault(GetIt c) {}

  void registerRemoteConfig(GetIt c) {}
}
