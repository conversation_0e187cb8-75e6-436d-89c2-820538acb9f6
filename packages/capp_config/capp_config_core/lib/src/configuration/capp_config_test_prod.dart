import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:gma_vault/gma_vault.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../capp_config_core.dart';
import '../infrastructure/inapp_tracking/default_inapp_personalized_tracking_service.dart';
import '../infrastructure/inapp_tracking/default_inapp_tracking_service.dart';

class CappConfigTestProd extends CappConfigProd {
  CappConfigTestProd({
    required String appStoreId,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    required String imageBaseUrl,
    required String googlePlayId,
    required double defaultPositionLatitude,
    required double defaultPositionLongitude,
    required Map<String, dynamic> remoteConfigSeed,
    required ConfigurationRoot environmentConfigSeed,
    required String appTraitValue,
    super.appGalleryId,
  }) : super(
          appStoreId: appStoreId,
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          imageBaseUrl: imageBaseUrl,
          googlePlayId: googlePlayId,
          remoteConfigSeed: remoteConfigSeed,
          defaultPositionLatitude: defaultPositionLatitude,
          defaultPositionLongitude: defaultPositionLongitude,
          environmentConfigSeed: environmentConfigSeed,
          appTraitValue: appTraitValue,
        );

  @override
  Future<void> preRegisterDependencies(GetIt container) async {
    container
      ..registerSingleton<IGmaSecureStorage>(await GmaSecureStorage.asyncInit())
      ..registerSingleton<GmaStorageProvider>(
        SecureInMemoryStorageProvider(logger: container<Logger>()),
        instanceName: GmaStorageType.secure.name,
      )
      ..registerSingleton<GmaStorageProvider>(
        PersistantInMemoryStorageProvider(logger: container<Logger>()),
        instanceName: GmaStorageType.persistant.name,
      )
      ..registerSingleton<GmaStorageProvider>(
        SessionInMemoryStorageProvider(logger: container<Logger>()),
      );
  }

  @override
  void registerPermissions(GetIt c) {
    c.registerLazySingleton<IPermissionsService>(
      TestPermissionsService.new,
    );
  }

  @override
  void registerInAppTrackingService(GetIt c) {
    c
      ..registerLazySingleton<IInAppTracking>(DefaultInAppTrackingService.new)
      ..registerLazySingleton<IInAppPersonalizedTracking>(DefaultInAppPersonalizedTrackingService.new);
  }

  @override
  void registerPersistentStorages(GetIt c) {
    c.registerLazySingleton(() => PermissionsStorage(storage: c.get<ReactiveInMemoryStorageProvider>()));
  }

  @override
  void registerRepositories(GetIt c) {
    super.registerRepositories(c);

    if (c.isRegistered<IFileRepository>()) {
      c.unregister<IFileRepository>();
    }
    c.registerTrackingLazySingleton<IFileRepository>(FakeFileRepository.new);
  }

  @override
  void registerVault(GetIt c) {
    c.registerLazySingleton<IGmaVault>(GmaVaultTest.new);
  }
}
