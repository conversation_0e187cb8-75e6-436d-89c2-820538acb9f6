abstract class IInAppPersonalizedTracking {
  // Track when the in-app message is viewed.
  void trackInAppPersView({
    required String? msid,
    required int? templateId,
  });

  // Track when the in-app CTA is clicked.
  void trackInAppPersCtaClick({
    required String? msid,
    required int? templateId,
  });

  // Track when the in-app close action is clicked.
  void trackInAppPersCloseClick({
    required String? msid,
    required int? templateId,
  });
}
