import '../../domain/i_inapp_personalized_tracking.dart';

class DefaultInAppPersonalizedTrackingService implements IInAppPersonalizedTracking {
  @override
  void trackInAppPersCloseClick({String? inAppType, required String? msid, required int? templateId}) {
    // no-op
  }

  @override
  void trackInAppPersCtaClick({String? inAppType, required String? msid, required int? templateId}) {
    // no-op
  }

  @override
  void trackInAppPersView({String? inAppType, required String? msid, required int? templateId}) {
    // no-op
  }
}