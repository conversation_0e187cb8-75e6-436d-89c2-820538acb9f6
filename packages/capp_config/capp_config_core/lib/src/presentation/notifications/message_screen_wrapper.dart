// ignore_for_file: avoid_print, no_leading_underscores_for_local_identifiers

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_deals_core/capp_deals_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_messaging/koyal_messaging.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_config_core.dart';
import '../../application/deeplinks/pending_deeplink_handler.dart';

class MessageScreenWrapper extends StatelessWidget {
  final Widget child;

  const MessageScreenWrapper({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocListener<CustomInAppBloc, CustomInAppState>(
      listener: (context, state) async {
        await _displayInAppMessage(
          KoyalModular.of(context).rootNavigatorKey.currentState!.overlay!.context,
          state,
          state.msid,
        );
      },
      listenWhen: (previous, current) => previous.inAppMessage != current.inAppMessage && current.inAppMessage != null,
      child: MessagingListener(
        onReceived: (context, message, callback) {
          if (message != null && message.data != null) {
            switch (message.data!.notificationType) {
              case MessageType.authSignedInOnOtherDevice:
                context.navigateToLogoutScreen(LogoutReason.signedInOnOtherDevice);
                break;
              case MessageType.authLogoutPhoneUpdated:
                context.navigateToLogoutScreen(LogoutReason.phoneUpdated);
                break;
              case MessageType.authLogoutAccountDelete:
                context.navigateToLogoutScreen(LogoutReason.signOutAccountDeleted);
                break;
              case MessageType.identityIdentifyResult:
                context.get<PendingDeeplinkHandler>().setIsListeningFromAuth(isListening: true);
                _openDeeplink(context, '/identify-message-result', message);
                break;
              case MessageType.ongoingMaintenance:
                showScheduledMaintenanceDialog(context: context);
                break;
              default:
                break;
            }
          }
        },
        onTapped: (message) {
          print('onTapped: ${message?.title}');
          navigate(context: context, message: message!);
        },
        child: child,
      ),
    );
  }

  // !!ALERT!! this is called only when the notification is TAPPED, it is USELESS for data notifications
  static Future<Object?> navigate({BuildContext? context, required NotificationMessage message}) async {
    // ignore: deprecated_member_use
    final nav = context != null ? context.navigator : KoyalCore.I.to;
    Future<Object?> _openProduct() async {
      await nav!.pushFromPackage(
        package: 'CappDeals',
        screen: 'ProductDetailScreen',
        arguments: ProductDetailArguments(productId: message.data!.productId),
      );
      return nav.toMainScreen();
    }

    switch (message.data!.notificationType) {
      case MessageType.authSignedInOnOtherDevice:
        return context!.navigateToLogoutScreen(LogoutReason.signedInOnOtherDevice);
      case MessageType.authLogoutPhoneUpdated:
        return context!.navigateToLogoutScreen(LogoutReason.phoneUpdated);
      case MessageType.messagingInboxMessage:
        if (message.data!.inboxItemId != null) {
          _openDeeplink(context, '/inboxmsg', message);
        } else {
          _openDeeplink(context, '/inbox', message);
        }
        break;
      case MessageType.orderStateChanged:
        if (message.containsDataKey(MessageDataKeys.productIdDataKey) && message.data!.orderState == 'cancelled') {
          return _openProduct();
        }
        break;
      case MessageType.deeplink:
        if (message.data!.deeplink != null) {
          _openDeeplink(context, message.data!.deeplink!, message);
        }
        break;
      default:
        if (message.containsDataKey(MessageDataKeys.productIdDataKey)) {
          return _openProduct();
        } else if (message.containsDataKey(MessageDataKeys.inboxIdDataKey)) {
          _openDeeplink(context, '/inboxmsg', message);
          break;
        }
        print(message);
        break;
    }
    return null;
  }

  static void _openDeeplink(BuildContext? context, String link, NotificationMessage message) {
    context?.read<DeeplinkResolverBloc>().add(
          DeeplinkResolverEvent.openDeeplink(
            deeplink: DeeplinkModel(link: link, messageId: message.data!.msId, message: message),
          ),
        );
  }

  Future<void> _displayInAppMessage(
    BuildContext context,
    CustomInAppState state,
    String? msid,
  ) async {
    final inAppMessage = state.inAppMessage!;
    final inAppType = state.inAppType?.getInAppMessageType();
    final isPersonalize = inAppType == InAppMessageType.inAppPersonalized;

    void _trackCtaClick() {
      if (isPersonalize) {
        context.get<IInAppPersonalizedTracking>().trackInAppPersCtaClick(
          msid: msid,
          templateId: inAppMessage.templateId,
        );
      } else {
        context.get<IInAppTracking>().trackInAppCtaClick(
          msid: msid,
          templateId: inAppMessage.templateId,
        );
      }
    }

    void _trackCloseClick() {
      if (isPersonalize) {
        context.get<IInAppPersonalizedTracking>().trackInAppPersCloseClick(
          msid: msid,
          templateId: inAppMessage.templateId,
        );
      } else {
        context.get<IInAppTracking>().trackInAppCloseClick(
          msid: msid,
          templateId: inAppMessage.templateId,
        );
      }
    }

    void _trackView() {
      if (isPersonalize) {
        context.get<IInAppPersonalizedTracking>().trackInAppPersView(
          msid: msid,
          templateId: inAppMessage.templateId,
        );
      } else {
        context.get<IInAppTracking>().trackInAppView(
          msid: msid,
          templateId: inAppMessage.templateId,
        );
      }
    }

    void _onCtaPressed() {
      _trackCtaClick();

      Navigator.pop(context, true);

      if (inAppMessage.ctaButtonUrl != null && context.mounted) {
        context.get<IDeeplinkService>().deeplinkOrLaunch(
              inAppMessage.ctaButtonUrl!,
              context,
            );
      }
    }

    await showDialog(
      context: context,
      barrierDismissible: state.isDismissible,
      builder: (context) {
        return CustomInAppDialog(
          closeOnBackGesture: state.isDismissible,
          title: inAppMessage.title ?? '',
          bodyText: inAppMessage.bodyText,
          ctaButtonText: inAppMessage.ctaButtonText ?? '',
          onCtaPressed: _onCtaPressed,
          tertiaryCtaButtonText: inAppMessage.tertiaryCtaButtonText,
          onTertiaryPressed:
              inAppMessage.tertiaryCtaButtonText?.isNotEmpty ?? false
                  ? () => Navigator.pop(context)
                  : null,
          imageId: inAppMessage.imageId,
          onViewed: _trackView,
        );
      },
    ).then((value) {
      state.onClose?.call();
      // if the primary button was not clicked, track the close event
      if (value != true && context.mounted) {
        _trackCloseClick();
      }
    });
  }
}

class MessageScreenWrapperBuilder implements IMessageScreenWrapperBuilder {
  @override
  Widget build({required Widget child}) {
    return MessageScreenWrapper(child: child);
  }
}
