import 'package:a2a_intent_receiver/a2a_intent_receiver.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_config_core/capp_config_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui/capp_ui.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:get_it/get_it.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../infrastructure/inapp_tracking/inapp_personalize_tracking_vn.dart';
import '../infrastructure/inapp_tracking/inapp_tracking_vn.dart';
import '../infrastructure/permission_service/permissions_service_vn.dart';

class CappConfigProdVn extends CappConfigProd {
  CappConfigProdVn({
    required String appStoreId,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    required String imageBaseUrl,
    required Map<String, dynamic> remoteConfigSeed,
    required String googlePlayId,
    required double defaultPositionLatitude,
    required double defaultPositionLongitude,
    required ConfigurationRoot environmentConfigSeed,
    required String appTraitValue,
    FirebaseOptions? firebaseOptions,
  }) : super(
          appStoreId: appStoreId,
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          imageBaseUrl: imageBaseUrl,
          remoteConfigSeed: remoteConfigSeed,
          googlePlayId: googlePlayId,
          defaultPositionLatitude: defaultPositionLatitude,
          defaultPositionLongitude: defaultPositionLongitude,
          environmentConfigSeed: environmentConfigSeed,
          appTraitValue: appTraitValue,
          firebaseOptions: firebaseOptions,
        );

  @override
  void registerPermissions(GetIt c) {
    c.registerLazySingleton<IPermissionsService>(
      () => PermissionVnService(
        permissionsRepository: c<PermissionsRepository>(),
      ),
    );
  }

  @override
  void registerInAppTrackingService(GetIt c) {
    c
      ..registerFactory<IInAppTracking>(
        () => InAppTrackingVn(c<CappTrackingService>()),
      )
      ..registerLazySingleton<IInAppPersonalizedTracking>(
        () => InAppPersonalizedTrackingVn(c<CappTrackingService>()),
      );
  }

  @override
  void registerAnimation(GetIt c) {
    c.registerFactory<ILoadingWidgetFactory>(LoadingFactory.new);
  }

  @override
  void registerPlatformServices(GetIt c) {
    super.registerPlatformServices(c);

    c.registerLazySingleton<IA2AIntentReceiverService>(
      () => A2AIntentReceiverService(
        logger: c<Logger>(),
        intentReceiver: A2AIntentReceiver.instance,
        deeplinkService: c<IDeeplinkService>(),
      ),
    );
  }

  @override
  Future<void> preRegisterDependencies(GetIt container) {
    container.registerLazySingleton<PerformanceConfig>(
      () => PerformanceConfig(
        isFirebasePerformanceMonitoringEnabled: true,
        enabledTraces: [
          TraceType.appStart,
          TraceType.bod1Submission,
          TraceType.disbursementInfo,
          TraceType.documentPreparation,
          TraceType.homepageLoading,
          TraceType.instalmentCalculation,
          TraceType.login,
          TraceType.logout,
          TraceType.myLoans,
          TraceType.offerBanner,
          TraceType.otpSignature,
          TraceType.productOfferOtp,
          TraceType.productOfferCalculation,
        ],
      ),
    );
    return super.preRegisterDependencies(container);
  }
}
