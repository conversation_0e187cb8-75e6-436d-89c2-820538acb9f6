import 'package:capp_config_core/capp_config_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:koyal_shared/koyal_shared.dart';

class InAppPersonalizedTrackingVn implements IInAppPersonalizedTracking {
  final CappTrackingService _trackingService;

  InAppPersonalizedTrackingVn(this._trackingService);

  @override
  void trackInAppPersCloseClick({required String? msid, required int? templateId}) {
    _trackingService.trackInAppMessagePersEvent(
      event: KoyalEvent.sasInAppPersDismissClick,
      eventCategory: KoyalTrackingCategories.app,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: KoyalTrackingLabels.sasInAppPersDismissButton,
      msid: msid,
      templateId: templateId?.toString(),
    );
  }

  @override
  void trackInAppPersView({required String? msid, required int? templateId}) {
    _trackingService.trackInAppMessagePersEvent(
      event: KoyalEvent.sasInAppPersView,
      eventCategory: KoyalTrackingCategories.app,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: KoyalTrackingLabels.sasInAppPers,
      msid: msid,
      templateId: templateId?.toString(),
    );
  }

  @override
  void trackInAppPersCtaClick({required String? msid, required int? templateId}) {
    _trackingService.trackInAppMessagePersEvent(
      event: KoyalEvent.sasInAppPersContinueClick,
      eventCategory: KoyalTrackingCategories.app,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: KoyalTrackingLabels.sasInAppPersContinueButton,
      msid: msid,
      templateId: templateId?.toString(),
    );
  }
}