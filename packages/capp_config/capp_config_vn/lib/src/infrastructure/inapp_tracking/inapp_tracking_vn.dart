import 'package:capp_config_core/capp_config_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:koyal_shared/koyal_shared.dart';

class InAppTrackingVn implements IInAppTracking {
  final CappTrackingService _trackingService;

  InAppTrackingVn(this._trackingService);

  @override
  void trackInAppView({required String? msid, required int? templateId}) {
    _trackingService.trackEvent(
      event: KoyalEvent.sasInAppView,
      eventCategory: KoyalTrackingCategories.app,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: KoyalTrackingLabels.sasInApp,
      customDimensions: {
        TrackingProperties.propertyCdExternalMessageId: msid ?? '',
        TrackingProperties.propertyCdItemId: templateId.toString(),
      },
    );
  }

  @override
  void trackInAppCtaClick({required String? msid, required int? templateId}) {
    _trackingService.trackEvent(
      event: KoyalEvent.sasInAppCtaClick,
      eventCategory: KoyalTrackingCategories.app,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: KoyalTrackingLabels.sasInAppCtaBtn,
      customDimensions: {
        TrackingProperties.propertyCdExternalMessageId: msid ?? '',
        TrackingProperties.propertyCdItemId: templateId.toString(),
      },
    );
  }

  @override
  void trackInAppCloseClick({required String? msid, required int? templateId}) {
    _trackingService.trackEvent(
      event: KoyalEvent.sasInAppCloseClick,
      eventCategory: KoyalTrackingCategories.app,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: KoyalTrackingLabels.sasInAppCloseBtn,
      customDimensions: {
        TrackingProperties.propertyCdExternalMessageId: msid ?? '',
        TrackingProperties.propertyCdItemId: templateId.toString(),
      },
    );
  }
}
