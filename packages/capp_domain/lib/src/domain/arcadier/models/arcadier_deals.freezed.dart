// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'arcadier_deals.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ArcadierDeals {
  ArcadierDealsData? get data => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ArcadierDealsCopyWith<ArcadierDeals> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ArcadierDealsCopyWith<$Res> {
  factory $ArcadierDealsCopyWith(
          ArcadierDeals value, $Res Function(ArcadierDeals) then) =
      _$ArcadierDealsCopyWithImpl<$Res, ArcadierDeals>;
  @useResult
  $Res call({ArcadierDealsData? data});

  $ArcadierDealsDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$ArcadierDealsCopyWithImpl<$Res, $Val extends ArcadierDeals>
    implements $ArcadierDealsCopyWith<$Res> {
  _$ArcadierDealsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as ArcadierDealsData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ArcadierDealsDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $ArcadierDealsDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ArcadierDealsCopyWith<$Res>
    implements $ArcadierDealsCopyWith<$Res> {
  factory _$$_ArcadierDealsCopyWith(
          _$_ArcadierDeals value, $Res Function(_$_ArcadierDeals) then) =
      __$$_ArcadierDealsCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ArcadierDealsData? data});

  @override
  $ArcadierDealsDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$_ArcadierDealsCopyWithImpl<$Res>
    extends _$ArcadierDealsCopyWithImpl<$Res, _$_ArcadierDeals>
    implements _$$_ArcadierDealsCopyWith<$Res> {
  __$$_ArcadierDealsCopyWithImpl(
      _$_ArcadierDeals _value, $Res Function(_$_ArcadierDeals) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_$_ArcadierDeals(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as ArcadierDealsData?,
    ));
  }
}

/// @nodoc

class _$_ArcadierDeals implements _ArcadierDeals {
  _$_ArcadierDeals({required this.data});

  @override
  final ArcadierDealsData? data;

  @override
  String toString() {
    return 'ArcadierDeals(data: $data)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ArcadierDeals &&
            (identical(other.data, data) || other.data == data));
  }

  @override
  int get hashCode => Object.hash(runtimeType, data);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ArcadierDealsCopyWith<_$_ArcadierDeals> get copyWith =>
      __$$_ArcadierDealsCopyWithImpl<_$_ArcadierDeals>(this, _$identity);
}

abstract class _ArcadierDeals implements ArcadierDeals {
  factory _ArcadierDeals({required final ArcadierDealsData? data}) =
      _$_ArcadierDeals;

  @override
  ArcadierDealsData? get data;
  @override
  @JsonKey(ignore: true)
  _$$_ArcadierDealsCopyWith<_$_ArcadierDeals> get copyWith =>
      throw _privateConstructorUsedError;
}
