// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'arcadier_deals_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ArcadierDealsData {
  int get pageSize => throw _privateConstructorUsedError;
  int get pageNumber => throw _privateConstructorUsedError;
  int get totalHits => throw _privateConstructorUsedError;
  String get viewAllUrl => throw _privateConstructorUsedError;
  List<ArcadierDealsProduct> get products => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ArcadierDealsDataCopyWith<ArcadierDealsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ArcadierDealsDataCopyWith<$Res> {
  factory $ArcadierDealsDataCopyWith(
          ArcadierDealsData value, $Res Function(ArcadierDealsData) then) =
      _$ArcadierDealsDataCopyWithImpl<$Res, ArcadierDealsData>;
  @useResult
  $Res call(
      {int pageSize,
      int pageNumber,
      int totalHits,
      String viewAllUrl,
      List<ArcadierDealsProduct> products});
}

/// @nodoc
class _$ArcadierDealsDataCopyWithImpl<$Res, $Val extends ArcadierDealsData>
    implements $ArcadierDealsDataCopyWith<$Res> {
  _$ArcadierDealsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
    Object? pageNumber = null,
    Object? totalHits = null,
    Object? viewAllUrl = null,
    Object? products = null,
  }) {
    return _then(_value.copyWith(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      pageNumber: null == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      totalHits: null == totalHits
          ? _value.totalHits
          : totalHits // ignore: cast_nullable_to_non_nullable
              as int,
      viewAllUrl: null == viewAllUrl
          ? _value.viewAllUrl
          : viewAllUrl // ignore: cast_nullable_to_non_nullable
              as String,
      products: null == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ArcadierDealsProduct>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ArcadierDealsDataCopyWith<$Res>
    implements $ArcadierDealsDataCopyWith<$Res> {
  factory _$$_ArcadierDealsDataCopyWith(_$_ArcadierDealsData value,
          $Res Function(_$_ArcadierDealsData) then) =
      __$$_ArcadierDealsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int pageSize,
      int pageNumber,
      int totalHits,
      String viewAllUrl,
      List<ArcadierDealsProduct> products});
}

/// @nodoc
class __$$_ArcadierDealsDataCopyWithImpl<$Res>
    extends _$ArcadierDealsDataCopyWithImpl<$Res, _$_ArcadierDealsData>
    implements _$$_ArcadierDealsDataCopyWith<$Res> {
  __$$_ArcadierDealsDataCopyWithImpl(
      _$_ArcadierDealsData _value, $Res Function(_$_ArcadierDealsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
    Object? pageNumber = null,
    Object? totalHits = null,
    Object? viewAllUrl = null,
    Object? products = null,
  }) {
    return _then(_$_ArcadierDealsData(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      pageNumber: null == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      totalHits: null == totalHits
          ? _value.totalHits
          : totalHits // ignore: cast_nullable_to_non_nullable
              as int,
      viewAllUrl: null == viewAllUrl
          ? _value.viewAllUrl
          : viewAllUrl // ignore: cast_nullable_to_non_nullable
              as String,
      products: null == products
          ? _value._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ArcadierDealsProduct>,
    ));
  }
}

/// @nodoc

class _$_ArcadierDealsData implements _ArcadierDealsData {
  _$_ArcadierDealsData(
      {this.pageSize = 10,
      this.pageNumber = 1,
      this.totalHits = 0,
      this.viewAllUrl = '',
      final List<ArcadierDealsProduct> products =
          const <ArcadierDealsProduct>[]})
      : _products = products;

  @override
  @JsonKey()
  final int pageSize;
  @override
  @JsonKey()
  final int pageNumber;
  @override
  @JsonKey()
  final int totalHits;
  @override
  @JsonKey()
  final String viewAllUrl;
  final List<ArcadierDealsProduct> _products;
  @override
  @JsonKey()
  List<ArcadierDealsProduct> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  @override
  String toString() {
    return 'ArcadierDealsData(pageSize: $pageSize, pageNumber: $pageNumber, totalHits: $totalHits, viewAllUrl: $viewAllUrl, products: $products)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ArcadierDealsData &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.pageNumber, pageNumber) ||
                other.pageNumber == pageNumber) &&
            (identical(other.totalHits, totalHits) ||
                other.totalHits == totalHits) &&
            (identical(other.viewAllUrl, viewAllUrl) ||
                other.viewAllUrl == viewAllUrl) &&
            const DeepCollectionEquality().equals(other._products, _products));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pageSize, pageNumber, totalHits,
      viewAllUrl, const DeepCollectionEquality().hash(_products));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ArcadierDealsDataCopyWith<_$_ArcadierDealsData> get copyWith =>
      __$$_ArcadierDealsDataCopyWithImpl<_$_ArcadierDealsData>(
          this, _$identity);
}

abstract class _ArcadierDealsData implements ArcadierDealsData {
  factory _ArcadierDealsData(
      {final int pageSize,
      final int pageNumber,
      final int totalHits,
      final String viewAllUrl,
      final List<ArcadierDealsProduct> products}) = _$_ArcadierDealsData;

  @override
  int get pageSize;
  @override
  int get pageNumber;
  @override
  int get totalHits;
  @override
  String get viewAllUrl;
  @override
  List<ArcadierDealsProduct> get products;
  @override
  @JsonKey(ignore: true)
  _$$_ArcadierDealsDataCopyWith<_$_ArcadierDealsData> get copyWith =>
      throw _privateConstructorUsedError;
}
