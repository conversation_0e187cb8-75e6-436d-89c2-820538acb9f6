// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bnpl_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$BnplFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedBnplFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(BnplNoContent value) noContent,
    required TResult Function(BnplTooManyRequests value) tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedBnplFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(BnplNoContent value)? noContent,
    TResult? Function(BnplTooManyRequests value)? tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedBnplFailure value)? unexpectedInstallmentFailure,
    TResult Function(BnplNoContent value)? noContent,
    TResult Function(BnplTooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BnplFailureCopyWith<$Res> {
  factory $BnplFailureCopyWith(
          BnplFailure value, $Res Function(BnplFailure) then) =
      _$BnplFailureCopyWithImpl<$Res, BnplFailure>;
}

/// @nodoc
class _$BnplFailureCopyWithImpl<$Res, $Val extends BnplFailure>
    implements $BnplFailureCopyWith<$Res> {
  _$BnplFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$UnexpectedBnplFailureCopyWith<$Res> {
  factory _$$UnexpectedBnplFailureCopyWith(_$UnexpectedBnplFailure value,
          $Res Function(_$UnexpectedBnplFailure) then) =
      __$$UnexpectedBnplFailureCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedBnplFailureCopyWithImpl<$Res>
    extends _$BnplFailureCopyWithImpl<$Res, _$UnexpectedBnplFailure>
    implements _$$UnexpectedBnplFailureCopyWith<$Res> {
  __$$UnexpectedBnplFailureCopyWithImpl(_$UnexpectedBnplFailure _value,
      $Res Function(_$UnexpectedBnplFailure) _then)
      : super(_value, _then);
}

/// @nodoc

class _$UnexpectedBnplFailure extends UnexpectedBnplFailure {
  const _$UnexpectedBnplFailure() : super._();

  @override
  String toString() {
    return 'BnplFailure.unexpectedInstallmentFailure()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnexpectedBnplFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return unexpectedInstallmentFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return unexpectedInstallmentFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (unexpectedInstallmentFailure != null) {
      return unexpectedInstallmentFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedBnplFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(BnplNoContent value) noContent,
    required TResult Function(BnplTooManyRequests value) tooManyRequests,
  }) {
    return unexpectedInstallmentFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedBnplFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(BnplNoContent value)? noContent,
    TResult? Function(BnplTooManyRequests value)? tooManyRequests,
  }) {
    return unexpectedInstallmentFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedBnplFailure value)? unexpectedInstallmentFailure,
    TResult Function(BnplNoContent value)? noContent,
    TResult Function(BnplTooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (unexpectedInstallmentFailure != null) {
      return unexpectedInstallmentFailure(this);
    }
    return orElse();
  }
}

abstract class UnexpectedBnplFailure extends BnplFailure {
  const factory UnexpectedBnplFailure() = _$UnexpectedBnplFailure;
  const UnexpectedBnplFailure._() : super._();
}

/// @nodoc
abstract class _$$BnplNoContentCopyWith<$Res> {
  factory _$$BnplNoContentCopyWith(
          _$BnplNoContent value, $Res Function(_$BnplNoContent) then) =
      __$$BnplNoContentCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BnplNoContentCopyWithImpl<$Res>
    extends _$BnplFailureCopyWithImpl<$Res, _$BnplNoContent>
    implements _$$BnplNoContentCopyWith<$Res> {
  __$$BnplNoContentCopyWithImpl(
      _$BnplNoContent _value, $Res Function(_$BnplNoContent) _then)
      : super(_value, _then);
}

/// @nodoc

class _$BnplNoContent extends BnplNoContent {
  const _$BnplNoContent() : super._();

  @override
  String toString() {
    return 'BnplFailure.noContent()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BnplNoContent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return noContent();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return noContent?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (noContent != null) {
      return noContent();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedBnplFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(BnplNoContent value) noContent,
    required TResult Function(BnplTooManyRequests value) tooManyRequests,
  }) {
    return noContent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedBnplFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(BnplNoContent value)? noContent,
    TResult? Function(BnplTooManyRequests value)? tooManyRequests,
  }) {
    return noContent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedBnplFailure value)? unexpectedInstallmentFailure,
    TResult Function(BnplNoContent value)? noContent,
    TResult Function(BnplTooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (noContent != null) {
      return noContent(this);
    }
    return orElse();
  }
}

abstract class BnplNoContent extends BnplFailure {
  const factory BnplNoContent() = _$BnplNoContent;
  const BnplNoContent._() : super._();
}

/// @nodoc
abstract class _$$BnplTooManyRequestsCopyWith<$Res> {
  factory _$$BnplTooManyRequestsCopyWith(_$BnplTooManyRequests value,
          $Res Function(_$BnplTooManyRequests) then) =
      __$$BnplTooManyRequestsCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BnplTooManyRequestsCopyWithImpl<$Res>
    extends _$BnplFailureCopyWithImpl<$Res, _$BnplTooManyRequests>
    implements _$$BnplTooManyRequestsCopyWith<$Res> {
  __$$BnplTooManyRequestsCopyWithImpl(
      _$BnplTooManyRequests _value, $Res Function(_$BnplTooManyRequests) _then)
      : super(_value, _then);
}

/// @nodoc

class _$BnplTooManyRequests extends BnplTooManyRequests {
  const _$BnplTooManyRequests() : super._();

  @override
  String toString() {
    return 'BnplFailure.tooManyRequests()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BnplTooManyRequests);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return tooManyRequests();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return tooManyRequests?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (tooManyRequests != null) {
      return tooManyRequests();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedBnplFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(BnplNoContent value) noContent,
    required TResult Function(BnplTooManyRequests value) tooManyRequests,
  }) {
    return tooManyRequests(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedBnplFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(BnplNoContent value)? noContent,
    TResult? Function(BnplTooManyRequests value)? tooManyRequests,
  }) {
    return tooManyRequests?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedBnplFailure value)? unexpectedInstallmentFailure,
    TResult Function(BnplNoContent value)? noContent,
    TResult Function(BnplTooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (tooManyRequests != null) {
      return tooManyRequests(this);
    }
    return orElse();
  }
}

abstract class BnplTooManyRequests extends BnplFailure {
  const factory BnplTooManyRequests() = _$BnplTooManyRequests;
  const BnplTooManyRequests._() : super._();
}
