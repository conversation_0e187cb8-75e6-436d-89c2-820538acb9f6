// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_card_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CreditCardFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedCreditCardFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(CreditCardNoContent value) noContent,
    required TResult Function(_TooManyRequests value) tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedCreditCardFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(CreditCardNoContent value)? noContent,
    TResult? Function(_TooManyRequests value)? tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedCreditCardFailure value)?
        unexpectedInstallmentFailure,
    TResult Function(CreditCardNoContent value)? noContent,
    TResult Function(_TooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditCardFailureCopyWith<$Res> {
  factory $CreditCardFailureCopyWith(
          CreditCardFailure value, $Res Function(CreditCardFailure) then) =
      _$CreditCardFailureCopyWithImpl<$Res, CreditCardFailure>;
}

/// @nodoc
class _$CreditCardFailureCopyWithImpl<$Res, $Val extends CreditCardFailure>
    implements $CreditCardFailureCopyWith<$Res> {
  _$CreditCardFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$UnexpectedCreditCardFailureCopyWith<$Res> {
  factory _$$UnexpectedCreditCardFailureCopyWith(
          _$UnexpectedCreditCardFailure value,
          $Res Function(_$UnexpectedCreditCardFailure) then) =
      __$$UnexpectedCreditCardFailureCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedCreditCardFailureCopyWithImpl<$Res>
    extends _$CreditCardFailureCopyWithImpl<$Res, _$UnexpectedCreditCardFailure>
    implements _$$UnexpectedCreditCardFailureCopyWith<$Res> {
  __$$UnexpectedCreditCardFailureCopyWithImpl(
      _$UnexpectedCreditCardFailure _value,
      $Res Function(_$UnexpectedCreditCardFailure) _then)
      : super(_value, _then);
}

/// @nodoc

class _$UnexpectedCreditCardFailure extends UnexpectedCreditCardFailure {
  const _$UnexpectedCreditCardFailure() : super._();

  @override
  String toString() {
    return 'CreditCardFailure.unexpectedInstallmentFailure()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnexpectedCreditCardFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return unexpectedInstallmentFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return unexpectedInstallmentFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (unexpectedInstallmentFailure != null) {
      return unexpectedInstallmentFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedCreditCardFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(CreditCardNoContent value) noContent,
    required TResult Function(_TooManyRequests value) tooManyRequests,
  }) {
    return unexpectedInstallmentFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedCreditCardFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(CreditCardNoContent value)? noContent,
    TResult? Function(_TooManyRequests value)? tooManyRequests,
  }) {
    return unexpectedInstallmentFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedCreditCardFailure value)?
        unexpectedInstallmentFailure,
    TResult Function(CreditCardNoContent value)? noContent,
    TResult Function(_TooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (unexpectedInstallmentFailure != null) {
      return unexpectedInstallmentFailure(this);
    }
    return orElse();
  }
}

abstract class UnexpectedCreditCardFailure extends CreditCardFailure {
  const factory UnexpectedCreditCardFailure() = _$UnexpectedCreditCardFailure;
  const UnexpectedCreditCardFailure._() : super._();
}

/// @nodoc
abstract class _$$CreditCardNoContentCopyWith<$Res> {
  factory _$$CreditCardNoContentCopyWith(_$CreditCardNoContent value,
          $Res Function(_$CreditCardNoContent) then) =
      __$$CreditCardNoContentCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreditCardNoContentCopyWithImpl<$Res>
    extends _$CreditCardFailureCopyWithImpl<$Res, _$CreditCardNoContent>
    implements _$$CreditCardNoContentCopyWith<$Res> {
  __$$CreditCardNoContentCopyWithImpl(
      _$CreditCardNoContent _value, $Res Function(_$CreditCardNoContent) _then)
      : super(_value, _then);
}

/// @nodoc

class _$CreditCardNoContent extends CreditCardNoContent {
  const _$CreditCardNoContent() : super._();

  @override
  String toString() {
    return 'CreditCardFailure.noContent()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CreditCardNoContent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return noContent();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return noContent?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (noContent != null) {
      return noContent();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedCreditCardFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(CreditCardNoContent value) noContent,
    required TResult Function(_TooManyRequests value) tooManyRequests,
  }) {
    return noContent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedCreditCardFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(CreditCardNoContent value)? noContent,
    TResult? Function(_TooManyRequests value)? tooManyRequests,
  }) {
    return noContent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedCreditCardFailure value)?
        unexpectedInstallmentFailure,
    TResult Function(CreditCardNoContent value)? noContent,
    TResult Function(_TooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (noContent != null) {
      return noContent(this);
    }
    return orElse();
  }
}

abstract class CreditCardNoContent extends CreditCardFailure {
  const factory CreditCardNoContent() = _$CreditCardNoContent;
  const CreditCardNoContent._() : super._();
}

/// @nodoc
abstract class _$$_TooManyRequestsCopyWith<$Res> {
  factory _$$_TooManyRequestsCopyWith(
          _$_TooManyRequests value, $Res Function(_$_TooManyRequests) then) =
      __$$_TooManyRequestsCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_TooManyRequestsCopyWithImpl<$Res>
    extends _$CreditCardFailureCopyWithImpl<$Res, _$_TooManyRequests>
    implements _$$_TooManyRequestsCopyWith<$Res> {
  __$$_TooManyRequestsCopyWithImpl(
      _$_TooManyRequests _value, $Res Function(_$_TooManyRequests) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_TooManyRequests extends _TooManyRequests {
  const _$_TooManyRequests() : super._();

  @override
  String toString() {
    return 'CreditCardFailure.tooManyRequests()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_TooManyRequests);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return tooManyRequests();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return tooManyRequests?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (tooManyRequests != null) {
      return tooManyRequests();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedCreditCardFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(CreditCardNoContent value) noContent,
    required TResult Function(_TooManyRequests value) tooManyRequests,
  }) {
    return tooManyRequests(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedCreditCardFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(CreditCardNoContent value)? noContent,
    TResult? Function(_TooManyRequests value)? tooManyRequests,
  }) {
    return tooManyRequests?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedCreditCardFailure value)?
        unexpectedInstallmentFailure,
    TResult Function(CreditCardNoContent value)? noContent,
    TResult Function(_TooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (tooManyRequests != null) {
      return tooManyRequests(this);
    }
    return orElse();
  }
}

abstract class _TooManyRequests extends CreditCardFailure {
  const factory _TooManyRequests() = _$_TooManyRequests;
  const _TooManyRequests._() : super._();
}
