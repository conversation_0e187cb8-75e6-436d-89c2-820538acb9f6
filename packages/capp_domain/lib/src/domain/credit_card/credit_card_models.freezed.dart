// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_card_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CreditCardOverview {
  Decimal? get actualSpend => throw _privateConstructorUsedError;
  Decimal? get available => throw _privateConstructorUsedError;
  Decimal? get availableMonthlyInstallmentLimit =>
      throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  ContractStatus? get status => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CreditCardOverviewCopyWith<CreditCardOverview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditCardOverviewCopyWith<$Res> {
  factory $CreditCardOverviewCopyWith(
          CreditCardOverview value, $Res Function(CreditCardOverview) then) =
      _$CreditCardOverviewCopyWithImpl<$Res, CreditCardOverview>;
  @useResult
  $Res call(
      {Decimal? actualSpend,
      Decimal? available,
      Decimal? availableMonthlyInstallmentLimit,
      String? currency,
      ContractStatus? status});
}

/// @nodoc
class _$CreditCardOverviewCopyWithImpl<$Res, $Val extends CreditCardOverview>
    implements $CreditCardOverviewCopyWith<$Res> {
  _$CreditCardOverviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? actualSpend = freezed,
    Object? available = freezed,
    Object? availableMonthlyInstallmentLimit = freezed,
    Object? currency = freezed,
    Object? status = freezed,
  }) {
    return _then(_value.copyWith(
      actualSpend: freezed == actualSpend
          ? _value.actualSpend
          : actualSpend // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      available: freezed == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      availableMonthlyInstallmentLimit: freezed ==
              availableMonthlyInstallmentLimit
          ? _value.availableMonthlyInstallmentLimit
          : availableMonthlyInstallmentLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ContractStatus?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CreditCardOverviewCopyWith<$Res>
    implements $CreditCardOverviewCopyWith<$Res> {
  factory _$$_CreditCardOverviewCopyWith(_$_CreditCardOverview value,
          $Res Function(_$_CreditCardOverview) then) =
      __$$_CreditCardOverviewCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Decimal? actualSpend,
      Decimal? available,
      Decimal? availableMonthlyInstallmentLimit,
      String? currency,
      ContractStatus? status});
}

/// @nodoc
class __$$_CreditCardOverviewCopyWithImpl<$Res>
    extends _$CreditCardOverviewCopyWithImpl<$Res, _$_CreditCardOverview>
    implements _$$_CreditCardOverviewCopyWith<$Res> {
  __$$_CreditCardOverviewCopyWithImpl(
      _$_CreditCardOverview _value, $Res Function(_$_CreditCardOverview) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? actualSpend = freezed,
    Object? available = freezed,
    Object? availableMonthlyInstallmentLimit = freezed,
    Object? currency = freezed,
    Object? status = freezed,
  }) {
    return _then(_$_CreditCardOverview(
      actualSpend: freezed == actualSpend
          ? _value.actualSpend
          : actualSpend // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      available: freezed == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      availableMonthlyInstallmentLimit: freezed ==
              availableMonthlyInstallmentLimit
          ? _value.availableMonthlyInstallmentLimit
          : availableMonthlyInstallmentLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ContractStatus?,
    ));
  }
}

/// @nodoc

class _$_CreditCardOverview implements _CreditCardOverview {
  _$_CreditCardOverview(
      {this.actualSpend,
      this.available,
      this.availableMonthlyInstallmentLimit,
      this.currency,
      this.status});

  @override
  final Decimal? actualSpend;
  @override
  final Decimal? available;
  @override
  final Decimal? availableMonthlyInstallmentLimit;
  @override
  final String? currency;
  @override
  final ContractStatus? status;

  @override
  String toString() {
    return 'CreditCardOverview(actualSpend: $actualSpend, available: $available, availableMonthlyInstallmentLimit: $availableMonthlyInstallmentLimit, currency: $currency, status: $status)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CreditCardOverview &&
            (identical(other.actualSpend, actualSpend) ||
                other.actualSpend == actualSpend) &&
            (identical(other.available, available) ||
                other.available == available) &&
            (identical(other.availableMonthlyInstallmentLimit,
                    availableMonthlyInstallmentLimit) ||
                other.availableMonthlyInstallmentLimit ==
                    availableMonthlyInstallmentLimit) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, actualSpend, available,
      availableMonthlyInstallmentLimit, currency, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CreditCardOverviewCopyWith<_$_CreditCardOverview> get copyWith =>
      __$$_CreditCardOverviewCopyWithImpl<_$_CreditCardOverview>(
          this, _$identity);
}

abstract class _CreditCardOverview implements CreditCardOverview {
  factory _CreditCardOverview(
      {final Decimal? actualSpend,
      final Decimal? available,
      final Decimal? availableMonthlyInstallmentLimit,
      final String? currency,
      final ContractStatus? status}) = _$_CreditCardOverview;

  @override
  Decimal? get actualSpend;
  @override
  Decimal? get available;
  @override
  Decimal? get availableMonthlyInstallmentLimit;
  @override
  String? get currency;
  @override
  ContractStatus? get status;
  @override
  @JsonKey(ignore: true)
  _$$_CreditCardOverviewCopyWith<_$_CreditCardOverview> get copyWith =>
      throw _privateConstructorUsedError;
}
