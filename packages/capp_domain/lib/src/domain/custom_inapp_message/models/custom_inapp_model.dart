import 'package:freezed_annotation/freezed_annotation.dart';

part 'custom_inapp_model.freezed.dart';

@freezed
class CustomInApp with _$CustomInApp {
  factory CustomInApp({
    required String? id,
    required int? templateId,
    required String? title,
    required String? bodyText,
    required String? imageId,
    required String? ctaButtonText,
    required String? ctaButtonUrl,
    required String? tertiaryCtaButtonText,
  }) = _CustomInApp;
}
