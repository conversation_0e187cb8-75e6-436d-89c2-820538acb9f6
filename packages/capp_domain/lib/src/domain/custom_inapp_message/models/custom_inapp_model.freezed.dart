// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'custom_inapp_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CustomInApp {
  String? get id => throw _privateConstructorUsedError;
  int? get templateId => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get bodyText => throw _privateConstructorUsedError;
  String? get imageId => throw _privateConstructorUsedError;
  String? get ctaButtonText => throw _privateConstructorUsedError;
  String? get ctaButtonUrl => throw _privateConstructorUsedError;
  String? get tertiaryCtaButtonText => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CustomInAppCopyWith<CustomInApp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomInAppCopyWith<$Res> {
  factory $CustomInAppCopyWith(
          CustomInApp value, $Res Function(CustomInApp) then) =
      _$CustomInAppCopyWithImpl<$Res, CustomInApp>;
  @useResult
  $Res call(
      {String? id,
      int? templateId,
      String? title,
      String? bodyText,
      String? imageId,
      String? ctaButtonText,
      String? ctaButtonUrl,
      String? tertiaryCtaButtonText});
}

/// @nodoc
class _$CustomInAppCopyWithImpl<$Res, $Val extends CustomInApp>
    implements $CustomInAppCopyWith<$Res> {
  _$CustomInAppCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? templateId = freezed,
    Object? title = freezed,
    Object? bodyText = freezed,
    Object? imageId = freezed,
    Object? ctaButtonText = freezed,
    Object? ctaButtonUrl = freezed,
    Object? tertiaryCtaButtonText = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      templateId: freezed == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      bodyText: freezed == bodyText
          ? _value.bodyText
          : bodyText // ignore: cast_nullable_to_non_nullable
              as String?,
      imageId: freezed == imageId
          ? _value.imageId
          : imageId // ignore: cast_nullable_to_non_nullable
              as String?,
      ctaButtonText: freezed == ctaButtonText
          ? _value.ctaButtonText
          : ctaButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      ctaButtonUrl: freezed == ctaButtonUrl
          ? _value.ctaButtonUrl
          : ctaButtonUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tertiaryCtaButtonText: freezed == tertiaryCtaButtonText
          ? _value.tertiaryCtaButtonText
          : tertiaryCtaButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CustomInAppCopyWith<$Res>
    implements $CustomInAppCopyWith<$Res> {
  factory _$$_CustomInAppCopyWith(
          _$_CustomInApp value, $Res Function(_$_CustomInApp) then) =
      __$$_CustomInAppCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      int? templateId,
      String? title,
      String? bodyText,
      String? imageId,
      String? ctaButtonText,
      String? ctaButtonUrl,
      String? tertiaryCtaButtonText});
}

/// @nodoc
class __$$_CustomInAppCopyWithImpl<$Res>
    extends _$CustomInAppCopyWithImpl<$Res, _$_CustomInApp>
    implements _$$_CustomInAppCopyWith<$Res> {
  __$$_CustomInAppCopyWithImpl(
      _$_CustomInApp _value, $Res Function(_$_CustomInApp) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? templateId = freezed,
    Object? title = freezed,
    Object? bodyText = freezed,
    Object? imageId = freezed,
    Object? ctaButtonText = freezed,
    Object? ctaButtonUrl = freezed,
    Object? tertiaryCtaButtonText = freezed,
  }) {
    return _then(_$_CustomInApp(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      templateId: freezed == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      bodyText: freezed == bodyText
          ? _value.bodyText
          : bodyText // ignore: cast_nullable_to_non_nullable
              as String?,
      imageId: freezed == imageId
          ? _value.imageId
          : imageId // ignore: cast_nullable_to_non_nullable
              as String?,
      ctaButtonText: freezed == ctaButtonText
          ? _value.ctaButtonText
          : ctaButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      ctaButtonUrl: freezed == ctaButtonUrl
          ? _value.ctaButtonUrl
          : ctaButtonUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tertiaryCtaButtonText: freezed == tertiaryCtaButtonText
          ? _value.tertiaryCtaButtonText
          : tertiaryCtaButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_CustomInApp implements _CustomInApp {
  _$_CustomInApp(
      {required this.id,
      required this.templateId,
      required this.title,
      required this.bodyText,
      required this.imageId,
      required this.ctaButtonText,
      required this.ctaButtonUrl,
      required this.tertiaryCtaButtonText});

  @override
  final String? id;
  @override
  final int? templateId;
  @override
  final String? title;
  @override
  final String? bodyText;
  @override
  final String? imageId;
  @override
  final String? ctaButtonText;
  @override
  final String? ctaButtonUrl;
  @override
  final String? tertiaryCtaButtonText;

  @override
  String toString() {
    return 'CustomInApp(id: $id, templateId: $templateId, title: $title, bodyText: $bodyText, imageId: $imageId, ctaButtonText: $ctaButtonText, ctaButtonUrl: $ctaButtonUrl, tertiaryCtaButtonText: $tertiaryCtaButtonText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CustomInApp &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.templateId, templateId) ||
                other.templateId == templateId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.bodyText, bodyText) ||
                other.bodyText == bodyText) &&
            (identical(other.imageId, imageId) || other.imageId == imageId) &&
            (identical(other.ctaButtonText, ctaButtonText) ||
                other.ctaButtonText == ctaButtonText) &&
            (identical(other.ctaButtonUrl, ctaButtonUrl) ||
                other.ctaButtonUrl == ctaButtonUrl) &&
            (identical(other.tertiaryCtaButtonText, tertiaryCtaButtonText) ||
                other.tertiaryCtaButtonText == tertiaryCtaButtonText));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, templateId, title, bodyText,
      imageId, ctaButtonText, ctaButtonUrl, tertiaryCtaButtonText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CustomInAppCopyWith<_$_CustomInApp> get copyWith =>
      __$$_CustomInAppCopyWithImpl<_$_CustomInApp>(this, _$identity);
}

abstract class _CustomInApp implements CustomInApp {
  factory _CustomInApp(
      {required final String? id,
      required final int? templateId,
      required final String? title,
      required final String? bodyText,
      required final String? imageId,
      required final String? ctaButtonText,
      required final String? ctaButtonUrl,
      required final String? tertiaryCtaButtonText}) = _$_CustomInApp;

  @override
  String? get id;
  @override
  int? get templateId;
  @override
  String? get title;
  @override
  String? get bodyText;
  @override
  String? get imageId;
  @override
  String? get ctaButtonText;
  @override
  String? get ctaButtonUrl;
  @override
  String? get tertiaryCtaButtonText;
  @override
  @JsonKey(ignore: true)
  _$$_CustomInAppCopyWith<_$_CustomInApp> get copyWith =>
      throw _privateConstructorUsedError;
}
