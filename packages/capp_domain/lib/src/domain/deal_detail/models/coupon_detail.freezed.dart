// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'coupon_detail.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CouponDetail {
  String? get couponId => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;
  bool? get isTopDeal => throw _privateConstructorUsedError;
  bool? get isDailyDeal => throw _privateConstructorUsedError;
  bool? get isHearted => throw _privateConstructorUsedError;
  String? get bannerText => throw _privateConstructorUsedError;
  Price? get priceRegular => throw _privateConstructorUsedError;
  Price? get priceDiscount => throw _privateConstructorUsedError;
  String? get introduction => throw _privateConstructorUsedError;
  String? get descriptionAbout => throw _privateConstructorUsedError;
  String? get descriptionNeedToKnow => throw _privateConstructorUsedError;
  List<Media>? get media => throw _privateConstructorUsedError;
  String? get merchantBranchId => throw _privateConstructorUsedError;
  DateTime? get validFrom => throw _privateConstructorUsedError;
  DateTime? get validTo => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  int? get couponsAvailable => throw _privateConstructorUsedError;
  int? get couponsLeft => throw _privateConstructorUsedError;
  int? get ratingsCount => throw _privateConstructorUsedError;
  double? get rating => throw _privateConstructorUsedError;
  MerchantBranchInfo? get merchant => throw _privateConstructorUsedError;
  String? get eTag => throw _privateConstructorUsedError;
  int? get xpValue => throw _privateConstructorUsedError;
  String? get couponColor => throw _privateConstructorUsedError;
  String? get categoryId => throw _privateConstructorUsedError;
  CouponBrandClassification? get brandClassification =>
      throw _privateConstructorUsedError;
  String? get brandName => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;
  Media? get brandLogo => throw _privateConstructorUsedError;
  bool? get isOnline => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CouponDetailCopyWith<CouponDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CouponDetailCopyWith<$Res> {
  factory $CouponDetailCopyWith(
          CouponDetail value, $Res Function(CouponDetail) then) =
      _$CouponDetailCopyWithImpl<$Res, CouponDetail>;
  @useResult
  $Res call(
      {String? couponId,
      String? title,
      String? code,
      bool? isTopDeal,
      bool? isDailyDeal,
      bool? isHearted,
      String? bannerText,
      Price? priceRegular,
      Price? priceDiscount,
      String? introduction,
      String? descriptionAbout,
      String? descriptionNeedToKnow,
      List<Media>? media,
      String? merchantBranchId,
      DateTime? validFrom,
      DateTime? validTo,
      bool? isActive,
      int? couponsAvailable,
      int? couponsLeft,
      int? ratingsCount,
      double? rating,
      MerchantBranchInfo? merchant,
      String? eTag,
      int? xpValue,
      String? couponColor,
      String? categoryId,
      CouponBrandClassification? brandClassification,
      String? brandName,
      String? url,
      Media? brandLogo,
      bool? isOnline});

  $PriceCopyWith<$Res>? get priceRegular;
  $PriceCopyWith<$Res>? get priceDiscount;
  $MerchantBranchInfoCopyWith<$Res>? get merchant;
  $MediaCopyWith<$Res>? get brandLogo;
}

/// @nodoc
class _$CouponDetailCopyWithImpl<$Res, $Val extends CouponDetail>
    implements $CouponDetailCopyWith<$Res> {
  _$CouponDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? couponId = freezed,
    Object? title = freezed,
    Object? code = freezed,
    Object? isTopDeal = freezed,
    Object? isDailyDeal = freezed,
    Object? isHearted = freezed,
    Object? bannerText = freezed,
    Object? priceRegular = freezed,
    Object? priceDiscount = freezed,
    Object? introduction = freezed,
    Object? descriptionAbout = freezed,
    Object? descriptionNeedToKnow = freezed,
    Object? media = freezed,
    Object? merchantBranchId = freezed,
    Object? validFrom = freezed,
    Object? validTo = freezed,
    Object? isActive = freezed,
    Object? couponsAvailable = freezed,
    Object? couponsLeft = freezed,
    Object? ratingsCount = freezed,
    Object? rating = freezed,
    Object? merchant = freezed,
    Object? eTag = freezed,
    Object? xpValue = freezed,
    Object? couponColor = freezed,
    Object? categoryId = freezed,
    Object? brandClassification = freezed,
    Object? brandName = freezed,
    Object? url = freezed,
    Object? brandLogo = freezed,
    Object? isOnline = freezed,
  }) {
    return _then(_value.copyWith(
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      isTopDeal: freezed == isTopDeal
          ? _value.isTopDeal
          : isTopDeal // ignore: cast_nullable_to_non_nullable
              as bool?,
      isDailyDeal: freezed == isDailyDeal
          ? _value.isDailyDeal
          : isDailyDeal // ignore: cast_nullable_to_non_nullable
              as bool?,
      isHearted: freezed == isHearted
          ? _value.isHearted
          : isHearted // ignore: cast_nullable_to_non_nullable
              as bool?,
      bannerText: freezed == bannerText
          ? _value.bannerText
          : bannerText // ignore: cast_nullable_to_non_nullable
              as String?,
      priceRegular: freezed == priceRegular
          ? _value.priceRegular
          : priceRegular // ignore: cast_nullable_to_non_nullable
              as Price?,
      priceDiscount: freezed == priceDiscount
          ? _value.priceDiscount
          : priceDiscount // ignore: cast_nullable_to_non_nullable
              as Price?,
      introduction: freezed == introduction
          ? _value.introduction
          : introduction // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAbout: freezed == descriptionAbout
          ? _value.descriptionAbout
          : descriptionAbout // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionNeedToKnow: freezed == descriptionNeedToKnow
          ? _value.descriptionNeedToKnow
          : descriptionNeedToKnow // ignore: cast_nullable_to_non_nullable
              as String?,
      media: freezed == media
          ? _value.media
          : media // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      merchantBranchId: freezed == merchantBranchId
          ? _value.merchantBranchId
          : merchantBranchId // ignore: cast_nullable_to_non_nullable
              as String?,
      validFrom: freezed == validFrom
          ? _value.validFrom
          : validFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      couponsAvailable: freezed == couponsAvailable
          ? _value.couponsAvailable
          : couponsAvailable // ignore: cast_nullable_to_non_nullable
              as int?,
      couponsLeft: freezed == couponsLeft
          ? _value.couponsLeft
          : couponsLeft // ignore: cast_nullable_to_non_nullable
              as int?,
      ratingsCount: freezed == ratingsCount
          ? _value.ratingsCount
          : ratingsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      merchant: freezed == merchant
          ? _value.merchant
          : merchant // ignore: cast_nullable_to_non_nullable
              as MerchantBranchInfo?,
      eTag: freezed == eTag
          ? _value.eTag
          : eTag // ignore: cast_nullable_to_non_nullable
              as String?,
      xpValue: freezed == xpValue
          ? _value.xpValue
          : xpValue // ignore: cast_nullable_to_non_nullable
              as int?,
      couponColor: freezed == couponColor
          ? _value.couponColor
          : couponColor // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      brandClassification: freezed == brandClassification
          ? _value.brandClassification
          : brandClassification // ignore: cast_nullable_to_non_nullable
              as CouponBrandClassification?,
      brandName: freezed == brandName
          ? _value.brandName
          : brandName // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      brandLogo: freezed == brandLogo
          ? _value.brandLogo
          : brandLogo // ignore: cast_nullable_to_non_nullable
              as Media?,
      isOnline: freezed == isOnline
          ? _value.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PriceCopyWith<$Res>? get priceRegular {
    if (_value.priceRegular == null) {
      return null;
    }

    return $PriceCopyWith<$Res>(_value.priceRegular!, (value) {
      return _then(_value.copyWith(priceRegular: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PriceCopyWith<$Res>? get priceDiscount {
    if (_value.priceDiscount == null) {
      return null;
    }

    return $PriceCopyWith<$Res>(_value.priceDiscount!, (value) {
      return _then(_value.copyWith(priceDiscount: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MerchantBranchInfoCopyWith<$Res>? get merchant {
    if (_value.merchant == null) {
      return null;
    }

    return $MerchantBranchInfoCopyWith<$Res>(_value.merchant!, (value) {
      return _then(_value.copyWith(merchant: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MediaCopyWith<$Res>? get brandLogo {
    if (_value.brandLogo == null) {
      return null;
    }

    return $MediaCopyWith<$Res>(_value.brandLogo!, (value) {
      return _then(_value.copyWith(brandLogo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CouponDetailCopyWith<$Res>
    implements $CouponDetailCopyWith<$Res> {
  factory _$$_CouponDetailCopyWith(
          _$_CouponDetail value, $Res Function(_$_CouponDetail) then) =
      __$$_CouponDetailCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? couponId,
      String? title,
      String? code,
      bool? isTopDeal,
      bool? isDailyDeal,
      bool? isHearted,
      String? bannerText,
      Price? priceRegular,
      Price? priceDiscount,
      String? introduction,
      String? descriptionAbout,
      String? descriptionNeedToKnow,
      List<Media>? media,
      String? merchantBranchId,
      DateTime? validFrom,
      DateTime? validTo,
      bool? isActive,
      int? couponsAvailable,
      int? couponsLeft,
      int? ratingsCount,
      double? rating,
      MerchantBranchInfo? merchant,
      String? eTag,
      int? xpValue,
      String? couponColor,
      String? categoryId,
      CouponBrandClassification? brandClassification,
      String? brandName,
      String? url,
      Media? brandLogo,
      bool? isOnline});

  @override
  $PriceCopyWith<$Res>? get priceRegular;
  @override
  $PriceCopyWith<$Res>? get priceDiscount;
  @override
  $MerchantBranchInfoCopyWith<$Res>? get merchant;
  @override
  $MediaCopyWith<$Res>? get brandLogo;
}

/// @nodoc
class __$$_CouponDetailCopyWithImpl<$Res>
    extends _$CouponDetailCopyWithImpl<$Res, _$_CouponDetail>
    implements _$$_CouponDetailCopyWith<$Res> {
  __$$_CouponDetailCopyWithImpl(
      _$_CouponDetail _value, $Res Function(_$_CouponDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? couponId = freezed,
    Object? title = freezed,
    Object? code = freezed,
    Object? isTopDeal = freezed,
    Object? isDailyDeal = freezed,
    Object? isHearted = freezed,
    Object? bannerText = freezed,
    Object? priceRegular = freezed,
    Object? priceDiscount = freezed,
    Object? introduction = freezed,
    Object? descriptionAbout = freezed,
    Object? descriptionNeedToKnow = freezed,
    Object? media = freezed,
    Object? merchantBranchId = freezed,
    Object? validFrom = freezed,
    Object? validTo = freezed,
    Object? isActive = freezed,
    Object? couponsAvailable = freezed,
    Object? couponsLeft = freezed,
    Object? ratingsCount = freezed,
    Object? rating = freezed,
    Object? merchant = freezed,
    Object? eTag = freezed,
    Object? xpValue = freezed,
    Object? couponColor = freezed,
    Object? categoryId = freezed,
    Object? brandClassification = freezed,
    Object? brandName = freezed,
    Object? url = freezed,
    Object? brandLogo = freezed,
    Object? isOnline = freezed,
  }) {
    return _then(_$_CouponDetail(
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      isTopDeal: freezed == isTopDeal
          ? _value.isTopDeal
          : isTopDeal // ignore: cast_nullable_to_non_nullable
              as bool?,
      isDailyDeal: freezed == isDailyDeal
          ? _value.isDailyDeal
          : isDailyDeal // ignore: cast_nullable_to_non_nullable
              as bool?,
      isHearted: freezed == isHearted
          ? _value.isHearted
          : isHearted // ignore: cast_nullable_to_non_nullable
              as bool?,
      bannerText: freezed == bannerText
          ? _value.bannerText
          : bannerText // ignore: cast_nullable_to_non_nullable
              as String?,
      priceRegular: freezed == priceRegular
          ? _value.priceRegular
          : priceRegular // ignore: cast_nullable_to_non_nullable
              as Price?,
      priceDiscount: freezed == priceDiscount
          ? _value.priceDiscount
          : priceDiscount // ignore: cast_nullable_to_non_nullable
              as Price?,
      introduction: freezed == introduction
          ? _value.introduction
          : introduction // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAbout: freezed == descriptionAbout
          ? _value.descriptionAbout
          : descriptionAbout // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionNeedToKnow: freezed == descriptionNeedToKnow
          ? _value.descriptionNeedToKnow
          : descriptionNeedToKnow // ignore: cast_nullable_to_non_nullable
              as String?,
      media: freezed == media
          ? _value._media
          : media // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      merchantBranchId: freezed == merchantBranchId
          ? _value.merchantBranchId
          : merchantBranchId // ignore: cast_nullable_to_non_nullable
              as String?,
      validFrom: freezed == validFrom
          ? _value.validFrom
          : validFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      couponsAvailable: freezed == couponsAvailable
          ? _value.couponsAvailable
          : couponsAvailable // ignore: cast_nullable_to_non_nullable
              as int?,
      couponsLeft: freezed == couponsLeft
          ? _value.couponsLeft
          : couponsLeft // ignore: cast_nullable_to_non_nullable
              as int?,
      ratingsCount: freezed == ratingsCount
          ? _value.ratingsCount
          : ratingsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      merchant: freezed == merchant
          ? _value.merchant
          : merchant // ignore: cast_nullable_to_non_nullable
              as MerchantBranchInfo?,
      eTag: freezed == eTag
          ? _value.eTag
          : eTag // ignore: cast_nullable_to_non_nullable
              as String?,
      xpValue: freezed == xpValue
          ? _value.xpValue
          : xpValue // ignore: cast_nullable_to_non_nullable
              as int?,
      couponColor: freezed == couponColor
          ? _value.couponColor
          : couponColor // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      brandClassification: freezed == brandClassification
          ? _value.brandClassification
          : brandClassification // ignore: cast_nullable_to_non_nullable
              as CouponBrandClassification?,
      brandName: freezed == brandName
          ? _value.brandName
          : brandName // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      brandLogo: freezed == brandLogo
          ? _value.brandLogo
          : brandLogo // ignore: cast_nullable_to_non_nullable
              as Media?,
      isOnline: freezed == isOnline
          ? _value.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$_CouponDetail extends _CouponDetail {
  const _$_CouponDetail(
      {this.couponId,
      this.title,
      this.code,
      this.isTopDeal,
      this.isDailyDeal,
      this.isHearted,
      this.bannerText,
      this.priceRegular,
      this.priceDiscount,
      this.introduction,
      this.descriptionAbout,
      this.descriptionNeedToKnow,
      final List<Media>? media,
      this.merchantBranchId,
      this.validFrom,
      this.validTo,
      this.isActive,
      this.couponsAvailable,
      this.couponsLeft,
      this.ratingsCount,
      this.rating,
      this.merchant,
      this.eTag,
      this.xpValue,
      this.couponColor,
      this.categoryId,
      this.brandClassification,
      this.brandName,
      this.url,
      this.brandLogo,
      this.isOnline})
      : _media = media,
        super._();

  @override
  final String? couponId;
  @override
  final String? title;
  @override
  final String? code;
  @override
  final bool? isTopDeal;
  @override
  final bool? isDailyDeal;
  @override
  final bool? isHearted;
  @override
  final String? bannerText;
  @override
  final Price? priceRegular;
  @override
  final Price? priceDiscount;
  @override
  final String? introduction;
  @override
  final String? descriptionAbout;
  @override
  final String? descriptionNeedToKnow;
  final List<Media>? _media;
  @override
  List<Media>? get media {
    final value = _media;
    if (value == null) return null;
    if (_media is EqualUnmodifiableListView) return _media;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? merchantBranchId;
  @override
  final DateTime? validFrom;
  @override
  final DateTime? validTo;
  @override
  final bool? isActive;
  @override
  final int? couponsAvailable;
  @override
  final int? couponsLeft;
  @override
  final int? ratingsCount;
  @override
  final double? rating;
  @override
  final MerchantBranchInfo? merchant;
  @override
  final String? eTag;
  @override
  final int? xpValue;
  @override
  final String? couponColor;
  @override
  final String? categoryId;
  @override
  final CouponBrandClassification? brandClassification;
  @override
  final String? brandName;
  @override
  final String? url;
  @override
  final Media? brandLogo;
  @override
  final bool? isOnline;

  @override
  String toString() {
    return 'CouponDetail(couponId: $couponId, title: $title, code: $code, isTopDeal: $isTopDeal, isDailyDeal: $isDailyDeal, isHearted: $isHearted, bannerText: $bannerText, priceRegular: $priceRegular, priceDiscount: $priceDiscount, introduction: $introduction, descriptionAbout: $descriptionAbout, descriptionNeedToKnow: $descriptionNeedToKnow, media: $media, merchantBranchId: $merchantBranchId, validFrom: $validFrom, validTo: $validTo, isActive: $isActive, couponsAvailable: $couponsAvailable, couponsLeft: $couponsLeft, ratingsCount: $ratingsCount, rating: $rating, merchant: $merchant, eTag: $eTag, xpValue: $xpValue, couponColor: $couponColor, categoryId: $categoryId, brandClassification: $brandClassification, brandName: $brandName, url: $url, brandLogo: $brandLogo, isOnline: $isOnline)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CouponDetail &&
            (identical(other.couponId, couponId) ||
                other.couponId == couponId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.isTopDeal, isTopDeal) ||
                other.isTopDeal == isTopDeal) &&
            (identical(other.isDailyDeal, isDailyDeal) ||
                other.isDailyDeal == isDailyDeal) &&
            (identical(other.isHearted, isHearted) ||
                other.isHearted == isHearted) &&
            (identical(other.bannerText, bannerText) ||
                other.bannerText == bannerText) &&
            (identical(other.priceRegular, priceRegular) ||
                other.priceRegular == priceRegular) &&
            (identical(other.priceDiscount, priceDiscount) ||
                other.priceDiscount == priceDiscount) &&
            (identical(other.introduction, introduction) ||
                other.introduction == introduction) &&
            (identical(other.descriptionAbout, descriptionAbout) ||
                other.descriptionAbout == descriptionAbout) &&
            (identical(other.descriptionNeedToKnow, descriptionNeedToKnow) ||
                other.descriptionNeedToKnow == descriptionNeedToKnow) &&
            const DeepCollectionEquality().equals(other._media, _media) &&
            (identical(other.merchantBranchId, merchantBranchId) ||
                other.merchantBranchId == merchantBranchId) &&
            (identical(other.validFrom, validFrom) ||
                other.validFrom == validFrom) &&
            (identical(other.validTo, validTo) || other.validTo == validTo) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.couponsAvailable, couponsAvailable) ||
                other.couponsAvailable == couponsAvailable) &&
            (identical(other.couponsLeft, couponsLeft) ||
                other.couponsLeft == couponsLeft) &&
            (identical(other.ratingsCount, ratingsCount) ||
                other.ratingsCount == ratingsCount) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.merchant, merchant) ||
                other.merchant == merchant) &&
            (identical(other.eTag, eTag) || other.eTag == eTag) &&
            (identical(other.xpValue, xpValue) || other.xpValue == xpValue) &&
            (identical(other.couponColor, couponColor) ||
                other.couponColor == couponColor) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.brandClassification, brandClassification) ||
                other.brandClassification == brandClassification) &&
            (identical(other.brandName, brandName) ||
                other.brandName == brandName) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.brandLogo, brandLogo) ||
                other.brandLogo == brandLogo) &&
            (identical(other.isOnline, isOnline) ||
                other.isOnline == isOnline));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        couponId,
        title,
        code,
        isTopDeal,
        isDailyDeal,
        isHearted,
        bannerText,
        priceRegular,
        priceDiscount,
        introduction,
        descriptionAbout,
        descriptionNeedToKnow,
        const DeepCollectionEquality().hash(_media),
        merchantBranchId,
        validFrom,
        validTo,
        isActive,
        couponsAvailable,
        couponsLeft,
        ratingsCount,
        rating,
        merchant,
        eTag,
        xpValue,
        couponColor,
        categoryId,
        brandClassification,
        brandName,
        url,
        brandLogo,
        isOnline
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CouponDetailCopyWith<_$_CouponDetail> get copyWith =>
      __$$_CouponDetailCopyWithImpl<_$_CouponDetail>(this, _$identity);
}

abstract class _CouponDetail extends CouponDetail {
  const factory _CouponDetail(
      {final String? couponId,
      final String? title,
      final String? code,
      final bool? isTopDeal,
      final bool? isDailyDeal,
      final bool? isHearted,
      final String? bannerText,
      final Price? priceRegular,
      final Price? priceDiscount,
      final String? introduction,
      final String? descriptionAbout,
      final String? descriptionNeedToKnow,
      final List<Media>? media,
      final String? merchantBranchId,
      final DateTime? validFrom,
      final DateTime? validTo,
      final bool? isActive,
      final int? couponsAvailable,
      final int? couponsLeft,
      final int? ratingsCount,
      final double? rating,
      final MerchantBranchInfo? merchant,
      final String? eTag,
      final int? xpValue,
      final String? couponColor,
      final String? categoryId,
      final CouponBrandClassification? brandClassification,
      final String? brandName,
      final String? url,
      final Media? brandLogo,
      final bool? isOnline}) = _$_CouponDetail;
  const _CouponDetail._() : super._();

  @override
  String? get couponId;
  @override
  String? get title;
  @override
  String? get code;
  @override
  bool? get isTopDeal;
  @override
  bool? get isDailyDeal;
  @override
  bool? get isHearted;
  @override
  String? get bannerText;
  @override
  Price? get priceRegular;
  @override
  Price? get priceDiscount;
  @override
  String? get introduction;
  @override
  String? get descriptionAbout;
  @override
  String? get descriptionNeedToKnow;
  @override
  List<Media>? get media;
  @override
  String? get merchantBranchId;
  @override
  DateTime? get validFrom;
  @override
  DateTime? get validTo;
  @override
  bool? get isActive;
  @override
  int? get couponsAvailable;
  @override
  int? get couponsLeft;
  @override
  int? get ratingsCount;
  @override
  double? get rating;
  @override
  MerchantBranchInfo? get merchant;
  @override
  String? get eTag;
  @override
  int? get xpValue;
  @override
  String? get couponColor;
  @override
  String? get categoryId;
  @override
  CouponBrandClassification? get brandClassification;
  @override
  String? get brandName;
  @override
  String? get url;
  @override
  Media? get brandLogo;
  @override
  bool? get isOnline;
  @override
  @JsonKey(ignore: true)
  _$$_CouponDetailCopyWith<_$_CouponDetail> get copyWith =>
      throw _privateConstructorUsedError;
}
