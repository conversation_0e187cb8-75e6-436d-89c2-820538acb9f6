// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'coupon_list.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CouponList {
  List<CouponDetail>? get items => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CouponListCopyWith<CouponList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CouponListCopyWith<$Res> {
  factory $CouponListCopyWith(
          CouponList value, $Res Function(CouponList) then) =
      _$CouponListCopyWithImpl<$Res, CouponList>;
  @useResult
  $Res call({List<CouponDetail>? items});
}

/// @nodoc
class _$CouponListCopyWithImpl<$Res, $Val extends CouponList>
    implements $CouponListCopyWith<$Res> {
  _$CouponListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<CouponDetail>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CouponListCopyWith<$Res>
    implements $CouponListCopyWith<$Res> {
  factory _$$_CouponListCopyWith(
          _$_CouponList value, $Res Function(_$_CouponList) then) =
      __$$_CouponListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CouponDetail>? items});
}

/// @nodoc
class __$$_CouponListCopyWithImpl<$Res>
    extends _$CouponListCopyWithImpl<$Res, _$_CouponList>
    implements _$$_CouponListCopyWith<$Res> {
  __$$_CouponListCopyWithImpl(
      _$_CouponList _value, $Res Function(_$_CouponList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
  }) {
    return _then(_$_CouponList(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<CouponDetail>?,
    ));
  }
}

/// @nodoc

class _$_CouponList implements _CouponList {
  const _$_CouponList({final List<CouponDetail>? items}) : _items = items;

  final List<CouponDetail>? _items;
  @override
  List<CouponDetail>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CouponList(items: $items)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CouponList &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CouponListCopyWith<_$_CouponList> get copyWith =>
      __$$_CouponListCopyWithImpl<_$_CouponList>(this, _$identity);
}

abstract class _CouponList implements CouponList {
  const factory _CouponList({final List<CouponDetail>? items}) = _$_CouponList;

  @override
  List<CouponDetail>? get items;
  @override
  @JsonKey(ignore: true)
  _$$_CouponListCopyWith<_$_CouponList> get copyWith =>
      throw _privateConstructorUsedError;
}
