// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deal_detail.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DealDetail {
  String get productId => throw _privateConstructorUsedError;
  String get categoryId => throw _privateConstructorUsedError;
  bool get isHearted => throw _privateConstructorUsedError;
  double? get rating => throw _privateConstructorUsedError;
  int? get ratingCount => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<String> get imageUrls => throw _privateConstructorUsedError;
  Decimal? get price => throw _privateConstructorUsedError;
  Decimal? get loanInstallment => throw _privateConstructorUsedError;
  int? get discount =>
      throw _privateConstructorUsedError; // hasCashOffersNearby, hasCashOffers, hasCashOffersOnline, hasLoanOffersOnline, accountNumber
// are here because they come from same API endpoint
// they are used on Product detail page to display "Buy" buttons and following dialogs/flows
  bool get hasCashOffersNearby => throw _privateConstructorUsedError;
  bool get hasLoanOffersNearby => throw _privateConstructorUsedError;
  bool get hasCashOffersOnline => throw _privateConstructorUsedError;
  bool get hasLoanOffersOnline => throw _privateConstructorUsedError;
  String? get accountNumber => throw _privateConstructorUsedError;
  DateTime? get validTo => throw _privateConstructorUsedError;
  Decimal? get loanMinAmount => throw _privateConstructorUsedError;
  Decimal? get loanMaxAmount => throw _privateConstructorUsedError;
  Decimal? get availableLimit => throw _privateConstructorUsedError;
  Decimal? get usedLimit =>
      throw _privateConstructorUsedError; // hasNearby, hasOnline are deprecated, used to keep compatibility with old DealsApi
  bool? get hasNearby => throw _privateConstructorUsedError;
  bool? get hasOnline => throw _privateConstructorUsedError;
  ProductAttributes? get attributes => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DealDetailCopyWith<DealDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DealDetailCopyWith<$Res> {
  factory $DealDetailCopyWith(
          DealDetail value, $Res Function(DealDetail) then) =
      _$DealDetailCopyWithImpl<$Res, DealDetail>;
  @useResult
  $Res call(
      {String productId,
      String categoryId,
      bool isHearted,
      double? rating,
      int? ratingCount,
      String title,
      String? description,
      List<String> imageUrls,
      Decimal? price,
      Decimal? loanInstallment,
      int? discount,
      bool hasCashOffersNearby,
      bool hasLoanOffersNearby,
      bool hasCashOffersOnline,
      bool hasLoanOffersOnline,
      String? accountNumber,
      DateTime? validTo,
      Decimal? loanMinAmount,
      Decimal? loanMaxAmount,
      Decimal? availableLimit,
      Decimal? usedLimit,
      bool? hasNearby,
      bool? hasOnline,
      ProductAttributes? attributes});

  $ProductAttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class _$DealDetailCopyWithImpl<$Res, $Val extends DealDetail>
    implements $DealDetailCopyWith<$Res> {
  _$DealDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? categoryId = null,
    Object? isHearted = null,
    Object? rating = freezed,
    Object? ratingCount = freezed,
    Object? title = null,
    Object? description = freezed,
    Object? imageUrls = null,
    Object? price = freezed,
    Object? loanInstallment = freezed,
    Object? discount = freezed,
    Object? hasCashOffersNearby = null,
    Object? hasLoanOffersNearby = null,
    Object? hasCashOffersOnline = null,
    Object? hasLoanOffersOnline = null,
    Object? accountNumber = freezed,
    Object? validTo = freezed,
    Object? loanMinAmount = freezed,
    Object? loanMaxAmount = freezed,
    Object? availableLimit = freezed,
    Object? usedLimit = freezed,
    Object? hasNearby = freezed,
    Object? hasOnline = freezed,
    Object? attributes = freezed,
  }) {
    return _then(_value.copyWith(
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      isHearted: null == isHearted
          ? _value.isHearted
          : isHearted // ignore: cast_nullable_to_non_nullable
              as bool,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      ratingCount: freezed == ratingCount
          ? _value.ratingCount
          : ratingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrls: null == imageUrls
          ? _value.imageUrls
          : imageUrls // ignore: cast_nullable_to_non_nullable
              as List<String>,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      loanInstallment: freezed == loanInstallment
          ? _value.loanInstallment
          : loanInstallment // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as int?,
      hasCashOffersNearby: null == hasCashOffersNearby
          ? _value.hasCashOffersNearby
          : hasCashOffersNearby // ignore: cast_nullable_to_non_nullable
              as bool,
      hasLoanOffersNearby: null == hasLoanOffersNearby
          ? _value.hasLoanOffersNearby
          : hasLoanOffersNearby // ignore: cast_nullable_to_non_nullable
              as bool,
      hasCashOffersOnline: null == hasCashOffersOnline
          ? _value.hasCashOffersOnline
          : hasCashOffersOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      hasLoanOffersOnline: null == hasLoanOffersOnline
          ? _value.hasLoanOffersOnline
          : hasLoanOffersOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loanMinAmount: freezed == loanMinAmount
          ? _value.loanMinAmount
          : loanMinAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      loanMaxAmount: freezed == loanMaxAmount
          ? _value.loanMaxAmount
          : loanMaxAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      availableLimit: freezed == availableLimit
          ? _value.availableLimit
          : availableLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      usedLimit: freezed == usedLimit
          ? _value.usedLimit
          : usedLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      hasNearby: freezed == hasNearby
          ? _value.hasNearby
          : hasNearby // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasOnline: freezed == hasOnline
          ? _value.hasOnline
          : hasOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as ProductAttributes?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProductAttributesCopyWith<$Res>? get attributes {
    if (_value.attributes == null) {
      return null;
    }

    return $ProductAttributesCopyWith<$Res>(_value.attributes!, (value) {
      return _then(_value.copyWith(attributes: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_DealDetailCopyWith<$Res>
    implements $DealDetailCopyWith<$Res> {
  factory _$$_DealDetailCopyWith(
          _$_DealDetail value, $Res Function(_$_DealDetail) then) =
      __$$_DealDetailCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String productId,
      String categoryId,
      bool isHearted,
      double? rating,
      int? ratingCount,
      String title,
      String? description,
      List<String> imageUrls,
      Decimal? price,
      Decimal? loanInstallment,
      int? discount,
      bool hasCashOffersNearby,
      bool hasLoanOffersNearby,
      bool hasCashOffersOnline,
      bool hasLoanOffersOnline,
      String? accountNumber,
      DateTime? validTo,
      Decimal? loanMinAmount,
      Decimal? loanMaxAmount,
      Decimal? availableLimit,
      Decimal? usedLimit,
      bool? hasNearby,
      bool? hasOnline,
      ProductAttributes? attributes});

  @override
  $ProductAttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class __$$_DealDetailCopyWithImpl<$Res>
    extends _$DealDetailCopyWithImpl<$Res, _$_DealDetail>
    implements _$$_DealDetailCopyWith<$Res> {
  __$$_DealDetailCopyWithImpl(
      _$_DealDetail _value, $Res Function(_$_DealDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? categoryId = null,
    Object? isHearted = null,
    Object? rating = freezed,
    Object? ratingCount = freezed,
    Object? title = null,
    Object? description = freezed,
    Object? imageUrls = null,
    Object? price = freezed,
    Object? loanInstallment = freezed,
    Object? discount = freezed,
    Object? hasCashOffersNearby = null,
    Object? hasLoanOffersNearby = null,
    Object? hasCashOffersOnline = null,
    Object? hasLoanOffersOnline = null,
    Object? accountNumber = freezed,
    Object? validTo = freezed,
    Object? loanMinAmount = freezed,
    Object? loanMaxAmount = freezed,
    Object? availableLimit = freezed,
    Object? usedLimit = freezed,
    Object? hasNearby = freezed,
    Object? hasOnline = freezed,
    Object? attributes = freezed,
  }) {
    return _then(_$_DealDetail(
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      isHearted: null == isHearted
          ? _value.isHearted
          : isHearted // ignore: cast_nullable_to_non_nullable
              as bool,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      ratingCount: freezed == ratingCount
          ? _value.ratingCount
          : ratingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrls: null == imageUrls
          ? _value._imageUrls
          : imageUrls // ignore: cast_nullable_to_non_nullable
              as List<String>,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      loanInstallment: freezed == loanInstallment
          ? _value.loanInstallment
          : loanInstallment // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as int?,
      hasCashOffersNearby: null == hasCashOffersNearby
          ? _value.hasCashOffersNearby
          : hasCashOffersNearby // ignore: cast_nullable_to_non_nullable
              as bool,
      hasLoanOffersNearby: null == hasLoanOffersNearby
          ? _value.hasLoanOffersNearby
          : hasLoanOffersNearby // ignore: cast_nullable_to_non_nullable
              as bool,
      hasCashOffersOnline: null == hasCashOffersOnline
          ? _value.hasCashOffersOnline
          : hasCashOffersOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      hasLoanOffersOnline: null == hasLoanOffersOnline
          ? _value.hasLoanOffersOnline
          : hasLoanOffersOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loanMinAmount: freezed == loanMinAmount
          ? _value.loanMinAmount
          : loanMinAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      loanMaxAmount: freezed == loanMaxAmount
          ? _value.loanMaxAmount
          : loanMaxAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      availableLimit: freezed == availableLimit
          ? _value.availableLimit
          : availableLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      usedLimit: freezed == usedLimit
          ? _value.usedLimit
          : usedLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      hasNearby: freezed == hasNearby
          ? _value.hasNearby
          : hasNearby // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasOnline: freezed == hasOnline
          ? _value.hasOnline
          : hasOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as ProductAttributes?,
    ));
  }
}

/// @nodoc

class _$_DealDetail extends _DealDetail {
  const _$_DealDetail(
      {required this.productId,
      required this.categoryId,
      required this.isHearted,
      this.rating,
      this.ratingCount,
      required this.title,
      this.description,
      required final List<String> imageUrls,
      this.price,
      this.loanInstallment,
      this.discount,
      required this.hasCashOffersNearby,
      required this.hasLoanOffersNearby,
      required this.hasCashOffersOnline,
      required this.hasLoanOffersOnline,
      this.accountNumber,
      this.validTo,
      this.loanMinAmount,
      this.loanMaxAmount,
      this.availableLimit,
      this.usedLimit,
      this.hasNearby,
      this.hasOnline,
      this.attributes})
      : _imageUrls = imageUrls,
        super._();

  @override
  final String productId;
  @override
  final String categoryId;
  @override
  final bool isHearted;
  @override
  final double? rating;
  @override
  final int? ratingCount;
  @override
  final String title;
  @override
  final String? description;
  final List<String> _imageUrls;
  @override
  List<String> get imageUrls {
    if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_imageUrls);
  }

  @override
  final Decimal? price;
  @override
  final Decimal? loanInstallment;
  @override
  final int? discount;
// hasCashOffersNearby, hasCashOffers, hasCashOffersOnline, hasLoanOffersOnline, accountNumber
// are here because they come from same API endpoint
// they are used on Product detail page to display "Buy" buttons and following dialogs/flows
  @override
  final bool hasCashOffersNearby;
  @override
  final bool hasLoanOffersNearby;
  @override
  final bool hasCashOffersOnline;
  @override
  final bool hasLoanOffersOnline;
  @override
  final String? accountNumber;
  @override
  final DateTime? validTo;
  @override
  final Decimal? loanMinAmount;
  @override
  final Decimal? loanMaxAmount;
  @override
  final Decimal? availableLimit;
  @override
  final Decimal? usedLimit;
// hasNearby, hasOnline are deprecated, used to keep compatibility with old DealsApi
  @override
  final bool? hasNearby;
  @override
  final bool? hasOnline;
  @override
  final ProductAttributes? attributes;

  @override
  String toString() {
    return 'DealDetail(productId: $productId, categoryId: $categoryId, isHearted: $isHearted, rating: $rating, ratingCount: $ratingCount, title: $title, description: $description, imageUrls: $imageUrls, price: $price, loanInstallment: $loanInstallment, discount: $discount, hasCashOffersNearby: $hasCashOffersNearby, hasLoanOffersNearby: $hasLoanOffersNearby, hasCashOffersOnline: $hasCashOffersOnline, hasLoanOffersOnline: $hasLoanOffersOnline, accountNumber: $accountNumber, validTo: $validTo, loanMinAmount: $loanMinAmount, loanMaxAmount: $loanMaxAmount, availableLimit: $availableLimit, usedLimit: $usedLimit, hasNearby: $hasNearby, hasOnline: $hasOnline, attributes: $attributes)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DealDetail &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.isHearted, isHearted) ||
                other.isHearted == isHearted) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.ratingCount, ratingCount) ||
                other.ratingCount == ratingCount) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality()
                .equals(other._imageUrls, _imageUrls) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.loanInstallment, loanInstallment) ||
                other.loanInstallment == loanInstallment) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.hasCashOffersNearby, hasCashOffersNearby) ||
                other.hasCashOffersNearby == hasCashOffersNearby) &&
            (identical(other.hasLoanOffersNearby, hasLoanOffersNearby) ||
                other.hasLoanOffersNearby == hasLoanOffersNearby) &&
            (identical(other.hasCashOffersOnline, hasCashOffersOnline) ||
                other.hasCashOffersOnline == hasCashOffersOnline) &&
            (identical(other.hasLoanOffersOnline, hasLoanOffersOnline) ||
                other.hasLoanOffersOnline == hasLoanOffersOnline) &&
            (identical(other.accountNumber, accountNumber) ||
                other.accountNumber == accountNumber) &&
            (identical(other.validTo, validTo) || other.validTo == validTo) &&
            (identical(other.loanMinAmount, loanMinAmount) ||
                other.loanMinAmount == loanMinAmount) &&
            (identical(other.loanMaxAmount, loanMaxAmount) ||
                other.loanMaxAmount == loanMaxAmount) &&
            (identical(other.availableLimit, availableLimit) ||
                other.availableLimit == availableLimit) &&
            (identical(other.usedLimit, usedLimit) ||
                other.usedLimit == usedLimit) &&
            (identical(other.hasNearby, hasNearby) ||
                other.hasNearby == hasNearby) &&
            (identical(other.hasOnline, hasOnline) ||
                other.hasOnline == hasOnline) &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        productId,
        categoryId,
        isHearted,
        rating,
        ratingCount,
        title,
        description,
        const DeepCollectionEquality().hash(_imageUrls),
        price,
        loanInstallment,
        discount,
        hasCashOffersNearby,
        hasLoanOffersNearby,
        hasCashOffersOnline,
        hasLoanOffersOnline,
        accountNumber,
        validTo,
        loanMinAmount,
        loanMaxAmount,
        availableLimit,
        usedLimit,
        hasNearby,
        hasOnline,
        attributes
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DealDetailCopyWith<_$_DealDetail> get copyWith =>
      __$$_DealDetailCopyWithImpl<_$_DealDetail>(this, _$identity);
}

abstract class _DealDetail extends DealDetail {
  const factory _DealDetail(
      {required final String productId,
      required final String categoryId,
      required final bool isHearted,
      final double? rating,
      final int? ratingCount,
      required final String title,
      final String? description,
      required final List<String> imageUrls,
      final Decimal? price,
      final Decimal? loanInstallment,
      final int? discount,
      required final bool hasCashOffersNearby,
      required final bool hasLoanOffersNearby,
      required final bool hasCashOffersOnline,
      required final bool hasLoanOffersOnline,
      final String? accountNumber,
      final DateTime? validTo,
      final Decimal? loanMinAmount,
      final Decimal? loanMaxAmount,
      final Decimal? availableLimit,
      final Decimal? usedLimit,
      final bool? hasNearby,
      final bool? hasOnline,
      final ProductAttributes? attributes}) = _$_DealDetail;
  const _DealDetail._() : super._();

  @override
  String get productId;
  @override
  String get categoryId;
  @override
  bool get isHearted;
  @override
  double? get rating;
  @override
  int? get ratingCount;
  @override
  String get title;
  @override
  String? get description;
  @override
  List<String> get imageUrls;
  @override
  Decimal? get price;
  @override
  Decimal? get loanInstallment;
  @override
  int? get discount;
  @override // hasCashOffersNearby, hasCashOffers, hasCashOffersOnline, hasLoanOffersOnline, accountNumber
// are here because they come from same API endpoint
// they are used on Product detail page to display "Buy" buttons and following dialogs/flows
  bool get hasCashOffersNearby;
  @override
  bool get hasLoanOffersNearby;
  @override
  bool get hasCashOffersOnline;
  @override
  bool get hasLoanOffersOnline;
  @override
  String? get accountNumber;
  @override
  DateTime? get validTo;
  @override
  Decimal? get loanMinAmount;
  @override
  Decimal? get loanMaxAmount;
  @override
  Decimal? get availableLimit;
  @override
  Decimal? get usedLimit;
  @override // hasNearby, hasOnline are deprecated, used to keep compatibility with old DealsApi
  bool? get hasNearby;
  @override
  bool? get hasOnline;
  @override
  ProductAttributes? get attributes;
  @override
  @JsonKey(ignore: true)
  _$$_DealDetailCopyWith<_$_DealDetail> get copyWith =>
      throw _privateConstructorUsedError;
}
