// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deal_preview_banner.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DealPreviewBanner {
  String get text =>
      throw _privateConstructorUsedError; // ignore: invalid_annotation_target
  @JsonKey(fromJson: colorFromJson, toJson: colorToJson)
  Color? get color => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DealPreviewBannerCopyWith<DealPreviewBanner> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DealPreviewBannerCopyWith<$Res> {
  factory $DealPreviewBannerCopyWith(
          DealPreviewBanner value, $Res Function(DealPreviewBanner) then) =
      _$DealPreviewBannerCopyWithImpl<$Res, DealPreviewBanner>;
  @useResult
  $Res call(
      {String text,
      @JsonKey(fromJson: colorFromJson, toJson: colorToJson) Color? color});
}

/// @nodoc
class _$DealPreviewBannerCopyWithImpl<$Res, $Val extends DealPreviewBanner>
    implements $DealPreviewBannerCopyWith<$Res> {
  _$DealPreviewBannerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
    Object? color = freezed,
  }) {
    return _then(_value.copyWith(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as Color?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DealPreviewBannerCopyWith<$Res>
    implements $DealPreviewBannerCopyWith<$Res> {
  factory _$$_DealPreviewBannerCopyWith(_$_DealPreviewBanner value,
          $Res Function(_$_DealPreviewBanner) then) =
      __$$_DealPreviewBannerCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String text,
      @JsonKey(fromJson: colorFromJson, toJson: colorToJson) Color? color});
}

/// @nodoc
class __$$_DealPreviewBannerCopyWithImpl<$Res>
    extends _$DealPreviewBannerCopyWithImpl<$Res, _$_DealPreviewBanner>
    implements _$$_DealPreviewBannerCopyWith<$Res> {
  __$$_DealPreviewBannerCopyWithImpl(
      _$_DealPreviewBanner _value, $Res Function(_$_DealPreviewBanner) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
    Object? color = freezed,
  }) {
    return _then(_$_DealPreviewBanner(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as Color?,
    ));
  }
}

/// @nodoc

class _$_DealPreviewBanner implements _DealPreviewBanner {
  _$_DealPreviewBanner(
      {required this.text,
      @JsonKey(fromJson: colorFromJson, toJson: colorToJson) this.color});

  @override
  final String text;
// ignore: invalid_annotation_target
  @override
  @JsonKey(fromJson: colorFromJson, toJson: colorToJson)
  final Color? color;

  @override
  String toString() {
    return 'DealPreviewBanner(text: $text, color: $color)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DealPreviewBanner &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.color, color) || other.color == color));
  }

  @override
  int get hashCode => Object.hash(runtimeType, text, color);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DealPreviewBannerCopyWith<_$_DealPreviewBanner> get copyWith =>
      __$$_DealPreviewBannerCopyWithImpl<_$_DealPreviewBanner>(
          this, _$identity);
}

abstract class _DealPreviewBanner implements DealPreviewBanner {
  factory _DealPreviewBanner(
      {required final String text,
      @JsonKey(fromJson: colorFromJson, toJson: colorToJson)
      final Color? color}) = _$_DealPreviewBanner;

  @override
  String get text;
  @override // ignore: invalid_annotation_target
  @JsonKey(fromJson: colorFromJson, toJson: colorToJson)
  Color? get color;
  @override
  @JsonKey(ignore: true)
  _$$_DealPreviewBannerCopyWith<_$_DealPreviewBanner> get copyWith =>
      throw _privateConstructorUsedError;
}
