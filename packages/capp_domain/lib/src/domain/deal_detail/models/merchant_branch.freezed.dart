// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'merchant_branch.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$MerchantBranch {
  String? get merchantBranchId => throw _privateConstructorUsedError;
  Location? get location => throw _privateConstructorUsedError;
  bool? get isTopSeller => throw _privateConstructorUsedError;
  List<Media>? get icons => throw _privateConstructorUsedError;
  List<MapDealBase>? get deals => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MerchantBranchCopyWith<MerchantBranch> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MerchantBranchCopyWith<$Res> {
  factory $MerchantBranchCopyWith(
          MerchantBranch value, $Res Function(MerchantBranch) then) =
      _$MerchantBranchCopyWithImpl<$Res, MerchantBranch>;
  @useResult
  $Res call(
      {String? merchantBranchId,
      Location? location,
      bool? isTopSeller,
      List<Media>? icons,
      List<MapDealBase>? deals});

  $LocationCopyWith<$Res>? get location;
}

/// @nodoc
class _$MerchantBranchCopyWithImpl<$Res, $Val extends MerchantBranch>
    implements $MerchantBranchCopyWith<$Res> {
  _$MerchantBranchCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? merchantBranchId = freezed,
    Object? location = freezed,
    Object? isTopSeller = freezed,
    Object? icons = freezed,
    Object? deals = freezed,
  }) {
    return _then(_value.copyWith(
      merchantBranchId: freezed == merchantBranchId
          ? _value.merchantBranchId
          : merchantBranchId // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as Location?,
      isTopSeller: freezed == isTopSeller
          ? _value.isTopSeller
          : isTopSeller // ignore: cast_nullable_to_non_nullable
              as bool?,
      icons: freezed == icons
          ? _value.icons
          : icons // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      deals: freezed == deals
          ? _value.deals
          : deals // ignore: cast_nullable_to_non_nullable
              as List<MapDealBase>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LocationCopyWith<$Res>? get location {
    if (_value.location == null) {
      return null;
    }

    return $LocationCopyWith<$Res>(_value.location!, (value) {
      return _then(_value.copyWith(location: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MerchantBranchCopyWith<$Res>
    implements $MerchantBranchCopyWith<$Res> {
  factory _$$_MerchantBranchCopyWith(
          _$_MerchantBranch value, $Res Function(_$_MerchantBranch) then) =
      __$$_MerchantBranchCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? merchantBranchId,
      Location? location,
      bool? isTopSeller,
      List<Media>? icons,
      List<MapDealBase>? deals});

  @override
  $LocationCopyWith<$Res>? get location;
}

/// @nodoc
class __$$_MerchantBranchCopyWithImpl<$Res>
    extends _$MerchantBranchCopyWithImpl<$Res, _$_MerchantBranch>
    implements _$$_MerchantBranchCopyWith<$Res> {
  __$$_MerchantBranchCopyWithImpl(
      _$_MerchantBranch _value, $Res Function(_$_MerchantBranch) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? merchantBranchId = freezed,
    Object? location = freezed,
    Object? isTopSeller = freezed,
    Object? icons = freezed,
    Object? deals = freezed,
  }) {
    return _then(_$_MerchantBranch(
      merchantBranchId: freezed == merchantBranchId
          ? _value.merchantBranchId
          : merchantBranchId // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as Location?,
      isTopSeller: freezed == isTopSeller
          ? _value.isTopSeller
          : isTopSeller // ignore: cast_nullable_to_non_nullable
              as bool?,
      icons: freezed == icons
          ? _value._icons
          : icons // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      deals: freezed == deals
          ? _value._deals
          : deals // ignore: cast_nullable_to_non_nullable
              as List<MapDealBase>?,
    ));
  }
}

/// @nodoc

class _$_MerchantBranch extends _MerchantBranch {
  const _$_MerchantBranch(
      {this.merchantBranchId,
      this.location,
      this.isTopSeller,
      final List<Media>? icons,
      final List<MapDealBase>? deals})
      : _icons = icons,
        _deals = deals,
        super._();

  @override
  final String? merchantBranchId;
  @override
  final Location? location;
  @override
  final bool? isTopSeller;
  final List<Media>? _icons;
  @override
  List<Media>? get icons {
    final value = _icons;
    if (value == null) return null;
    if (_icons is EqualUnmodifiableListView) return _icons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<MapDealBase>? _deals;
  @override
  List<MapDealBase>? get deals {
    final value = _deals;
    if (value == null) return null;
    if (_deals is EqualUnmodifiableListView) return _deals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MerchantBranch(merchantBranchId: $merchantBranchId, location: $location, isTopSeller: $isTopSeller, icons: $icons, deals: $deals)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MerchantBranch &&
            (identical(other.merchantBranchId, merchantBranchId) ||
                other.merchantBranchId == merchantBranchId) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.isTopSeller, isTopSeller) ||
                other.isTopSeller == isTopSeller) &&
            const DeepCollectionEquality().equals(other._icons, _icons) &&
            const DeepCollectionEquality().equals(other._deals, _deals));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      merchantBranchId,
      location,
      isTopSeller,
      const DeepCollectionEquality().hash(_icons),
      const DeepCollectionEquality().hash(_deals));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MerchantBranchCopyWith<_$_MerchantBranch> get copyWith =>
      __$$_MerchantBranchCopyWithImpl<_$_MerchantBranch>(this, _$identity);
}

abstract class _MerchantBranch extends MerchantBranch {
  const factory _MerchantBranch(
      {final String? merchantBranchId,
      final Location? location,
      final bool? isTopSeller,
      final List<Media>? icons,
      final List<MapDealBase>? deals}) = _$_MerchantBranch;
  const _MerchantBranch._() : super._();

  @override
  String? get merchantBranchId;
  @override
  Location? get location;
  @override
  bool? get isTopSeller;
  @override
  List<Media>? get icons;
  @override
  List<MapDealBase>? get deals;
  @override
  @JsonKey(ignore: true)
  _$$_MerchantBranchCopyWith<_$_MerchantBranch> get copyWith =>
      throw _privateConstructorUsedError;
}
