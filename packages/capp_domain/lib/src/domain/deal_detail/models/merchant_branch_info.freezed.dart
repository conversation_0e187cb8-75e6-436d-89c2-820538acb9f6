// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'merchant_branch_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$MerchantBranchInfo {
  String? get merchantBranchId => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  Location? get location => throw _privateConstructorUsedError;
  List<Media>? get icons => throw _privateConstructorUsedError;
  String? get salesroomCode => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MerchantBranchInfoCopyWith<MerchantBranchInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MerchantBranchInfoCopyWith<$Res> {
  factory $MerchantBranchInfoCopyWith(
          MerchantBranchInfo value, $Res Function(MerchantBranchInfo) then) =
      _$MerchantBranchInfoCopyWithImpl<$Res, MerchantBranchInfo>;
  @useResult
  $Res call(
      {String? merchantBranchId,
      String? name,
      String? address,
      Location? location,
      List<Media>? icons,
      String? salesroomCode});

  $LocationCopyWith<$Res>? get location;
}

/// @nodoc
class _$MerchantBranchInfoCopyWithImpl<$Res, $Val extends MerchantBranchInfo>
    implements $MerchantBranchInfoCopyWith<$Res> {
  _$MerchantBranchInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? merchantBranchId = freezed,
    Object? name = freezed,
    Object? address = freezed,
    Object? location = freezed,
    Object? icons = freezed,
    Object? salesroomCode = freezed,
  }) {
    return _then(_value.copyWith(
      merchantBranchId: freezed == merchantBranchId
          ? _value.merchantBranchId
          : merchantBranchId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as Location?,
      icons: freezed == icons
          ? _value.icons
          : icons // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      salesroomCode: freezed == salesroomCode
          ? _value.salesroomCode
          : salesroomCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LocationCopyWith<$Res>? get location {
    if (_value.location == null) {
      return null;
    }

    return $LocationCopyWith<$Res>(_value.location!, (value) {
      return _then(_value.copyWith(location: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MerchantBranchInfoCopyWith<$Res>
    implements $MerchantBranchInfoCopyWith<$Res> {
  factory _$$_MerchantBranchInfoCopyWith(_$_MerchantBranchInfo value,
          $Res Function(_$_MerchantBranchInfo) then) =
      __$$_MerchantBranchInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? merchantBranchId,
      String? name,
      String? address,
      Location? location,
      List<Media>? icons,
      String? salesroomCode});

  @override
  $LocationCopyWith<$Res>? get location;
}

/// @nodoc
class __$$_MerchantBranchInfoCopyWithImpl<$Res>
    extends _$MerchantBranchInfoCopyWithImpl<$Res, _$_MerchantBranchInfo>
    implements _$$_MerchantBranchInfoCopyWith<$Res> {
  __$$_MerchantBranchInfoCopyWithImpl(
      _$_MerchantBranchInfo _value, $Res Function(_$_MerchantBranchInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? merchantBranchId = freezed,
    Object? name = freezed,
    Object? address = freezed,
    Object? location = freezed,
    Object? icons = freezed,
    Object? salesroomCode = freezed,
  }) {
    return _then(_$_MerchantBranchInfo(
      merchantBranchId: freezed == merchantBranchId
          ? _value.merchantBranchId
          : merchantBranchId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as Location?,
      icons: freezed == icons
          ? _value._icons
          : icons // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      salesroomCode: freezed == salesroomCode
          ? _value.salesroomCode
          : salesroomCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_MerchantBranchInfo extends _MerchantBranchInfo {
  const _$_MerchantBranchInfo(
      {this.merchantBranchId,
      this.name,
      this.address,
      this.location,
      final List<Media>? icons,
      this.salesroomCode})
      : _icons = icons,
        super._();

  @override
  final String? merchantBranchId;
  @override
  final String? name;
  @override
  final String? address;
  @override
  final Location? location;
  final List<Media>? _icons;
  @override
  List<Media>? get icons {
    final value = _icons;
    if (value == null) return null;
    if (_icons is EqualUnmodifiableListView) return _icons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? salesroomCode;

  @override
  String toString() {
    return 'MerchantBranchInfo(merchantBranchId: $merchantBranchId, name: $name, address: $address, location: $location, icons: $icons, salesroomCode: $salesroomCode)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MerchantBranchInfo &&
            (identical(other.merchantBranchId, merchantBranchId) ||
                other.merchantBranchId == merchantBranchId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.location, location) ||
                other.location == location) &&
            const DeepCollectionEquality().equals(other._icons, _icons) &&
            (identical(other.salesroomCode, salesroomCode) ||
                other.salesroomCode == salesroomCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, merchantBranchId, name, address,
      location, const DeepCollectionEquality().hash(_icons), salesroomCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MerchantBranchInfoCopyWith<_$_MerchantBranchInfo> get copyWith =>
      __$$_MerchantBranchInfoCopyWithImpl<_$_MerchantBranchInfo>(
          this, _$identity);
}

abstract class _MerchantBranchInfo extends MerchantBranchInfo {
  const factory _MerchantBranchInfo(
      {final String? merchantBranchId,
      final String? name,
      final String? address,
      final Location? location,
      final List<Media>? icons,
      final String? salesroomCode}) = _$_MerchantBranchInfo;
  const _MerchantBranchInfo._() : super._();

  @override
  String? get merchantBranchId;
  @override
  String? get name;
  @override
  String? get address;
  @override
  Location? get location;
  @override
  List<Media>? get icons;
  @override
  String? get salesroomCode;
  @override
  @JsonKey(ignore: true)
  _$$_MerchantBranchInfoCopyWith<_$_MerchantBranchInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$MerchantBranchInfoList {
  List<MerchantBranchInfo>? get merchants => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  int? get pageNumber => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MerchantBranchInfoListCopyWith<MerchantBranchInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MerchantBranchInfoListCopyWith<$Res> {
  factory $MerchantBranchInfoListCopyWith(MerchantBranchInfoList value,
          $Res Function(MerchantBranchInfoList) then) =
      _$MerchantBranchInfoListCopyWithImpl<$Res, MerchantBranchInfoList>;
  @useResult
  $Res call(
      {List<MerchantBranchInfo>? merchants, int? pageSize, int? pageNumber});
}

/// @nodoc
class _$MerchantBranchInfoListCopyWithImpl<$Res,
        $Val extends MerchantBranchInfoList>
    implements $MerchantBranchInfoListCopyWith<$Res> {
  _$MerchantBranchInfoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? merchants = freezed,
    Object? pageSize = freezed,
    Object? pageNumber = freezed,
  }) {
    return _then(_value.copyWith(
      merchants: freezed == merchants
          ? _value.merchants
          : merchants // ignore: cast_nullable_to_non_nullable
              as List<MerchantBranchInfo>?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      pageNumber: freezed == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MerchantBranchInfoListCopyWith<$Res>
    implements $MerchantBranchInfoListCopyWith<$Res> {
  factory _$$_MerchantBranchInfoListCopyWith(_$_MerchantBranchInfoList value,
          $Res Function(_$_MerchantBranchInfoList) then) =
      __$$_MerchantBranchInfoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<MerchantBranchInfo>? merchants, int? pageSize, int? pageNumber});
}

/// @nodoc
class __$$_MerchantBranchInfoListCopyWithImpl<$Res>
    extends _$MerchantBranchInfoListCopyWithImpl<$Res,
        _$_MerchantBranchInfoList>
    implements _$$_MerchantBranchInfoListCopyWith<$Res> {
  __$$_MerchantBranchInfoListCopyWithImpl(_$_MerchantBranchInfoList _value,
      $Res Function(_$_MerchantBranchInfoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? merchants = freezed,
    Object? pageSize = freezed,
    Object? pageNumber = freezed,
  }) {
    return _then(_$_MerchantBranchInfoList(
      merchants: freezed == merchants
          ? _value._merchants
          : merchants // ignore: cast_nullable_to_non_nullable
              as List<MerchantBranchInfo>?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      pageNumber: freezed == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$_MerchantBranchInfoList extends _MerchantBranchInfoList {
  const _$_MerchantBranchInfoList(
      {final List<MerchantBranchInfo>? merchants,
      this.pageSize,
      this.pageNumber})
      : _merchants = merchants,
        super._();

  final List<MerchantBranchInfo>? _merchants;
  @override
  List<MerchantBranchInfo>? get merchants {
    final value = _merchants;
    if (value == null) return null;
    if (_merchants is EqualUnmodifiableListView) return _merchants;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? pageSize;
  @override
  final int? pageNumber;

  @override
  String toString() {
    return 'MerchantBranchInfoList(merchants: $merchants, pageSize: $pageSize, pageNumber: $pageNumber)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MerchantBranchInfoList &&
            const DeepCollectionEquality()
                .equals(other._merchants, _merchants) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.pageNumber, pageNumber) ||
                other.pageNumber == pageNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_merchants), pageSize, pageNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MerchantBranchInfoListCopyWith<_$_MerchantBranchInfoList> get copyWith =>
      __$$_MerchantBranchInfoListCopyWithImpl<_$_MerchantBranchInfoList>(
          this, _$identity);
}

abstract class _MerchantBranchInfoList extends MerchantBranchInfoList {
  const factory _MerchantBranchInfoList(
      {final List<MerchantBranchInfo>? merchants,
      final int? pageSize,
      final int? pageNumber}) = _$_MerchantBranchInfoList;
  const _MerchantBranchInfoList._() : super._();

  @override
  List<MerchantBranchInfo>? get merchants;
  @override
  int? get pageSize;
  @override
  int? get pageNumber;
  @override
  @JsonKey(ignore: true)
  _$$_MerchantBranchInfoListCopyWith<_$_MerchantBranchInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}
