// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_detail.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ProductDetail {
  String get productId => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get categoryId => throw _privateConstructorUsedError;
  String? get commodityCategory => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get heroUrl => throw _privateConstructorUsedError;
  String? get ean => throw _privateConstructorUsedError;
  String? get manufacturer => throw _privateConstructorUsedError;
  String? get hoselManufacturer => throw _privateConstructorUsedError;
  List<Media>? get media => throw _privateConstructorUsedError;
  double? get rating => throw _privateConstructorUsedError;
  int? get ratingCount => throw _privateConstructorUsedError;
  ProductAttributes? get attributes => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ProductDetailCopyWith<ProductDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductDetailCopyWith<$Res> {
  factory $ProductDetailCopyWith(
          ProductDetail value, $Res Function(ProductDetail) then) =
      _$ProductDetailCopyWithImpl<$Res, ProductDetail>;
  @useResult
  $Res call(
      {String productId,
      String? title,
      String? categoryId,
      String? commodityCategory,
      String? description,
      String? heroUrl,
      String? ean,
      String? manufacturer,
      String? hoselManufacturer,
      List<Media>? media,
      double? rating,
      int? ratingCount,
      ProductAttributes? attributes});

  $ProductAttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class _$ProductDetailCopyWithImpl<$Res, $Val extends ProductDetail>
    implements $ProductDetailCopyWith<$Res> {
  _$ProductDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? title = freezed,
    Object? categoryId = freezed,
    Object? commodityCategory = freezed,
    Object? description = freezed,
    Object? heroUrl = freezed,
    Object? ean = freezed,
    Object? manufacturer = freezed,
    Object? hoselManufacturer = freezed,
    Object? media = freezed,
    Object? rating = freezed,
    Object? ratingCount = freezed,
    Object? attributes = freezed,
  }) {
    return _then(_value.copyWith(
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      commodityCategory: freezed == commodityCategory
          ? _value.commodityCategory
          : commodityCategory // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      heroUrl: freezed == heroUrl
          ? _value.heroUrl
          : heroUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ean: freezed == ean
          ? _value.ean
          : ean // ignore: cast_nullable_to_non_nullable
              as String?,
      manufacturer: freezed == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      hoselManufacturer: freezed == hoselManufacturer
          ? _value.hoselManufacturer
          : hoselManufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      media: freezed == media
          ? _value.media
          : media // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      ratingCount: freezed == ratingCount
          ? _value.ratingCount
          : ratingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as ProductAttributes?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProductAttributesCopyWith<$Res>? get attributes {
    if (_value.attributes == null) {
      return null;
    }

    return $ProductAttributesCopyWith<$Res>(_value.attributes!, (value) {
      return _then(_value.copyWith(attributes: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ProductDetailCopyWith<$Res>
    implements $ProductDetailCopyWith<$Res> {
  factory _$$_ProductDetailCopyWith(
          _$_ProductDetail value, $Res Function(_$_ProductDetail) then) =
      __$$_ProductDetailCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String productId,
      String? title,
      String? categoryId,
      String? commodityCategory,
      String? description,
      String? heroUrl,
      String? ean,
      String? manufacturer,
      String? hoselManufacturer,
      List<Media>? media,
      double? rating,
      int? ratingCount,
      ProductAttributes? attributes});

  @override
  $ProductAttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class __$$_ProductDetailCopyWithImpl<$Res>
    extends _$ProductDetailCopyWithImpl<$Res, _$_ProductDetail>
    implements _$$_ProductDetailCopyWith<$Res> {
  __$$_ProductDetailCopyWithImpl(
      _$_ProductDetail _value, $Res Function(_$_ProductDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? title = freezed,
    Object? categoryId = freezed,
    Object? commodityCategory = freezed,
    Object? description = freezed,
    Object? heroUrl = freezed,
    Object? ean = freezed,
    Object? manufacturer = freezed,
    Object? hoselManufacturer = freezed,
    Object? media = freezed,
    Object? rating = freezed,
    Object? ratingCount = freezed,
    Object? attributes = freezed,
  }) {
    return _then(_$_ProductDetail(
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      commodityCategory: freezed == commodityCategory
          ? _value.commodityCategory
          : commodityCategory // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      heroUrl: freezed == heroUrl
          ? _value.heroUrl
          : heroUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ean: freezed == ean
          ? _value.ean
          : ean // ignore: cast_nullable_to_non_nullable
              as String?,
      manufacturer: freezed == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      hoselManufacturer: freezed == hoselManufacturer
          ? _value.hoselManufacturer
          : hoselManufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      media: freezed == media
          ? _value._media
          : media // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      ratingCount: freezed == ratingCount
          ? _value.ratingCount
          : ratingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as ProductAttributes?,
    ));
  }
}

/// @nodoc

class _$_ProductDetail implements _ProductDetail {
  _$_ProductDetail(
      {required this.productId,
      this.title,
      this.categoryId,
      this.commodityCategory,
      this.description,
      this.heroUrl,
      this.ean,
      this.manufacturer,
      this.hoselManufacturer,
      final List<Media>? media,
      this.rating,
      this.ratingCount,
      this.attributes})
      : _media = media;

  @override
  final String productId;
  @override
  final String? title;
  @override
  final String? categoryId;
  @override
  final String? commodityCategory;
  @override
  final String? description;
  @override
  final String? heroUrl;
  @override
  final String? ean;
  @override
  final String? manufacturer;
  @override
  final String? hoselManufacturer;
  final List<Media>? _media;
  @override
  List<Media>? get media {
    final value = _media;
    if (value == null) return null;
    if (_media is EqualUnmodifiableListView) return _media;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final double? rating;
  @override
  final int? ratingCount;
  @override
  final ProductAttributes? attributes;

  @override
  String toString() {
    return 'ProductDetail(productId: $productId, title: $title, categoryId: $categoryId, commodityCategory: $commodityCategory, description: $description, heroUrl: $heroUrl, ean: $ean, manufacturer: $manufacturer, hoselManufacturer: $hoselManufacturer, media: $media, rating: $rating, ratingCount: $ratingCount, attributes: $attributes)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ProductDetail &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.commodityCategory, commodityCategory) ||
                other.commodityCategory == commodityCategory) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.heroUrl, heroUrl) || other.heroUrl == heroUrl) &&
            (identical(other.ean, ean) || other.ean == ean) &&
            (identical(other.manufacturer, manufacturer) ||
                other.manufacturer == manufacturer) &&
            (identical(other.hoselManufacturer, hoselManufacturer) ||
                other.hoselManufacturer == hoselManufacturer) &&
            const DeepCollectionEquality().equals(other._media, _media) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.ratingCount, ratingCount) ||
                other.ratingCount == ratingCount) &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      productId,
      title,
      categoryId,
      commodityCategory,
      description,
      heroUrl,
      ean,
      manufacturer,
      hoselManufacturer,
      const DeepCollectionEquality().hash(_media),
      rating,
      ratingCount,
      attributes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProductDetailCopyWith<_$_ProductDetail> get copyWith =>
      __$$_ProductDetailCopyWithImpl<_$_ProductDetail>(this, _$identity);
}

abstract class _ProductDetail implements ProductDetail {
  factory _ProductDetail(
      {required final String productId,
      final String? title,
      final String? categoryId,
      final String? commodityCategory,
      final String? description,
      final String? heroUrl,
      final String? ean,
      final String? manufacturer,
      final String? hoselManufacturer,
      final List<Media>? media,
      final double? rating,
      final int? ratingCount,
      final ProductAttributes? attributes}) = _$_ProductDetail;

  @override
  String get productId;
  @override
  String? get title;
  @override
  String? get categoryId;
  @override
  String? get commodityCategory;
  @override
  String? get description;
  @override
  String? get heroUrl;
  @override
  String? get ean;
  @override
  String? get manufacturer;
  @override
  String? get hoselManufacturer;
  @override
  List<Media>? get media;
  @override
  double? get rating;
  @override
  int? get ratingCount;
  @override
  ProductAttributes? get attributes;
  @override
  @JsonKey(ignore: true)
  _$$_ProductDetailCopyWith<_$_ProductDetail> get copyWith =>
      throw _privateConstructorUsedError;
}
