// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_detail_arcadier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ProductDetailArcadier {
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get terms => throw _privateConstructorUsedError;
  int? get monthlyInstallment => throw _privateConstructorUsedError;
  double? get interestRate => throw _privateConstructorUsedError;
  double? get amount => throw _privateConstructorUsedError;
  String? get brandId => throw _privateConstructorUsedError;
  String? get brandName => throw _privateConstructorUsedError;
  String? get shortdesc => throw _privateConstructorUsedError;
  String? get longDesc => throw _privateConstructorUsedError;
  int? get brandRating => throw _privateConstructorUsedError;
  List<Media>? get hero => throw _privateConstructorUsedError;
  String? get promoType => throw _privateConstructorUsedError;
  double? get discountPercent => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ProductDetailArcadierCopyWith<ProductDetailArcadier> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductDetailArcadierCopyWith<$Res> {
  factory $ProductDetailArcadierCopyWith(ProductDetailArcadier value,
          $Res Function(ProductDetailArcadier) then) =
      _$ProductDetailArcadierCopyWithImpl<$Res, ProductDetailArcadier>;
  @useResult
  $Res call(
      {String? id,
      String? name,
      int? terms,
      int? monthlyInstallment,
      double? interestRate,
      double? amount,
      String? brandId,
      String? brandName,
      String? shortdesc,
      String? longDesc,
      int? brandRating,
      List<Media>? hero,
      String? promoType,
      double? discountPercent});
}

/// @nodoc
class _$ProductDetailArcadierCopyWithImpl<$Res,
        $Val extends ProductDetailArcadier>
    implements $ProductDetailArcadierCopyWith<$Res> {
  _$ProductDetailArcadierCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? terms = freezed,
    Object? monthlyInstallment = freezed,
    Object? interestRate = freezed,
    Object? amount = freezed,
    Object? brandId = freezed,
    Object? brandName = freezed,
    Object? shortdesc = freezed,
    Object? longDesc = freezed,
    Object? brandRating = freezed,
    Object? hero = freezed,
    Object? promoType = freezed,
    Object? discountPercent = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      terms: freezed == terms
          ? _value.terms
          : terms // ignore: cast_nullable_to_non_nullable
              as int?,
      monthlyInstallment: freezed == monthlyInstallment
          ? _value.monthlyInstallment
          : monthlyInstallment // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      brandId: freezed == brandId
          ? _value.brandId
          : brandId // ignore: cast_nullable_to_non_nullable
              as String?,
      brandName: freezed == brandName
          ? _value.brandName
          : brandName // ignore: cast_nullable_to_non_nullable
              as String?,
      shortdesc: freezed == shortdesc
          ? _value.shortdesc
          : shortdesc // ignore: cast_nullable_to_non_nullable
              as String?,
      longDesc: freezed == longDesc
          ? _value.longDesc
          : longDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      brandRating: freezed == brandRating
          ? _value.brandRating
          : brandRating // ignore: cast_nullable_to_non_nullable
              as int?,
      hero: freezed == hero
          ? _value.hero
          : hero // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      promoType: freezed == promoType
          ? _value.promoType
          : promoType // ignore: cast_nullable_to_non_nullable
              as String?,
      discountPercent: freezed == discountPercent
          ? _value.discountPercent
          : discountPercent // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ProductDetailArcadierCopyWith<$Res>
    implements $ProductDetailArcadierCopyWith<$Res> {
  factory _$$_ProductDetailArcadierCopyWith(_$_ProductDetailArcadier value,
          $Res Function(_$_ProductDetailArcadier) then) =
      __$$_ProductDetailArcadierCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? name,
      int? terms,
      int? monthlyInstallment,
      double? interestRate,
      double? amount,
      String? brandId,
      String? brandName,
      String? shortdesc,
      String? longDesc,
      int? brandRating,
      List<Media>? hero,
      String? promoType,
      double? discountPercent});
}

/// @nodoc
class __$$_ProductDetailArcadierCopyWithImpl<$Res>
    extends _$ProductDetailArcadierCopyWithImpl<$Res, _$_ProductDetailArcadier>
    implements _$$_ProductDetailArcadierCopyWith<$Res> {
  __$$_ProductDetailArcadierCopyWithImpl(_$_ProductDetailArcadier _value,
      $Res Function(_$_ProductDetailArcadier) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? terms = freezed,
    Object? monthlyInstallment = freezed,
    Object? interestRate = freezed,
    Object? amount = freezed,
    Object? brandId = freezed,
    Object? brandName = freezed,
    Object? shortdesc = freezed,
    Object? longDesc = freezed,
    Object? brandRating = freezed,
    Object? hero = freezed,
    Object? promoType = freezed,
    Object? discountPercent = freezed,
  }) {
    return _then(_$_ProductDetailArcadier(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      terms: freezed == terms
          ? _value.terms
          : terms // ignore: cast_nullable_to_non_nullable
              as int?,
      monthlyInstallment: freezed == monthlyInstallment
          ? _value.monthlyInstallment
          : monthlyInstallment // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      brandId: freezed == brandId
          ? _value.brandId
          : brandId // ignore: cast_nullable_to_non_nullable
              as String?,
      brandName: freezed == brandName
          ? _value.brandName
          : brandName // ignore: cast_nullable_to_non_nullable
              as String?,
      shortdesc: freezed == shortdesc
          ? _value.shortdesc
          : shortdesc // ignore: cast_nullable_to_non_nullable
              as String?,
      longDesc: freezed == longDesc
          ? _value.longDesc
          : longDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      brandRating: freezed == brandRating
          ? _value.brandRating
          : brandRating // ignore: cast_nullable_to_non_nullable
              as int?,
      hero: freezed == hero
          ? _value._hero
          : hero // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      promoType: freezed == promoType
          ? _value.promoType
          : promoType // ignore: cast_nullable_to_non_nullable
              as String?,
      discountPercent: freezed == discountPercent
          ? _value.discountPercent
          : discountPercent // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

class _$_ProductDetailArcadier implements _ProductDetailArcadier {
  _$_ProductDetailArcadier(
      {required this.id,
      this.name,
      this.terms,
      this.monthlyInstallment,
      this.interestRate,
      this.amount,
      this.brandId,
      this.brandName,
      this.shortdesc,
      this.longDesc,
      this.brandRating,
      final List<Media>? hero,
      this.promoType,
      this.discountPercent})
      : _hero = hero;

  @override
  final String? id;
  @override
  final String? name;
  @override
  final int? terms;
  @override
  final int? monthlyInstallment;
  @override
  final double? interestRate;
  @override
  final double? amount;
  @override
  final String? brandId;
  @override
  final String? brandName;
  @override
  final String? shortdesc;
  @override
  final String? longDesc;
  @override
  final int? brandRating;
  final List<Media>? _hero;
  @override
  List<Media>? get hero {
    final value = _hero;
    if (value == null) return null;
    if (_hero is EqualUnmodifiableListView) return _hero;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? promoType;
  @override
  final double? discountPercent;

  @override
  String toString() {
    return 'ProductDetailArcadier(id: $id, name: $name, terms: $terms, monthlyInstallment: $monthlyInstallment, interestRate: $interestRate, amount: $amount, brandId: $brandId, brandName: $brandName, shortdesc: $shortdesc, longDesc: $longDesc, brandRating: $brandRating, hero: $hero, promoType: $promoType, discountPercent: $discountPercent)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ProductDetailArcadier &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.terms, terms) || other.terms == terms) &&
            (identical(other.monthlyInstallment, monthlyInstallment) ||
                other.monthlyInstallment == monthlyInstallment) &&
            (identical(other.interestRate, interestRate) ||
                other.interestRate == interestRate) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.brandId, brandId) || other.brandId == brandId) &&
            (identical(other.brandName, brandName) ||
                other.brandName == brandName) &&
            (identical(other.shortdesc, shortdesc) ||
                other.shortdesc == shortdesc) &&
            (identical(other.longDesc, longDesc) ||
                other.longDesc == longDesc) &&
            (identical(other.brandRating, brandRating) ||
                other.brandRating == brandRating) &&
            const DeepCollectionEquality().equals(other._hero, _hero) &&
            (identical(other.promoType, promoType) ||
                other.promoType == promoType) &&
            (identical(other.discountPercent, discountPercent) ||
                other.discountPercent == discountPercent));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      terms,
      monthlyInstallment,
      interestRate,
      amount,
      brandId,
      brandName,
      shortdesc,
      longDesc,
      brandRating,
      const DeepCollectionEquality().hash(_hero),
      promoType,
      discountPercent);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProductDetailArcadierCopyWith<_$_ProductDetailArcadier> get copyWith =>
      __$$_ProductDetailArcadierCopyWithImpl<_$_ProductDetailArcadier>(
          this, _$identity);
}

abstract class _ProductDetailArcadier implements ProductDetailArcadier {
  factory _ProductDetailArcadier(
      {required final String? id,
      final String? name,
      final int? terms,
      final int? monthlyInstallment,
      final double? interestRate,
      final double? amount,
      final String? brandId,
      final String? brandName,
      final String? shortdesc,
      final String? longDesc,
      final int? brandRating,
      final List<Media>? hero,
      final String? promoType,
      final double? discountPercent}) = _$_ProductDetailArcadier;

  @override
  String? get id;
  @override
  String? get name;
  @override
  int? get terms;
  @override
  int? get monthlyInstallment;
  @override
  double? get interestRate;
  @override
  double? get amount;
  @override
  String? get brandId;
  @override
  String? get brandName;
  @override
  String? get shortdesc;
  @override
  String? get longDesc;
  @override
  int? get brandRating;
  @override
  List<Media>? get hero;
  @override
  String? get promoType;
  @override
  double? get discountPercent;
  @override
  @JsonKey(ignore: true)
  _$$_ProductDetailArcadierCopyWith<_$_ProductDetailArcadier> get copyWith =>
      throw _privateConstructorUsedError;
}
