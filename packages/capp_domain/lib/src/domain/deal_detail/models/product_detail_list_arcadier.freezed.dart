// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_detail_list_arcadier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ProductDetailListArcadier {
  num? get totalHits => throw _privateConstructorUsedError;
  num? get pageSize => throw _privateConstructorUsedError;
  num? get pageNumber => throw _privateConstructorUsedError;
  List<ProductDetailArcadier>? get products =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ProductDetailListArcadierCopyWith<ProductDetailListArcadier> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductDetailListArcadierCopyWith<$Res> {
  factory $ProductDetailListArcadierCopyWith(ProductDetailListArcadier value,
          $Res Function(ProductDetailListArcadier) then) =
      _$ProductDetailListArcadierCopyWithImpl<$Res, ProductDetailListArcadier>;
  @useResult
  $Res call(
      {num? totalHits,
      num? pageSize,
      num? pageNumber,
      List<ProductDetailArcadier>? products});
}

/// @nodoc
class _$ProductDetailListArcadierCopyWithImpl<$Res,
        $Val extends ProductDetailListArcadier>
    implements $ProductDetailListArcadierCopyWith<$Res> {
  _$ProductDetailListArcadierCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalHits = freezed,
    Object? pageSize = freezed,
    Object? pageNumber = freezed,
    Object? products = freezed,
  }) {
    return _then(_value.copyWith(
      totalHits: freezed == totalHits
          ? _value.totalHits
          : totalHits // ignore: cast_nullable_to_non_nullable
              as num?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as num?,
      pageNumber: freezed == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as num?,
      products: freezed == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ProductDetailArcadier>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ProductDetailListArcadierCopyWith<$Res>
    implements $ProductDetailListArcadierCopyWith<$Res> {
  factory _$$_ProductDetailListArcadierCopyWith(
          _$_ProductDetailListArcadier value,
          $Res Function(_$_ProductDetailListArcadier) then) =
      __$$_ProductDetailListArcadierCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {num? totalHits,
      num? pageSize,
      num? pageNumber,
      List<ProductDetailArcadier>? products});
}

/// @nodoc
class __$$_ProductDetailListArcadierCopyWithImpl<$Res>
    extends _$ProductDetailListArcadierCopyWithImpl<$Res,
        _$_ProductDetailListArcadier>
    implements _$$_ProductDetailListArcadierCopyWith<$Res> {
  __$$_ProductDetailListArcadierCopyWithImpl(
      _$_ProductDetailListArcadier _value,
      $Res Function(_$_ProductDetailListArcadier) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalHits = freezed,
    Object? pageSize = freezed,
    Object? pageNumber = freezed,
    Object? products = freezed,
  }) {
    return _then(_$_ProductDetailListArcadier(
      totalHits: freezed == totalHits
          ? _value.totalHits
          : totalHits // ignore: cast_nullable_to_non_nullable
              as num?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as num?,
      pageNumber: freezed == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as num?,
      products: freezed == products
          ? _value._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ProductDetailArcadier>?,
    ));
  }
}

/// @nodoc

class _$_ProductDetailListArcadier implements _ProductDetailListArcadier {
  const _$_ProductDetailListArcadier(
      {this.totalHits,
      this.pageSize,
      this.pageNumber,
      final List<ProductDetailArcadier>? products})
      : _products = products;

  @override
  final num? totalHits;
  @override
  final num? pageSize;
  @override
  final num? pageNumber;
  final List<ProductDetailArcadier>? _products;
  @override
  List<ProductDetailArcadier>? get products {
    final value = _products;
    if (value == null) return null;
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProductDetailListArcadier(totalHits: $totalHits, pageSize: $pageSize, pageNumber: $pageNumber, products: $products)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ProductDetailListArcadier &&
            (identical(other.totalHits, totalHits) ||
                other.totalHits == totalHits) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.pageNumber, pageNumber) ||
                other.pageNumber == pageNumber) &&
            const DeepCollectionEquality().equals(other._products, _products));
  }

  @override
  int get hashCode => Object.hash(runtimeType, totalHits, pageSize, pageNumber,
      const DeepCollectionEquality().hash(_products));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProductDetailListArcadierCopyWith<_$_ProductDetailListArcadier>
      get copyWith => __$$_ProductDetailListArcadierCopyWithImpl<
          _$_ProductDetailListArcadier>(this, _$identity);
}

abstract class _ProductDetailListArcadier implements ProductDetailListArcadier {
  const factory _ProductDetailListArcadier(
          {final num? totalHits,
          final num? pageSize,
          final num? pageNumber,
          final List<ProductDetailArcadier>? products}) =
      _$_ProductDetailListArcadier;

  @override
  num? get totalHits;
  @override
  num? get pageSize;
  @override
  num? get pageNumber;
  @override
  List<ProductDetailArcadier>? get products;
  @override
  @JsonKey(ignore: true)
  _$$_ProductDetailListArcadierCopyWith<_$_ProductDetailListArcadier>
      get copyWith => throw _privateConstructorUsedError;
}
