// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_offer_deal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ProductOfferDealModel {
  String get productOfferId => throw _privateConstructorUsedError;
  DateTime? get validFrom => throw _privateConstructorUsedError;
  DateTime? get validTo => throw _privateConstructorUsedError;
  MerchantBranchInfo? get merchant => throw _privateConstructorUsedError;
  int? get distanceInMinutes => throw _privateConstructorUsedError;
  List<Price>? get offers => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ProductOfferDealModelCopyWith<ProductOfferDealModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductOfferDealModelCopyWith<$Res> {
  factory $ProductOfferDealModelCopyWith(ProductOfferDealModel value,
          $Res Function(ProductOfferDealModel) then) =
      _$ProductOfferDealModelCopyWithImpl<$Res, ProductOfferDealModel>;
  @useResult
  $Res call(
      {String productOfferId,
      DateTime? validFrom,
      DateTime? validTo,
      MerchantBranchInfo? merchant,
      int? distanceInMinutes,
      List<Price>? offers});

  $MerchantBranchInfoCopyWith<$Res>? get merchant;
}

/// @nodoc
class _$ProductOfferDealModelCopyWithImpl<$Res,
        $Val extends ProductOfferDealModel>
    implements $ProductOfferDealModelCopyWith<$Res> {
  _$ProductOfferDealModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productOfferId = null,
    Object? validFrom = freezed,
    Object? validTo = freezed,
    Object? merchant = freezed,
    Object? distanceInMinutes = freezed,
    Object? offers = freezed,
  }) {
    return _then(_value.copyWith(
      productOfferId: null == productOfferId
          ? _value.productOfferId
          : productOfferId // ignore: cast_nullable_to_non_nullable
              as String,
      validFrom: freezed == validFrom
          ? _value.validFrom
          : validFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      merchant: freezed == merchant
          ? _value.merchant
          : merchant // ignore: cast_nullable_to_non_nullable
              as MerchantBranchInfo?,
      distanceInMinutes: freezed == distanceInMinutes
          ? _value.distanceInMinutes
          : distanceInMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      offers: freezed == offers
          ? _value.offers
          : offers // ignore: cast_nullable_to_non_nullable
              as List<Price>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MerchantBranchInfoCopyWith<$Res>? get merchant {
    if (_value.merchant == null) {
      return null;
    }

    return $MerchantBranchInfoCopyWith<$Res>(_value.merchant!, (value) {
      return _then(_value.copyWith(merchant: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ProductOfferDealModellCopyWith<$Res>
    implements $ProductOfferDealModelCopyWith<$Res> {
  factory _$$_ProductOfferDealModellCopyWith(_$_ProductOfferDealModell value,
          $Res Function(_$_ProductOfferDealModell) then) =
      __$$_ProductOfferDealModellCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String productOfferId,
      DateTime? validFrom,
      DateTime? validTo,
      MerchantBranchInfo? merchant,
      int? distanceInMinutes,
      List<Price>? offers});

  @override
  $MerchantBranchInfoCopyWith<$Res>? get merchant;
}

/// @nodoc
class __$$_ProductOfferDealModellCopyWithImpl<$Res>
    extends _$ProductOfferDealModelCopyWithImpl<$Res, _$_ProductOfferDealModell>
    implements _$$_ProductOfferDealModellCopyWith<$Res> {
  __$$_ProductOfferDealModellCopyWithImpl(_$_ProductOfferDealModell _value,
      $Res Function(_$_ProductOfferDealModell) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productOfferId = null,
    Object? validFrom = freezed,
    Object? validTo = freezed,
    Object? merchant = freezed,
    Object? distanceInMinutes = freezed,
    Object? offers = freezed,
  }) {
    return _then(_$_ProductOfferDealModell(
      productOfferId: null == productOfferId
          ? _value.productOfferId
          : productOfferId // ignore: cast_nullable_to_non_nullable
              as String,
      validFrom: freezed == validFrom
          ? _value.validFrom
          : validFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      merchant: freezed == merchant
          ? _value.merchant
          : merchant // ignore: cast_nullable_to_non_nullable
              as MerchantBranchInfo?,
      distanceInMinutes: freezed == distanceInMinutes
          ? _value.distanceInMinutes
          : distanceInMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      offers: freezed == offers
          ? _value._offers
          : offers // ignore: cast_nullable_to_non_nullable
              as List<Price>?,
    ));
  }
}

/// @nodoc

class _$_ProductOfferDealModell extends _ProductOfferDealModell {
  _$_ProductOfferDealModell(
      {required this.productOfferId,
      this.validFrom,
      this.validTo,
      this.merchant,
      this.distanceInMinutes,
      final List<Price>? offers})
      : _offers = offers,
        super._();

  @override
  final String productOfferId;
  @override
  final DateTime? validFrom;
  @override
  final DateTime? validTo;
  @override
  final MerchantBranchInfo? merchant;
  @override
  final int? distanceInMinutes;
  final List<Price>? _offers;
  @override
  List<Price>? get offers {
    final value = _offers;
    if (value == null) return null;
    if (_offers is EqualUnmodifiableListView) return _offers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProductOfferDealModel(productOfferId: $productOfferId, validFrom: $validFrom, validTo: $validTo, merchant: $merchant, distanceInMinutes: $distanceInMinutes, offers: $offers)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ProductOfferDealModell &&
            (identical(other.productOfferId, productOfferId) ||
                other.productOfferId == productOfferId) &&
            (identical(other.validFrom, validFrom) ||
                other.validFrom == validFrom) &&
            (identical(other.validTo, validTo) || other.validTo == validTo) &&
            (identical(other.merchant, merchant) ||
                other.merchant == merchant) &&
            (identical(other.distanceInMinutes, distanceInMinutes) ||
                other.distanceInMinutes == distanceInMinutes) &&
            const DeepCollectionEquality().equals(other._offers, _offers));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      productOfferId,
      validFrom,
      validTo,
      merchant,
      distanceInMinutes,
      const DeepCollectionEquality().hash(_offers));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProductOfferDealModellCopyWith<_$_ProductOfferDealModell> get copyWith =>
      __$$_ProductOfferDealModellCopyWithImpl<_$_ProductOfferDealModell>(
          this, _$identity);
}

abstract class _ProductOfferDealModell extends ProductOfferDealModel {
  factory _ProductOfferDealModell(
      {required final String productOfferId,
      final DateTime? validFrom,
      final DateTime? validTo,
      final MerchantBranchInfo? merchant,
      final int? distanceInMinutes,
      final List<Price>? offers}) = _$_ProductOfferDealModell;
  _ProductOfferDealModell._() : super._();

  @override
  String get productOfferId;
  @override
  DateTime? get validFrom;
  @override
  DateTime? get validTo;
  @override
  MerchantBranchInfo? get merchant;
  @override
  int? get distanceInMinutes;
  @override
  List<Price>? get offers;
  @override
  @JsonKey(ignore: true)
  _$$_ProductOfferDealModellCopyWith<_$_ProductOfferDealModell> get copyWith =>
      throw _privateConstructorUsedError;
}
