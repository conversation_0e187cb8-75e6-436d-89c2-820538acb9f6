// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_offer_detail.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ProductOfferDetail {
  String? get productOfferId => throw _privateConstructorUsedError;
  ProductDetail? get product => throw _privateConstructorUsedError;
  MerchantBranchInfo? get merchant => throw _privateConstructorUsedError;
  String? get banner => throw _privateConstructorUsedError;
  int? get stockCountAvailable => throw _privateConstructorUsedError;
  List<Price>? get prices => throw _privateConstructorUsedError;
  bool? get buyButtonTopLayout => throw _privateConstructorUsedError;
  int? get customersLookingCount => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;
  bool? get hasNearby => throw _privateConstructorUsedError;
  bool? get hasOnline => throw _privateConstructorUsedError;
  double? get distanceKm => throw _privateConstructorUsedError;
  int? get distanceMinutes => throw _privateConstructorUsedError;
  DateTime? get validTo => throw _privateConstructorUsedError;
  String? get modelNumber => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ProductOfferDetailCopyWith<ProductOfferDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductOfferDetailCopyWith<$Res> {
  factory $ProductOfferDetailCopyWith(
          ProductOfferDetail value, $Res Function(ProductOfferDetail) then) =
      _$ProductOfferDetailCopyWithImpl<$Res, ProductOfferDetail>;
  @useResult
  $Res call(
      {String? productOfferId,
      ProductDetail? product,
      MerchantBranchInfo? merchant,
      String? banner,
      int? stockCountAvailable,
      List<Price>? prices,
      bool? buyButtonTopLayout,
      int? customersLookingCount,
      String? url,
      bool? hasNearby,
      bool? hasOnline,
      double? distanceKm,
      int? distanceMinutes,
      DateTime? validTo,
      String? modelNumber});

  $ProductDetailCopyWith<$Res>? get product;
  $MerchantBranchInfoCopyWith<$Res>? get merchant;
}

/// @nodoc
class _$ProductOfferDetailCopyWithImpl<$Res, $Val extends ProductOfferDetail>
    implements $ProductOfferDetailCopyWith<$Res> {
  _$ProductOfferDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productOfferId = freezed,
    Object? product = freezed,
    Object? merchant = freezed,
    Object? banner = freezed,
    Object? stockCountAvailable = freezed,
    Object? prices = freezed,
    Object? buyButtonTopLayout = freezed,
    Object? customersLookingCount = freezed,
    Object? url = freezed,
    Object? hasNearby = freezed,
    Object? hasOnline = freezed,
    Object? distanceKm = freezed,
    Object? distanceMinutes = freezed,
    Object? validTo = freezed,
    Object? modelNumber = freezed,
  }) {
    return _then(_value.copyWith(
      productOfferId: freezed == productOfferId
          ? _value.productOfferId
          : productOfferId // ignore: cast_nullable_to_non_nullable
              as String?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductDetail?,
      merchant: freezed == merchant
          ? _value.merchant
          : merchant // ignore: cast_nullable_to_non_nullable
              as MerchantBranchInfo?,
      banner: freezed == banner
          ? _value.banner
          : banner // ignore: cast_nullable_to_non_nullable
              as String?,
      stockCountAvailable: freezed == stockCountAvailable
          ? _value.stockCountAvailable
          : stockCountAvailable // ignore: cast_nullable_to_non_nullable
              as int?,
      prices: freezed == prices
          ? _value.prices
          : prices // ignore: cast_nullable_to_non_nullable
              as List<Price>?,
      buyButtonTopLayout: freezed == buyButtonTopLayout
          ? _value.buyButtonTopLayout
          : buyButtonTopLayout // ignore: cast_nullable_to_non_nullable
              as bool?,
      customersLookingCount: freezed == customersLookingCount
          ? _value.customersLookingCount
          : customersLookingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      hasNearby: freezed == hasNearby
          ? _value.hasNearby
          : hasNearby // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasOnline: freezed == hasOnline
          ? _value.hasOnline
          : hasOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
      distanceKm: freezed == distanceKm
          ? _value.distanceKm
          : distanceKm // ignore: cast_nullable_to_non_nullable
              as double?,
      distanceMinutes: freezed == distanceMinutes
          ? _value.distanceMinutes
          : distanceMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      modelNumber: freezed == modelNumber
          ? _value.modelNumber
          : modelNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProductDetailCopyWith<$Res>? get product {
    if (_value.product == null) {
      return null;
    }

    return $ProductDetailCopyWith<$Res>(_value.product!, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MerchantBranchInfoCopyWith<$Res>? get merchant {
    if (_value.merchant == null) {
      return null;
    }

    return $MerchantBranchInfoCopyWith<$Res>(_value.merchant!, (value) {
      return _then(_value.copyWith(merchant: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ProductOfferDetailCopyWith<$Res>
    implements $ProductOfferDetailCopyWith<$Res> {
  factory _$$_ProductOfferDetailCopyWith(_$_ProductOfferDetail value,
          $Res Function(_$_ProductOfferDetail) then) =
      __$$_ProductOfferDetailCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productOfferId,
      ProductDetail? product,
      MerchantBranchInfo? merchant,
      String? banner,
      int? stockCountAvailable,
      List<Price>? prices,
      bool? buyButtonTopLayout,
      int? customersLookingCount,
      String? url,
      bool? hasNearby,
      bool? hasOnline,
      double? distanceKm,
      int? distanceMinutes,
      DateTime? validTo,
      String? modelNumber});

  @override
  $ProductDetailCopyWith<$Res>? get product;
  @override
  $MerchantBranchInfoCopyWith<$Res>? get merchant;
}

/// @nodoc
class __$$_ProductOfferDetailCopyWithImpl<$Res>
    extends _$ProductOfferDetailCopyWithImpl<$Res, _$_ProductOfferDetail>
    implements _$$_ProductOfferDetailCopyWith<$Res> {
  __$$_ProductOfferDetailCopyWithImpl(
      _$_ProductOfferDetail _value, $Res Function(_$_ProductOfferDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productOfferId = freezed,
    Object? product = freezed,
    Object? merchant = freezed,
    Object? banner = freezed,
    Object? stockCountAvailable = freezed,
    Object? prices = freezed,
    Object? buyButtonTopLayout = freezed,
    Object? customersLookingCount = freezed,
    Object? url = freezed,
    Object? hasNearby = freezed,
    Object? hasOnline = freezed,
    Object? distanceKm = freezed,
    Object? distanceMinutes = freezed,
    Object? validTo = freezed,
    Object? modelNumber = freezed,
  }) {
    return _then(_$_ProductOfferDetail(
      productOfferId: freezed == productOfferId
          ? _value.productOfferId
          : productOfferId // ignore: cast_nullable_to_non_nullable
              as String?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductDetail?,
      merchant: freezed == merchant
          ? _value.merchant
          : merchant // ignore: cast_nullable_to_non_nullable
              as MerchantBranchInfo?,
      banner: freezed == banner
          ? _value.banner
          : banner // ignore: cast_nullable_to_non_nullable
              as String?,
      stockCountAvailable: freezed == stockCountAvailable
          ? _value.stockCountAvailable
          : stockCountAvailable // ignore: cast_nullable_to_non_nullable
              as int?,
      prices: freezed == prices
          ? _value._prices
          : prices // ignore: cast_nullable_to_non_nullable
              as List<Price>?,
      buyButtonTopLayout: freezed == buyButtonTopLayout
          ? _value.buyButtonTopLayout
          : buyButtonTopLayout // ignore: cast_nullable_to_non_nullable
              as bool?,
      customersLookingCount: freezed == customersLookingCount
          ? _value.customersLookingCount
          : customersLookingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      hasNearby: freezed == hasNearby
          ? _value.hasNearby
          : hasNearby // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasOnline: freezed == hasOnline
          ? _value.hasOnline
          : hasOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
      distanceKm: freezed == distanceKm
          ? _value.distanceKm
          : distanceKm // ignore: cast_nullable_to_non_nullable
              as double?,
      distanceMinutes: freezed == distanceMinutes
          ? _value.distanceMinutes
          : distanceMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      modelNumber: freezed == modelNumber
          ? _value.modelNumber
          : modelNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_ProductOfferDetail extends _ProductOfferDetail {
  _$_ProductOfferDetail(
      {this.productOfferId,
      this.product,
      this.merchant,
      this.banner,
      this.stockCountAvailable,
      final List<Price>? prices,
      this.buyButtonTopLayout,
      this.customersLookingCount,
      this.url,
      this.hasNearby,
      this.hasOnline,
      this.distanceKm,
      this.distanceMinutes,
      this.validTo,
      this.modelNumber})
      : _prices = prices,
        super._();

  @override
  final String? productOfferId;
  @override
  final ProductDetail? product;
  @override
  final MerchantBranchInfo? merchant;
  @override
  final String? banner;
  @override
  final int? stockCountAvailable;
  final List<Price>? _prices;
  @override
  List<Price>? get prices {
    final value = _prices;
    if (value == null) return null;
    if (_prices is EqualUnmodifiableListView) return _prices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? buyButtonTopLayout;
  @override
  final int? customersLookingCount;
  @override
  final String? url;
  @override
  final bool? hasNearby;
  @override
  final bool? hasOnline;
  @override
  final double? distanceKm;
  @override
  final int? distanceMinutes;
  @override
  final DateTime? validTo;
  @override
  final String? modelNumber;

  @override
  String toString() {
    return 'ProductOfferDetail(productOfferId: $productOfferId, product: $product, merchant: $merchant, banner: $banner, stockCountAvailable: $stockCountAvailable, prices: $prices, buyButtonTopLayout: $buyButtonTopLayout, customersLookingCount: $customersLookingCount, url: $url, hasNearby: $hasNearby, hasOnline: $hasOnline, distanceKm: $distanceKm, distanceMinutes: $distanceMinutes, validTo: $validTo, modelNumber: $modelNumber)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ProductOfferDetail &&
            (identical(other.productOfferId, productOfferId) ||
                other.productOfferId == productOfferId) &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.merchant, merchant) ||
                other.merchant == merchant) &&
            (identical(other.banner, banner) || other.banner == banner) &&
            (identical(other.stockCountAvailable, stockCountAvailable) ||
                other.stockCountAvailable == stockCountAvailable) &&
            const DeepCollectionEquality().equals(other._prices, _prices) &&
            (identical(other.buyButtonTopLayout, buyButtonTopLayout) ||
                other.buyButtonTopLayout == buyButtonTopLayout) &&
            (identical(other.customersLookingCount, customersLookingCount) ||
                other.customersLookingCount == customersLookingCount) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.hasNearby, hasNearby) ||
                other.hasNearby == hasNearby) &&
            (identical(other.hasOnline, hasOnline) ||
                other.hasOnline == hasOnline) &&
            (identical(other.distanceKm, distanceKm) ||
                other.distanceKm == distanceKm) &&
            (identical(other.distanceMinutes, distanceMinutes) ||
                other.distanceMinutes == distanceMinutes) &&
            (identical(other.validTo, validTo) || other.validTo == validTo) &&
            (identical(other.modelNumber, modelNumber) ||
                other.modelNumber == modelNumber));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      productOfferId,
      product,
      merchant,
      banner,
      stockCountAvailable,
      const DeepCollectionEquality().hash(_prices),
      buyButtonTopLayout,
      customersLookingCount,
      url,
      hasNearby,
      hasOnline,
      distanceKm,
      distanceMinutes,
      validTo,
      modelNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProductOfferDetailCopyWith<_$_ProductOfferDetail> get copyWith =>
      __$$_ProductOfferDetailCopyWithImpl<_$_ProductOfferDetail>(
          this, _$identity);
}

abstract class _ProductOfferDetail extends ProductOfferDetail {
  factory _ProductOfferDetail(
      {final String? productOfferId,
      final ProductDetail? product,
      final MerchantBranchInfo? merchant,
      final String? banner,
      final int? stockCountAvailable,
      final List<Price>? prices,
      final bool? buyButtonTopLayout,
      final int? customersLookingCount,
      final String? url,
      final bool? hasNearby,
      final bool? hasOnline,
      final double? distanceKm,
      final int? distanceMinutes,
      final DateTime? validTo,
      final String? modelNumber}) = _$_ProductOfferDetail;
  _ProductOfferDetail._() : super._();

  @override
  String? get productOfferId;
  @override
  ProductDetail? get product;
  @override
  MerchantBranchInfo? get merchant;
  @override
  String? get banner;
  @override
  int? get stockCountAvailable;
  @override
  List<Price>? get prices;
  @override
  bool? get buyButtonTopLayout;
  @override
  int? get customersLookingCount;
  @override
  String? get url;
  @override
  bool? get hasNearby;
  @override
  bool? get hasOnline;
  @override
  double? get distanceKm;
  @override
  int? get distanceMinutes;
  @override
  DateTime? get validTo;
  @override
  String? get modelNumber;
  @override
  @JsonKey(ignore: true)
  _$$_ProductOfferDetailCopyWith<_$_ProductOfferDetail> get copyWith =>
      throw _privateConstructorUsedError;
}
