// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_offer_detail_list.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ProductOfferDetailList {
  int? get page => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;
  List<ProductOfferDetail> get items => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ProductOfferDetailListCopyWith<ProductOfferDetailList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductOfferDetailListCopyWith<$Res> {
  factory $ProductOfferDetailListCopyWith(ProductOfferDetailList value,
          $Res Function(ProductOfferDetailList) then) =
      _$ProductOfferDetailListCopyWithImpl<$Res, ProductOfferDetailList>;
  @useResult
  $Res call(
      {int? page,
      int? pageSize,
      int? totalCount,
      List<ProductOfferDetail> items});
}

/// @nodoc
class _$ProductOfferDetailListCopyWithImpl<$Res,
        $Val extends ProductOfferDetailList>
    implements $ProductOfferDetailListCopyWith<$Res> {
  _$ProductOfferDetailListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = freezed,
    Object? pageSize = freezed,
    Object? totalCount = freezed,
    Object? items = null,
  }) {
    return _then(_value.copyWith(
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductOfferDetail>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ProductOfferDetailListCopyWith<$Res>
    implements $ProductOfferDetailListCopyWith<$Res> {
  factory _$$_ProductOfferDetailListCopyWith(_$_ProductOfferDetailList value,
          $Res Function(_$_ProductOfferDetailList) then) =
      __$$_ProductOfferDetailListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? page,
      int? pageSize,
      int? totalCount,
      List<ProductOfferDetail> items});
}

/// @nodoc
class __$$_ProductOfferDetailListCopyWithImpl<$Res>
    extends _$ProductOfferDetailListCopyWithImpl<$Res,
        _$_ProductOfferDetailList>
    implements _$$_ProductOfferDetailListCopyWith<$Res> {
  __$$_ProductOfferDetailListCopyWithImpl(_$_ProductOfferDetailList _value,
      $Res Function(_$_ProductOfferDetailList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = freezed,
    Object? pageSize = freezed,
    Object? totalCount = freezed,
    Object? items = null,
  }) {
    return _then(_$_ProductOfferDetailList(
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductOfferDetail>,
    ));
  }
}

/// @nodoc

class _$_ProductOfferDetailList implements _ProductOfferDetailList {
  _$_ProductOfferDetailList(
      {this.page,
      this.pageSize,
      this.totalCount,
      final List<ProductOfferDetail> items = const <ProductOfferDetail>[]})
      : _items = items;

  @override
  final int? page;
  @override
  final int? pageSize;
  @override
  final int? totalCount;
  final List<ProductOfferDetail> _items;
  @override
  @JsonKey()
  List<ProductOfferDetail> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'ProductOfferDetailList(page: $page, pageSize: $pageSize, totalCount: $totalCount, items: $items)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ProductOfferDetailList &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @override
  int get hashCode => Object.hash(runtimeType, page, pageSize, totalCount,
      const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProductOfferDetailListCopyWith<_$_ProductOfferDetailList> get copyWith =>
      __$$_ProductOfferDetailListCopyWithImpl<_$_ProductOfferDetailList>(
          this, _$identity);
}

abstract class _ProductOfferDetailList implements ProductOfferDetailList {
  factory _ProductOfferDetailList(
      {final int? page,
      final int? pageSize,
      final int? totalCount,
      final List<ProductOfferDetail> items}) = _$_ProductOfferDetailList;

  @override
  int? get page;
  @override
  int? get pageSize;
  @override
  int? get totalCount;
  @override
  List<ProductOfferDetail> get items;
  @override
  @JsonKey(ignore: true)
  _$$_ProductOfferDetailListCopyWith<_$_ProductOfferDetailList> get copyWith =>
      throw _privateConstructorUsedError;
}
