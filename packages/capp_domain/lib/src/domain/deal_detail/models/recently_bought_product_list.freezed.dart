// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recently_bought_product_list.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RecentlyBoughtProductList {
  int? get page => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;
  List<RecentlyBoughtProduct>? get items => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RecentlyBoughtProductListCopyWith<RecentlyBoughtProductList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentlyBoughtProductListCopyWith<$Res> {
  factory $RecentlyBoughtProductListCopyWith(RecentlyBoughtProductList value,
          $Res Function(RecentlyBoughtProductList) then) =
      _$RecentlyBoughtProductListCopyWithImpl<$Res, RecentlyBoughtProductList>;
  @useResult
  $Res call(
      {int? page,
      int? pageSize,
      int? totalCount,
      List<RecentlyBoughtProduct>? items});
}

/// @nodoc
class _$RecentlyBoughtProductListCopyWithImpl<$Res,
        $Val extends RecentlyBoughtProductList>
    implements $RecentlyBoughtProductListCopyWith<$Res> {
  _$RecentlyBoughtProductListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = freezed,
    Object? pageSize = freezed,
    Object? totalCount = freezed,
    Object? items = freezed,
  }) {
    return _then(_value.copyWith(
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<RecentlyBoughtProduct>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RecentlyBoughtProductListCopyWith<$Res>
    implements $RecentlyBoughtProductListCopyWith<$Res> {
  factory _$$_RecentlyBoughtProductListCopyWith(
          _$_RecentlyBoughtProductList value,
          $Res Function(_$_RecentlyBoughtProductList) then) =
      __$$_RecentlyBoughtProductListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? page,
      int? pageSize,
      int? totalCount,
      List<RecentlyBoughtProduct>? items});
}

/// @nodoc
class __$$_RecentlyBoughtProductListCopyWithImpl<$Res>
    extends _$RecentlyBoughtProductListCopyWithImpl<$Res,
        _$_RecentlyBoughtProductList>
    implements _$$_RecentlyBoughtProductListCopyWith<$Res> {
  __$$_RecentlyBoughtProductListCopyWithImpl(
      _$_RecentlyBoughtProductList _value,
      $Res Function(_$_RecentlyBoughtProductList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = freezed,
    Object? pageSize = freezed,
    Object? totalCount = freezed,
    Object? items = freezed,
  }) {
    return _then(_$_RecentlyBoughtProductList(
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<RecentlyBoughtProduct>?,
    ));
  }
}

/// @nodoc

class _$_RecentlyBoughtProductList implements _RecentlyBoughtProductList {
  const _$_RecentlyBoughtProductList(
      {this.page,
      this.pageSize,
      this.totalCount,
      final List<RecentlyBoughtProduct>? items})
      : _items = items;

  @override
  final int? page;
  @override
  final int? pageSize;
  @override
  final int? totalCount;
  final List<RecentlyBoughtProduct>? _items;
  @override
  List<RecentlyBoughtProduct>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'RecentlyBoughtProductList(page: $page, pageSize: $pageSize, totalCount: $totalCount, items: $items)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RecentlyBoughtProductList &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @override
  int get hashCode => Object.hash(runtimeType, page, pageSize, totalCount,
      const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RecentlyBoughtProductListCopyWith<_$_RecentlyBoughtProductList>
      get copyWith => __$$_RecentlyBoughtProductListCopyWithImpl<
          _$_RecentlyBoughtProductList>(this, _$identity);
}

abstract class _RecentlyBoughtProductList implements RecentlyBoughtProductList {
  const factory _RecentlyBoughtProductList(
      {final int? page,
      final int? pageSize,
      final int? totalCount,
      final List<RecentlyBoughtProduct>? items}) = _$_RecentlyBoughtProductList;

  @override
  int? get page;
  @override
  int? get pageSize;
  @override
  int? get totalCount;
  @override
  List<RecentlyBoughtProduct>? get items;
  @override
  @JsonKey(ignore: true)
  _$$_RecentlyBoughtProductListCopyWith<_$_RecentlyBoughtProductList>
      get copyWith => throw _privateConstructorUsedError;
}
