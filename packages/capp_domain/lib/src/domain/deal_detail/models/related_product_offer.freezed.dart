// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'related_product_offer.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RelatedProductOffer {
  String? get productId => throw _privateConstructorUsedError;
  String? get productTitle => throw _privateConstructorUsedError;
  List<Media>? get media => throw _privateConstructorUsedError;
  List<ProductOfferDealModel>? get offers => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RelatedProductOfferCopyWith<RelatedProductOffer> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RelatedProductOfferCopyWith<$Res> {
  factory $RelatedProductOfferCopyWith(
          RelatedProductOffer value, $Res Function(RelatedProductOffer) then) =
      _$RelatedProductOfferCopyWithImpl<$Res, RelatedProductOffer>;
  @useResult
  $Res call(
      {String? productId,
      String? productTitle,
      List<Media>? media,
      List<ProductOfferDealModel>? offers});
}

/// @nodoc
class _$RelatedProductOfferCopyWithImpl<$Res, $Val extends RelatedProductOffer>
    implements $RelatedProductOfferCopyWith<$Res> {
  _$RelatedProductOfferCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? productTitle = freezed,
    Object? media = freezed,
    Object? offers = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      productTitle: freezed == productTitle
          ? _value.productTitle
          : productTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      media: freezed == media
          ? _value.media
          : media // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      offers: freezed == offers
          ? _value.offers
          : offers // ignore: cast_nullable_to_non_nullable
              as List<ProductOfferDealModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RelatedProductOfferCopyWith<$Res>
    implements $RelatedProductOfferCopyWith<$Res> {
  factory _$$_RelatedProductOfferCopyWith(_$_RelatedProductOffer value,
          $Res Function(_$_RelatedProductOffer) then) =
      __$$_RelatedProductOfferCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      String? productTitle,
      List<Media>? media,
      List<ProductOfferDealModel>? offers});
}

/// @nodoc
class __$$_RelatedProductOfferCopyWithImpl<$Res>
    extends _$RelatedProductOfferCopyWithImpl<$Res, _$_RelatedProductOffer>
    implements _$$_RelatedProductOfferCopyWith<$Res> {
  __$$_RelatedProductOfferCopyWithImpl(_$_RelatedProductOffer _value,
      $Res Function(_$_RelatedProductOffer) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? productTitle = freezed,
    Object? media = freezed,
    Object? offers = freezed,
  }) {
    return _then(_$_RelatedProductOffer(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      productTitle: freezed == productTitle
          ? _value.productTitle
          : productTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      media: freezed == media
          ? _value._media
          : media // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      offers: freezed == offers
          ? _value._offers
          : offers // ignore: cast_nullable_to_non_nullable
              as List<ProductOfferDealModel>?,
    ));
  }
}

/// @nodoc

class _$_RelatedProductOffer extends _RelatedProductOffer {
  _$_RelatedProductOffer(
      {this.productId,
      this.productTitle,
      final List<Media>? media,
      final List<ProductOfferDealModel>? offers})
      : _media = media,
        _offers = offers,
        super._();

  @override
  final String? productId;
  @override
  final String? productTitle;
  final List<Media>? _media;
  @override
  List<Media>? get media {
    final value = _media;
    if (value == null) return null;
    if (_media is EqualUnmodifiableListView) return _media;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductOfferDealModel>? _offers;
  @override
  List<ProductOfferDealModel>? get offers {
    final value = _offers;
    if (value == null) return null;
    if (_offers is EqualUnmodifiableListView) return _offers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'RelatedProductOffer(productId: $productId, productTitle: $productTitle, media: $media, offers: $offers)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RelatedProductOffer &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.productTitle, productTitle) ||
                other.productTitle == productTitle) &&
            const DeepCollectionEquality().equals(other._media, _media) &&
            const DeepCollectionEquality().equals(other._offers, _offers));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      productId,
      productTitle,
      const DeepCollectionEquality().hash(_media),
      const DeepCollectionEquality().hash(_offers));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RelatedProductOfferCopyWith<_$_RelatedProductOffer> get copyWith =>
      __$$_RelatedProductOfferCopyWithImpl<_$_RelatedProductOffer>(
          this, _$identity);
}

abstract class _RelatedProductOffer extends RelatedProductOffer {
  factory _RelatedProductOffer(
      {final String? productId,
      final String? productTitle,
      final List<Media>? media,
      final List<ProductOfferDealModel>? offers}) = _$_RelatedProductOffer;
  _RelatedProductOffer._() : super._();

  @override
  String? get productId;
  @override
  String? get productTitle;
  @override
  List<Media>? get media;
  @override
  List<ProductOfferDealModel>? get offers;
  @override
  @JsonKey(ignore: true)
  _$$_RelatedProductOfferCopyWith<_$_RelatedProductOffer> get copyWith =>
      throw _privateConstructorUsedError;
}
