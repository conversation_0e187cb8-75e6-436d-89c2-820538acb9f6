// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deal_list.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DealList {
  int get pageNumber => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  List<DealPreview> get pageItems => throw _privateConstructorUsedError;
  FilterOptions? get filterOptions => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DealListCopyWith<DealList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DealListCopyWith<$Res> {
  factory $DealListCopyWith(DealList value, $Res Function(DealList) then) =
      _$DealListCopyWithImpl<$Res, DealList>;
  @useResult
  $Res call(
      {int pageNumber,
      int pageSize,
      List<DealPreview> pageItems,
      FilterOptions? filterOptions});

  $FilterOptionsCopyWith<$Res>? get filterOptions;
}

/// @nodoc
class _$DealListCopyWithImpl<$Res, $Val extends DealList>
    implements $DealListCopyWith<$Res> {
  _$DealListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageNumber = null,
    Object? pageSize = null,
    Object? pageItems = null,
    Object? filterOptions = freezed,
  }) {
    return _then(_value.copyWith(
      pageNumber: null == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      pageItems: null == pageItems
          ? _value.pageItems
          : pageItems // ignore: cast_nullable_to_non_nullable
              as List<DealPreview>,
      filterOptions: freezed == filterOptions
          ? _value.filterOptions
          : filterOptions // ignore: cast_nullable_to_non_nullable
              as FilterOptions?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $FilterOptionsCopyWith<$Res>? get filterOptions {
    if (_value.filterOptions == null) {
      return null;
    }

    return $FilterOptionsCopyWith<$Res>(_value.filterOptions!, (value) {
      return _then(_value.copyWith(filterOptions: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_DealListCopyWith<$Res> implements $DealListCopyWith<$Res> {
  factory _$$_DealListCopyWith(
          _$_DealList value, $Res Function(_$_DealList) then) =
      __$$_DealListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int pageNumber,
      int pageSize,
      List<DealPreview> pageItems,
      FilterOptions? filterOptions});

  @override
  $FilterOptionsCopyWith<$Res>? get filterOptions;
}

/// @nodoc
class __$$_DealListCopyWithImpl<$Res>
    extends _$DealListCopyWithImpl<$Res, _$_DealList>
    implements _$$_DealListCopyWith<$Res> {
  __$$_DealListCopyWithImpl(
      _$_DealList _value, $Res Function(_$_DealList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageNumber = null,
    Object? pageSize = null,
    Object? pageItems = null,
    Object? filterOptions = freezed,
  }) {
    return _then(_$_DealList(
      pageNumber: null == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      pageItems: null == pageItems
          ? _value._pageItems
          : pageItems // ignore: cast_nullable_to_non_nullable
              as List<DealPreview>,
      filterOptions: freezed == filterOptions
          ? _value.filterOptions
          : filterOptions // ignore: cast_nullable_to_non_nullable
              as FilterOptions?,
    ));
  }
}

/// @nodoc

class _$_DealList implements _DealList {
  const _$_DealList(
      {required this.pageNumber,
      required this.pageSize,
      required final List<DealPreview> pageItems,
      this.filterOptions})
      : _pageItems = pageItems;

  @override
  final int pageNumber;
  @override
  final int pageSize;
  final List<DealPreview> _pageItems;
  @override
  List<DealPreview> get pageItems {
    if (_pageItems is EqualUnmodifiableListView) return _pageItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pageItems);
  }

  @override
  final FilterOptions? filterOptions;

  @override
  String toString() {
    return 'DealList(pageNumber: $pageNumber, pageSize: $pageSize, pageItems: $pageItems, filterOptions: $filterOptions)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DealList &&
            (identical(other.pageNumber, pageNumber) ||
                other.pageNumber == pageNumber) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            const DeepCollectionEquality()
                .equals(other._pageItems, _pageItems) &&
            (identical(other.filterOptions, filterOptions) ||
                other.filterOptions == filterOptions));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pageNumber, pageSize,
      const DeepCollectionEquality().hash(_pageItems), filterOptions);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DealListCopyWith<_$_DealList> get copyWith =>
      __$$_DealListCopyWithImpl<_$_DealList>(this, _$identity);
}

abstract class _DealList implements DealList {
  const factory _DealList(
      {required final int pageNumber,
      required final int pageSize,
      required final List<DealPreview> pageItems,
      final FilterOptions? filterOptions}) = _$_DealList;

  @override
  int get pageNumber;
  @override
  int get pageSize;
  @override
  List<DealPreview> get pageItems;
  @override
  FilterOptions? get filterOptions;
  @override
  @JsonKey(ignore: true)
  _$$_DealListCopyWith<_$_DealList> get copyWith =>
      throw _privateConstructorUsedError;
}
