// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deal_preview_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DealPreview {
  String? get dealId => throw _privateConstructorUsedError;
  DealType? get dealType => throw _privateConstructorUsedError;
  List<Media>? get hero => throw _privateConstructorUsedError;
  Decimal? get price => throw _privateConstructorUsedError;
  Decimal? get loanInstallment => throw _privateConstructorUsedError;
  int? get discount => throw _privateConstructorUsedError;
  int? get ratingsCount => throw _privateConstructorUsedError;
  double? get rating => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  DateTime? get validFrom => throw _privateConstructorUsedError;
  DateTime? get validTo => throw _privateConstructorUsedError;
  bool get isHearted => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DealPreviewCopyWith<DealPreview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DealPreviewCopyWith<$Res> {
  factory $DealPreviewCopyWith(
          DealPreview value, $Res Function(DealPreview) then) =
      _$DealPreviewCopyWithImpl<$Res, DealPreview>;
  @useResult
  $Res call(
      {String? dealId,
      DealType? dealType,
      List<Media>? hero,
      Decimal? price,
      Decimal? loanInstallment,
      int? discount,
      int? ratingsCount,
      double? rating,
      String? title,
      DateTime? validFrom,
      DateTime? validTo,
      bool isHearted});
}

/// @nodoc
class _$DealPreviewCopyWithImpl<$Res, $Val extends DealPreview>
    implements $DealPreviewCopyWith<$Res> {
  _$DealPreviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dealId = freezed,
    Object? dealType = freezed,
    Object? hero = freezed,
    Object? price = freezed,
    Object? loanInstallment = freezed,
    Object? discount = freezed,
    Object? ratingsCount = freezed,
    Object? rating = freezed,
    Object? title = freezed,
    Object? validFrom = freezed,
    Object? validTo = freezed,
    Object? isHearted = null,
  }) {
    return _then(_value.copyWith(
      dealId: freezed == dealId
          ? _value.dealId
          : dealId // ignore: cast_nullable_to_non_nullable
              as String?,
      dealType: freezed == dealType
          ? _value.dealType
          : dealType // ignore: cast_nullable_to_non_nullable
              as DealType?,
      hero: freezed == hero
          ? _value.hero
          : hero // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      loanInstallment: freezed == loanInstallment
          ? _value.loanInstallment
          : loanInstallment // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as int?,
      ratingsCount: freezed == ratingsCount
          ? _value.ratingsCount
          : ratingsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      validFrom: freezed == validFrom
          ? _value.validFrom
          : validFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isHearted: null == isHearted
          ? _value.isHearted
          : isHearted // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DealPreviewCopyWith<$Res>
    implements $DealPreviewCopyWith<$Res> {
  factory _$$_DealPreviewCopyWith(
          _$_DealPreview value, $Res Function(_$_DealPreview) then) =
      __$$_DealPreviewCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? dealId,
      DealType? dealType,
      List<Media>? hero,
      Decimal? price,
      Decimal? loanInstallment,
      int? discount,
      int? ratingsCount,
      double? rating,
      String? title,
      DateTime? validFrom,
      DateTime? validTo,
      bool isHearted});
}

/// @nodoc
class __$$_DealPreviewCopyWithImpl<$Res>
    extends _$DealPreviewCopyWithImpl<$Res, _$_DealPreview>
    implements _$$_DealPreviewCopyWith<$Res> {
  __$$_DealPreviewCopyWithImpl(
      _$_DealPreview _value, $Res Function(_$_DealPreview) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dealId = freezed,
    Object? dealType = freezed,
    Object? hero = freezed,
    Object? price = freezed,
    Object? loanInstallment = freezed,
    Object? discount = freezed,
    Object? ratingsCount = freezed,
    Object? rating = freezed,
    Object? title = freezed,
    Object? validFrom = freezed,
    Object? validTo = freezed,
    Object? isHearted = null,
  }) {
    return _then(_$_DealPreview(
      dealId: freezed == dealId
          ? _value.dealId
          : dealId // ignore: cast_nullable_to_non_nullable
              as String?,
      dealType: freezed == dealType
          ? _value.dealType
          : dealType // ignore: cast_nullable_to_non_nullable
              as DealType?,
      hero: freezed == hero
          ? _value._hero
          : hero // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      loanInstallment: freezed == loanInstallment
          ? _value.loanInstallment
          : loanInstallment // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as int?,
      ratingsCount: freezed == ratingsCount
          ? _value.ratingsCount
          : ratingsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      validFrom: freezed == validFrom
          ? _value.validFrom
          : validFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isHearted: null == isHearted
          ? _value.isHearted
          : isHearted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_DealPreview implements _DealPreview {
  _$_DealPreview(
      {this.dealId,
      this.dealType,
      final List<Media>? hero,
      this.price,
      this.loanInstallment,
      this.discount,
      this.ratingsCount,
      this.rating,
      this.title,
      this.validFrom,
      this.validTo,
      this.isHearted = false})
      : _hero = hero;

  @override
  final String? dealId;
  @override
  final DealType? dealType;
  final List<Media>? _hero;
  @override
  List<Media>? get hero {
    final value = _hero;
    if (value == null) return null;
    if (_hero is EqualUnmodifiableListView) return _hero;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final Decimal? price;
  @override
  final Decimal? loanInstallment;
  @override
  final int? discount;
  @override
  final int? ratingsCount;
  @override
  final double? rating;
  @override
  final String? title;
  @override
  final DateTime? validFrom;
  @override
  final DateTime? validTo;
  @override
  @JsonKey()
  final bool isHearted;

  @override
  String toString() {
    return 'DealPreview(dealId: $dealId, dealType: $dealType, hero: $hero, price: $price, loanInstallment: $loanInstallment, discount: $discount, ratingsCount: $ratingsCount, rating: $rating, title: $title, validFrom: $validFrom, validTo: $validTo, isHearted: $isHearted)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DealPreview &&
            (identical(other.dealId, dealId) || other.dealId == dealId) &&
            (identical(other.dealType, dealType) ||
                other.dealType == dealType) &&
            const DeepCollectionEquality().equals(other._hero, _hero) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.loanInstallment, loanInstallment) ||
                other.loanInstallment == loanInstallment) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.ratingsCount, ratingsCount) ||
                other.ratingsCount == ratingsCount) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.validFrom, validFrom) ||
                other.validFrom == validFrom) &&
            (identical(other.validTo, validTo) || other.validTo == validTo) &&
            (identical(other.isHearted, isHearted) ||
                other.isHearted == isHearted));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      dealId,
      dealType,
      const DeepCollectionEquality().hash(_hero),
      price,
      loanInstallment,
      discount,
      ratingsCount,
      rating,
      title,
      validFrom,
      validTo,
      isHearted);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DealPreviewCopyWith<_$_DealPreview> get copyWith =>
      __$$_DealPreviewCopyWithImpl<_$_DealPreview>(this, _$identity);
}

abstract class _DealPreview implements DealPreview {
  factory _DealPreview(
      {final String? dealId,
      final DealType? dealType,
      final List<Media>? hero,
      final Decimal? price,
      final Decimal? loanInstallment,
      final int? discount,
      final int? ratingsCount,
      final double? rating,
      final String? title,
      final DateTime? validFrom,
      final DateTime? validTo,
      final bool isHearted}) = _$_DealPreview;

  @override
  String? get dealId;
  @override
  DealType? get dealType;
  @override
  List<Media>? get hero;
  @override
  Decimal? get price;
  @override
  Decimal? get loanInstallment;
  @override
  int? get discount;
  @override
  int? get ratingsCount;
  @override
  double? get rating;
  @override
  String? get title;
  @override
  DateTime? get validFrom;
  @override
  DateTime? get validTo;
  @override
  bool get isHearted;
  @override
  @JsonKey(ignore: true)
  _$$_DealPreviewCopyWith<_$_DealPreview> get copyWith =>
      throw _privateConstructorUsedError;
}
