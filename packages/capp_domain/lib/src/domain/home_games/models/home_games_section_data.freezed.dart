// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_games_section_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$HomeGamesSectionData {
  bool get showCounts => throw _privateConstructorUsedError;
  List<HomeGamesItemData> get games => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $HomeGamesSectionDataCopyWith<HomeGamesSectionData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeGamesSectionDataCopyWith<$Res> {
  factory $HomeGamesSectionDataCopyWith(HomeGamesSectionData value,
          $Res Function(HomeGamesSectionData) then) =
      _$HomeGamesSectionDataCopyWithImpl<$Res, HomeGamesSectionData>;
  @useResult
  $Res call({bool showCounts, List<HomeGamesItemData> games});
}

/// @nodoc
class _$HomeGamesSectionDataCopyWithImpl<$Res,
        $Val extends HomeGamesSectionData>
    implements $HomeGamesSectionDataCopyWith<$Res> {
  _$HomeGamesSectionDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showCounts = null,
    Object? games = null,
  }) {
    return _then(_value.copyWith(
      showCounts: null == showCounts
          ? _value.showCounts
          : showCounts // ignore: cast_nullable_to_non_nullable
              as bool,
      games: null == games
          ? _value.games
          : games // ignore: cast_nullable_to_non_nullable
              as List<HomeGamesItemData>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_HomeGamesSectionDataCopyWith<$Res>
    implements $HomeGamesSectionDataCopyWith<$Res> {
  factory _$$_HomeGamesSectionDataCopyWith(_$_HomeGamesSectionData value,
          $Res Function(_$_HomeGamesSectionData) then) =
      __$$_HomeGamesSectionDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool showCounts, List<HomeGamesItemData> games});
}

/// @nodoc
class __$$_HomeGamesSectionDataCopyWithImpl<$Res>
    extends _$HomeGamesSectionDataCopyWithImpl<$Res, _$_HomeGamesSectionData>
    implements _$$_HomeGamesSectionDataCopyWith<$Res> {
  __$$_HomeGamesSectionDataCopyWithImpl(_$_HomeGamesSectionData _value,
      $Res Function(_$_HomeGamesSectionData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showCounts = null,
    Object? games = null,
  }) {
    return _then(_$_HomeGamesSectionData(
      showCounts: null == showCounts
          ? _value.showCounts
          : showCounts // ignore: cast_nullable_to_non_nullable
              as bool,
      games: null == games
          ? _value._games
          : games // ignore: cast_nullable_to_non_nullable
              as List<HomeGamesItemData>,
    ));
  }
}

/// @nodoc

class _$_HomeGamesSectionData implements _HomeGamesSectionData {
  const _$_HomeGamesSectionData(
      {required this.showCounts, required final List<HomeGamesItemData> games})
      : _games = games;

  @override
  final bool showCounts;
  final List<HomeGamesItemData> _games;
  @override
  List<HomeGamesItemData> get games {
    if (_games is EqualUnmodifiableListView) return _games;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_games);
  }

  @override
  String toString() {
    return 'HomeGamesSectionData(showCounts: $showCounts, games: $games)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_HomeGamesSectionData &&
            (identical(other.showCounts, showCounts) ||
                other.showCounts == showCounts) &&
            const DeepCollectionEquality().equals(other._games, _games));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, showCounts, const DeepCollectionEquality().hash(_games));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_HomeGamesSectionDataCopyWith<_$_HomeGamesSectionData> get copyWith =>
      __$$_HomeGamesSectionDataCopyWithImpl<_$_HomeGamesSectionData>(
          this, _$identity);
}

abstract class _HomeGamesSectionData implements HomeGamesSectionData {
  const factory _HomeGamesSectionData(
      {required final bool showCounts,
      required final List<HomeGamesItemData> games}) = _$_HomeGamesSectionData;

  @override
  bool get showCounts;
  @override
  List<HomeGamesItemData> get games;
  @override
  @JsonKey(ignore: true)
  _$$_HomeGamesSectionDataCopyWith<_$_HomeGamesSectionData> get copyWith =>
      throw _privateConstructorUsedError;
}
