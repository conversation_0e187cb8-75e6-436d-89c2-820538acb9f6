// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'installment_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$InstallmentOverview {
  double? get amount => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  DateTime? get dueDate => throw _privateConstructorUsedError;
  bool get productError => throw _privateConstructorUsedError;
  ProductOfferDetail? get productOffer => throw _privateConstructorUsedError;
  num? get installmentId => throw _privateConstructorUsedError;
  String? get productOfferId => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InstallmentOverviewCopyWith<InstallmentOverview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstallmentOverviewCopyWith<$Res> {
  factory $InstallmentOverviewCopyWith(
          InstallmentOverview value, $Res Function(InstallmentOverview) then) =
      _$InstallmentOverviewCopyWithImpl<$Res, InstallmentOverview>;
  @useResult
  $Res call(
      {double? amount,
      String? currency,
      DateTime? dueDate,
      bool productError,
      ProductOfferDetail? productOffer,
      num? installmentId,
      String? productOfferId});

  $ProductOfferDetailCopyWith<$Res>? get productOffer;
}

/// @nodoc
class _$InstallmentOverviewCopyWithImpl<$Res, $Val extends InstallmentOverview>
    implements $InstallmentOverviewCopyWith<$Res> {
  _$InstallmentOverviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? currency = freezed,
    Object? dueDate = freezed,
    Object? productError = null,
    Object? productOffer = freezed,
    Object? installmentId = freezed,
    Object? productOfferId = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      productError: null == productError
          ? _value.productError
          : productError // ignore: cast_nullable_to_non_nullable
              as bool,
      productOffer: freezed == productOffer
          ? _value.productOffer
          : productOffer // ignore: cast_nullable_to_non_nullable
              as ProductOfferDetail?,
      installmentId: freezed == installmentId
          ? _value.installmentId
          : installmentId // ignore: cast_nullable_to_non_nullable
              as num?,
      productOfferId: freezed == productOfferId
          ? _value.productOfferId
          : productOfferId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProductOfferDetailCopyWith<$Res>? get productOffer {
    if (_value.productOffer == null) {
      return null;
    }

    return $ProductOfferDetailCopyWith<$Res>(_value.productOffer!, (value) {
      return _then(_value.copyWith(productOffer: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_InstallmentOverviewCopyWith<$Res>
    implements $InstallmentOverviewCopyWith<$Res> {
  factory _$$_InstallmentOverviewCopyWith(_$_InstallmentOverview value,
          $Res Function(_$_InstallmentOverview) then) =
      __$$_InstallmentOverviewCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? amount,
      String? currency,
      DateTime? dueDate,
      bool productError,
      ProductOfferDetail? productOffer,
      num? installmentId,
      String? productOfferId});

  @override
  $ProductOfferDetailCopyWith<$Res>? get productOffer;
}

/// @nodoc
class __$$_InstallmentOverviewCopyWithImpl<$Res>
    extends _$InstallmentOverviewCopyWithImpl<$Res, _$_InstallmentOverview>
    implements _$$_InstallmentOverviewCopyWith<$Res> {
  __$$_InstallmentOverviewCopyWithImpl(_$_InstallmentOverview _value,
      $Res Function(_$_InstallmentOverview) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? currency = freezed,
    Object? dueDate = freezed,
    Object? productError = null,
    Object? productOffer = freezed,
    Object? installmentId = freezed,
    Object? productOfferId = freezed,
  }) {
    return _then(_$_InstallmentOverview(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      productError: null == productError
          ? _value.productError
          : productError // ignore: cast_nullable_to_non_nullable
              as bool,
      productOffer: freezed == productOffer
          ? _value.productOffer
          : productOffer // ignore: cast_nullable_to_non_nullable
              as ProductOfferDetail?,
      installmentId: freezed == installmentId
          ? _value.installmentId
          : installmentId // ignore: cast_nullable_to_non_nullable
              as num?,
      productOfferId: freezed == productOfferId
          ? _value.productOfferId
          : productOfferId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_InstallmentOverview implements _InstallmentOverview {
  _$_InstallmentOverview(
      {this.amount,
      this.currency,
      this.dueDate,
      this.productError = false,
      this.productOffer,
      this.installmentId,
      this.productOfferId});

  @override
  final double? amount;
  @override
  final String? currency;
  @override
  final DateTime? dueDate;
  @override
  @JsonKey()
  final bool productError;
  @override
  final ProductOfferDetail? productOffer;
  @override
  final num? installmentId;
  @override
  final String? productOfferId;

  @override
  String toString() {
    return 'InstallmentOverview(amount: $amount, currency: $currency, dueDate: $dueDate, productError: $productError, productOffer: $productOffer, installmentId: $installmentId, productOfferId: $productOfferId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InstallmentOverview &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.productError, productError) ||
                other.productError == productError) &&
            (identical(other.productOffer, productOffer) ||
                other.productOffer == productOffer) &&
            (identical(other.installmentId, installmentId) ||
                other.installmentId == installmentId) &&
            (identical(other.productOfferId, productOfferId) ||
                other.productOfferId == productOfferId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount, currency, dueDate,
      productError, productOffer, installmentId, productOfferId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InstallmentOverviewCopyWith<_$_InstallmentOverview> get copyWith =>
      __$$_InstallmentOverviewCopyWithImpl<_$_InstallmentOverview>(
          this, _$identity);
}

abstract class _InstallmentOverview implements InstallmentOverview {
  factory _InstallmentOverview(
      {final double? amount,
      final String? currency,
      final DateTime? dueDate,
      final bool productError,
      final ProductOfferDetail? productOffer,
      final num? installmentId,
      final String? productOfferId}) = _$_InstallmentOverview;

  @override
  double? get amount;
  @override
  String? get currency;
  @override
  DateTime? get dueDate;
  @override
  bool get productError;
  @override
  ProductOfferDetail? get productOffer;
  @override
  num? get installmentId;
  @override
  String? get productOfferId;
  @override
  @JsonKey(ignore: true)
  _$$_InstallmentOverviewCopyWith<_$_InstallmentOverview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$InstallmentOverviewList {
  Decimal get available => throw _privateConstructorUsedError;
  Decimal get actualSpend => throw _privateConstructorUsedError;
  Decimal? get availableMonthlyInstallmentLimit =>
      throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InstallmentOverviewListCopyWith<InstallmentOverviewList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstallmentOverviewListCopyWith<$Res> {
  factory $InstallmentOverviewListCopyWith(InstallmentOverviewList value,
          $Res Function(InstallmentOverviewList) then) =
      _$InstallmentOverviewListCopyWithImpl<$Res, InstallmentOverviewList>;
  @useResult
  $Res call(
      {Decimal available,
      Decimal actualSpend,
      Decimal? availableMonthlyInstallmentLimit,
      String currency});
}

/// @nodoc
class _$InstallmentOverviewListCopyWithImpl<$Res,
        $Val extends InstallmentOverviewList>
    implements $InstallmentOverviewListCopyWith<$Res> {
  _$InstallmentOverviewListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? available = null,
    Object? actualSpend = null,
    Object? availableMonthlyInstallmentLimit = freezed,
    Object? currency = null,
  }) {
    return _then(_value.copyWith(
      available: null == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as Decimal,
      actualSpend: null == actualSpend
          ? _value.actualSpend
          : actualSpend // ignore: cast_nullable_to_non_nullable
              as Decimal,
      availableMonthlyInstallmentLimit: freezed ==
              availableMonthlyInstallmentLimit
          ? _value.availableMonthlyInstallmentLimit
          : availableMonthlyInstallmentLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InstallmentOverviewListCopyWith<$Res>
    implements $InstallmentOverviewListCopyWith<$Res> {
  factory _$$_InstallmentOverviewListCopyWith(_$_InstallmentOverviewList value,
          $Res Function(_$_InstallmentOverviewList) then) =
      __$$_InstallmentOverviewListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Decimal available,
      Decimal actualSpend,
      Decimal? availableMonthlyInstallmentLimit,
      String currency});
}

/// @nodoc
class __$$_InstallmentOverviewListCopyWithImpl<$Res>
    extends _$InstallmentOverviewListCopyWithImpl<$Res,
        _$_InstallmentOverviewList>
    implements _$$_InstallmentOverviewListCopyWith<$Res> {
  __$$_InstallmentOverviewListCopyWithImpl(_$_InstallmentOverviewList _value,
      $Res Function(_$_InstallmentOverviewList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? available = null,
    Object? actualSpend = null,
    Object? availableMonthlyInstallmentLimit = freezed,
    Object? currency = null,
  }) {
    return _then(_$_InstallmentOverviewList(
      available: null == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as Decimal,
      actualSpend: null == actualSpend
          ? _value.actualSpend
          : actualSpend // ignore: cast_nullable_to_non_nullable
              as Decimal,
      availableMonthlyInstallmentLimit: freezed ==
              availableMonthlyInstallmentLimit
          ? _value.availableMonthlyInstallmentLimit
          : availableMonthlyInstallmentLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_InstallmentOverviewList implements _InstallmentOverviewList {
  const _$_InstallmentOverviewList(
      {required this.available,
      required this.actualSpend,
      this.availableMonthlyInstallmentLimit,
      required this.currency});

  @override
  final Decimal available;
  @override
  final Decimal actualSpend;
  @override
  final Decimal? availableMonthlyInstallmentLimit;
  @override
  final String currency;

  @override
  String toString() {
    return 'InstallmentOverviewList(available: $available, actualSpend: $actualSpend, availableMonthlyInstallmentLimit: $availableMonthlyInstallmentLimit, currency: $currency)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InstallmentOverviewList &&
            (identical(other.available, available) ||
                other.available == available) &&
            (identical(other.actualSpend, actualSpend) ||
                other.actualSpend == actualSpend) &&
            (identical(other.availableMonthlyInstallmentLimit,
                    availableMonthlyInstallmentLimit) ||
                other.availableMonthlyInstallmentLimit ==
                    availableMonthlyInstallmentLimit) &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @override
  int get hashCode => Object.hash(runtimeType, available, actualSpend,
      availableMonthlyInstallmentLimit, currency);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InstallmentOverviewListCopyWith<_$_InstallmentOverviewList>
      get copyWith =>
          __$$_InstallmentOverviewListCopyWithImpl<_$_InstallmentOverviewList>(
              this, _$identity);
}

abstract class _InstallmentOverviewList implements InstallmentOverviewList {
  const factory _InstallmentOverviewList(
      {required final Decimal available,
      required final Decimal actualSpend,
      final Decimal? availableMonthlyInstallmentLimit,
      required final String currency}) = _$_InstallmentOverviewList;

  @override
  Decimal get available;
  @override
  Decimal get actualSpend;
  @override
  Decimal? get availableMonthlyInstallmentLimit;
  @override
  String get currency;
  @override
  @JsonKey(ignore: true)
  _$$_InstallmentOverviewListCopyWith<_$_InstallmentOverviewList>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$InstallmentDetail {
  double get amount => throw _privateConstructorUsedError;
  double get paidAmount => throw _privateConstructorUsedError;
  double get principalBalance => throw _privateConstructorUsedError;
  double get principal => throw _privateConstructorUsedError;
  double get interestAmount => throw _privateConstructorUsedError;
  int get interestPercent => throw _privateConstructorUsedError;
  int get numberInstallment => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  DateTime get dueDate => throw _privateConstructorUsedError;
  int get months => throw _privateConstructorUsedError;
  InstallmentState? get state => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InstallmentDetailCopyWith<InstallmentDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstallmentDetailCopyWith<$Res> {
  factory $InstallmentDetailCopyWith(
          InstallmentDetail value, $Res Function(InstallmentDetail) then) =
      _$InstallmentDetailCopyWithImpl<$Res, InstallmentDetail>;
  @useResult
  $Res call(
      {double amount,
      double paidAmount,
      double principalBalance,
      double principal,
      double interestAmount,
      int interestPercent,
      int numberInstallment,
      String currency,
      DateTime dueDate,
      int months,
      InstallmentState? state});
}

/// @nodoc
class _$InstallmentDetailCopyWithImpl<$Res, $Val extends InstallmentDetail>
    implements $InstallmentDetailCopyWith<$Res> {
  _$InstallmentDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? paidAmount = null,
    Object? principalBalance = null,
    Object? principal = null,
    Object? interestAmount = null,
    Object? interestPercent = null,
    Object? numberInstallment = null,
    Object? currency = null,
    Object? dueDate = null,
    Object? months = null,
    Object? state = freezed,
  }) {
    return _then(_value.copyWith(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      paidAmount: null == paidAmount
          ? _value.paidAmount
          : paidAmount // ignore: cast_nullable_to_non_nullable
              as double,
      principalBalance: null == principalBalance
          ? _value.principalBalance
          : principalBalance // ignore: cast_nullable_to_non_nullable
              as double,
      principal: null == principal
          ? _value.principal
          : principal // ignore: cast_nullable_to_non_nullable
              as double,
      interestAmount: null == interestAmount
          ? _value.interestAmount
          : interestAmount // ignore: cast_nullable_to_non_nullable
              as double,
      interestPercent: null == interestPercent
          ? _value.interestPercent
          : interestPercent // ignore: cast_nullable_to_non_nullable
              as int,
      numberInstallment: null == numberInstallment
          ? _value.numberInstallment
          : numberInstallment // ignore: cast_nullable_to_non_nullable
              as int,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      dueDate: null == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      months: null == months
          ? _value.months
          : months // ignore: cast_nullable_to_non_nullable
              as int,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as InstallmentState?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InstallmentDetailCopyWith<$Res>
    implements $InstallmentDetailCopyWith<$Res> {
  factory _$$_InstallmentDetailCopyWith(_$_InstallmentDetail value,
          $Res Function(_$_InstallmentDetail) then) =
      __$$_InstallmentDetailCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double amount,
      double paidAmount,
      double principalBalance,
      double principal,
      double interestAmount,
      int interestPercent,
      int numberInstallment,
      String currency,
      DateTime dueDate,
      int months,
      InstallmentState? state});
}

/// @nodoc
class __$$_InstallmentDetailCopyWithImpl<$Res>
    extends _$InstallmentDetailCopyWithImpl<$Res, _$_InstallmentDetail>
    implements _$$_InstallmentDetailCopyWith<$Res> {
  __$$_InstallmentDetailCopyWithImpl(
      _$_InstallmentDetail _value, $Res Function(_$_InstallmentDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? paidAmount = null,
    Object? principalBalance = null,
    Object? principal = null,
    Object? interestAmount = null,
    Object? interestPercent = null,
    Object? numberInstallment = null,
    Object? currency = null,
    Object? dueDate = null,
    Object? months = null,
    Object? state = freezed,
  }) {
    return _then(_$_InstallmentDetail(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      paidAmount: null == paidAmount
          ? _value.paidAmount
          : paidAmount // ignore: cast_nullable_to_non_nullable
              as double,
      principalBalance: null == principalBalance
          ? _value.principalBalance
          : principalBalance // ignore: cast_nullable_to_non_nullable
              as double,
      principal: null == principal
          ? _value.principal
          : principal // ignore: cast_nullable_to_non_nullable
              as double,
      interestAmount: null == interestAmount
          ? _value.interestAmount
          : interestAmount // ignore: cast_nullable_to_non_nullable
              as double,
      interestPercent: null == interestPercent
          ? _value.interestPercent
          : interestPercent // ignore: cast_nullable_to_non_nullable
              as int,
      numberInstallment: null == numberInstallment
          ? _value.numberInstallment
          : numberInstallment // ignore: cast_nullable_to_non_nullable
              as int,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      dueDate: null == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      months: null == months
          ? _value.months
          : months // ignore: cast_nullable_to_non_nullable
              as int,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as InstallmentState?,
    ));
  }
}

/// @nodoc

class _$_InstallmentDetail implements _InstallmentDetail {
  const _$_InstallmentDetail(
      {required this.amount,
      required this.paidAmount,
      required this.principalBalance,
      required this.principal,
      required this.interestAmount,
      required this.interestPercent,
      required this.numberInstallment,
      required this.currency,
      required this.dueDate,
      required this.months,
      this.state});

  @override
  final double amount;
  @override
  final double paidAmount;
  @override
  final double principalBalance;
  @override
  final double principal;
  @override
  final double interestAmount;
  @override
  final int interestPercent;
  @override
  final int numberInstallment;
  @override
  final String currency;
  @override
  final DateTime dueDate;
  @override
  final int months;
  @override
  final InstallmentState? state;

  @override
  String toString() {
    return 'InstallmentDetail(amount: $amount, paidAmount: $paidAmount, principalBalance: $principalBalance, principal: $principal, interestAmount: $interestAmount, interestPercent: $interestPercent, numberInstallment: $numberInstallment, currency: $currency, dueDate: $dueDate, months: $months, state: $state)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InstallmentDetail &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.paidAmount, paidAmount) ||
                other.paidAmount == paidAmount) &&
            (identical(other.principalBalance, principalBalance) ||
                other.principalBalance == principalBalance) &&
            (identical(other.principal, principal) ||
                other.principal == principal) &&
            (identical(other.interestAmount, interestAmount) ||
                other.interestAmount == interestAmount) &&
            (identical(other.interestPercent, interestPercent) ||
                other.interestPercent == interestPercent) &&
            (identical(other.numberInstallment, numberInstallment) ||
                other.numberInstallment == numberInstallment) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.months, months) || other.months == months) &&
            (identical(other.state, state) || other.state == state));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      amount,
      paidAmount,
      principalBalance,
      principal,
      interestAmount,
      interestPercent,
      numberInstallment,
      currency,
      dueDate,
      months,
      state);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InstallmentDetailCopyWith<_$_InstallmentDetail> get copyWith =>
      __$$_InstallmentDetailCopyWithImpl<_$_InstallmentDetail>(
          this, _$identity);
}

abstract class _InstallmentDetail implements InstallmentDetail {
  const factory _InstallmentDetail(
      {required final double amount,
      required final double paidAmount,
      required final double principalBalance,
      required final double principal,
      required final double interestAmount,
      required final int interestPercent,
      required final int numberInstallment,
      required final String currency,
      required final DateTime dueDate,
      required final int months,
      final InstallmentState? state}) = _$_InstallmentDetail;

  @override
  double get amount;
  @override
  double get paidAmount;
  @override
  double get principalBalance;
  @override
  double get principal;
  @override
  double get interestAmount;
  @override
  int get interestPercent;
  @override
  int get numberInstallment;
  @override
  String get currency;
  @override
  DateTime get dueDate;
  @override
  int get months;
  @override
  InstallmentState? get state;
  @override
  @JsonKey(ignore: true)
  _$$_InstallmentDetailCopyWith<_$_InstallmentDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$InstallmentDetailList {
  List<InstallmentDetail> get installments =>
      throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  double get downpayment => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  double get loanAmount => throw _privateConstructorUsedError;
  int get months => throw _privateConstructorUsedError;
  DateTime get dueDate => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InstallmentDetailListCopyWith<InstallmentDetailList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstallmentDetailListCopyWith<$Res> {
  factory $InstallmentDetailListCopyWith(InstallmentDetailList value,
          $Res Function(InstallmentDetailList) then) =
      _$InstallmentDetailListCopyWithImpl<$Res, InstallmentDetailList>;
  @useResult
  $Res call(
      {List<InstallmentDetail> installments,
      double price,
      double downpayment,
      double amount,
      double loanAmount,
      int months,
      DateTime dueDate,
      String currency});
}

/// @nodoc
class _$InstallmentDetailListCopyWithImpl<$Res,
        $Val extends InstallmentDetailList>
    implements $InstallmentDetailListCopyWith<$Res> {
  _$InstallmentDetailListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? installments = null,
    Object? price = null,
    Object? downpayment = null,
    Object? amount = null,
    Object? loanAmount = null,
    Object? months = null,
    Object? dueDate = null,
    Object? currency = null,
  }) {
    return _then(_value.copyWith(
      installments: null == installments
          ? _value.installments
          : installments // ignore: cast_nullable_to_non_nullable
              as List<InstallmentDetail>,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      downpayment: null == downpayment
          ? _value.downpayment
          : downpayment // ignore: cast_nullable_to_non_nullable
              as double,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      loanAmount: null == loanAmount
          ? _value.loanAmount
          : loanAmount // ignore: cast_nullable_to_non_nullable
              as double,
      months: null == months
          ? _value.months
          : months // ignore: cast_nullable_to_non_nullable
              as int,
      dueDate: null == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InstallmentDetailListCopyWith<$Res>
    implements $InstallmentDetailListCopyWith<$Res> {
  factory _$$_InstallmentDetailListCopyWith(_$_InstallmentDetailList value,
          $Res Function(_$_InstallmentDetailList) then) =
      __$$_InstallmentDetailListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<InstallmentDetail> installments,
      double price,
      double downpayment,
      double amount,
      double loanAmount,
      int months,
      DateTime dueDate,
      String currency});
}

/// @nodoc
class __$$_InstallmentDetailListCopyWithImpl<$Res>
    extends _$InstallmentDetailListCopyWithImpl<$Res, _$_InstallmentDetailList>
    implements _$$_InstallmentDetailListCopyWith<$Res> {
  __$$_InstallmentDetailListCopyWithImpl(_$_InstallmentDetailList _value,
      $Res Function(_$_InstallmentDetailList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? installments = null,
    Object? price = null,
    Object? downpayment = null,
    Object? amount = null,
    Object? loanAmount = null,
    Object? months = null,
    Object? dueDate = null,
    Object? currency = null,
  }) {
    return _then(_$_InstallmentDetailList(
      installments: null == installments
          ? _value._installments
          : installments // ignore: cast_nullable_to_non_nullable
              as List<InstallmentDetail>,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      downpayment: null == downpayment
          ? _value.downpayment
          : downpayment // ignore: cast_nullable_to_non_nullable
              as double,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      loanAmount: null == loanAmount
          ? _value.loanAmount
          : loanAmount // ignore: cast_nullable_to_non_nullable
              as double,
      months: null == months
          ? _value.months
          : months // ignore: cast_nullable_to_non_nullable
              as int,
      dueDate: null == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_InstallmentDetailList implements _InstallmentDetailList {
  const _$_InstallmentDetailList(
      {required final List<InstallmentDetail> installments,
      required this.price,
      required this.downpayment,
      required this.amount,
      required this.loanAmount,
      required this.months,
      required this.dueDate,
      required this.currency})
      : _installments = installments;

  final List<InstallmentDetail> _installments;
  @override
  List<InstallmentDetail> get installments {
    if (_installments is EqualUnmodifiableListView) return _installments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_installments);
  }

  @override
  final double price;
  @override
  final double downpayment;
  @override
  final double amount;
  @override
  final double loanAmount;
  @override
  final int months;
  @override
  final DateTime dueDate;
  @override
  final String currency;

  @override
  String toString() {
    return 'InstallmentDetailList(installments: $installments, price: $price, downpayment: $downpayment, amount: $amount, loanAmount: $loanAmount, months: $months, dueDate: $dueDate, currency: $currency)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InstallmentDetailList &&
            const DeepCollectionEquality()
                .equals(other._installments, _installments) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.downpayment, downpayment) ||
                other.downpayment == downpayment) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.loanAmount, loanAmount) ||
                other.loanAmount == loanAmount) &&
            (identical(other.months, months) || other.months == months) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_installments),
      price,
      downpayment,
      amount,
      loanAmount,
      months,
      dueDate,
      currency);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InstallmentDetailListCopyWith<_$_InstallmentDetailList> get copyWith =>
      __$$_InstallmentDetailListCopyWithImpl<_$_InstallmentDetailList>(
          this, _$identity);
}

abstract class _InstallmentDetailList implements InstallmentDetailList {
  const factory _InstallmentDetailList(
      {required final List<InstallmentDetail> installments,
      required final double price,
      required final double downpayment,
      required final double amount,
      required final double loanAmount,
      required final int months,
      required final DateTime dueDate,
      required final String currency}) = _$_InstallmentDetailList;

  @override
  List<InstallmentDetail> get installments;
  @override
  double get price;
  @override
  double get downpayment;
  @override
  double get amount;
  @override
  double get loanAmount;
  @override
  int get months;
  @override
  DateTime get dueDate;
  @override
  String get currency;
  @override
  @JsonKey(ignore: true)
  _$$_InstallmentDetailListCopyWith<_$_InstallmentDetailList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$CurrentPayment {
  PaymentState get state => throw _privateConstructorUsedError;
  DateTime? get dueDate => throw _privateConstructorUsedError;
  MoneyAmount? get paidAmount => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CurrentPaymentCopyWith<CurrentPayment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CurrentPaymentCopyWith<$Res> {
  factory $CurrentPaymentCopyWith(
          CurrentPayment value, $Res Function(CurrentPayment) then) =
      _$CurrentPaymentCopyWithImpl<$Res, CurrentPayment>;
  @useResult
  $Res call({PaymentState state, DateTime? dueDate, MoneyAmount? paidAmount});

  $MoneyAmountCopyWith<$Res>? get paidAmount;
}

/// @nodoc
class _$CurrentPaymentCopyWithImpl<$Res, $Val extends CurrentPayment>
    implements $CurrentPaymentCopyWith<$Res> {
  _$CurrentPaymentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? dueDate = freezed,
    Object? paidAmount = freezed,
  }) {
    return _then(_value.copyWith(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as PaymentState,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      paidAmount: freezed == paidAmount
          ? _value.paidAmount
          : paidAmount // ignore: cast_nullable_to_non_nullable
              as MoneyAmount?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MoneyAmountCopyWith<$Res>? get paidAmount {
    if (_value.paidAmount == null) {
      return null;
    }

    return $MoneyAmountCopyWith<$Res>(_value.paidAmount!, (value) {
      return _then(_value.copyWith(paidAmount: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CurrentPaymentCopyWith<$Res>
    implements $CurrentPaymentCopyWith<$Res> {
  factory _$$_CurrentPaymentCopyWith(
          _$_CurrentPayment value, $Res Function(_$_CurrentPayment) then) =
      __$$_CurrentPaymentCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({PaymentState state, DateTime? dueDate, MoneyAmount? paidAmount});

  @override
  $MoneyAmountCopyWith<$Res>? get paidAmount;
}

/// @nodoc
class __$$_CurrentPaymentCopyWithImpl<$Res>
    extends _$CurrentPaymentCopyWithImpl<$Res, _$_CurrentPayment>
    implements _$$_CurrentPaymentCopyWith<$Res> {
  __$$_CurrentPaymentCopyWithImpl(
      _$_CurrentPayment _value, $Res Function(_$_CurrentPayment) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? dueDate = freezed,
    Object? paidAmount = freezed,
  }) {
    return _then(_$_CurrentPayment(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as PaymentState,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      paidAmount: freezed == paidAmount
          ? _value.paidAmount
          : paidAmount // ignore: cast_nullable_to_non_nullable
              as MoneyAmount?,
    ));
  }
}

/// @nodoc

class _$_CurrentPayment implements _CurrentPayment {
  const _$_CurrentPayment(
      {required this.state, required this.dueDate, required this.paidAmount});

  @override
  final PaymentState state;
  @override
  final DateTime? dueDate;
  @override
  final MoneyAmount? paidAmount;

  @override
  String toString() {
    return 'CurrentPayment(state: $state, dueDate: $dueDate, paidAmount: $paidAmount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CurrentPayment &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.paidAmount, paidAmount) ||
                other.paidAmount == paidAmount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, state, dueDate, paidAmount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CurrentPaymentCopyWith<_$_CurrentPayment> get copyWith =>
      __$$_CurrentPaymentCopyWithImpl<_$_CurrentPayment>(this, _$identity);
}

abstract class _CurrentPayment implements CurrentPayment {
  const factory _CurrentPayment(
      {required final PaymentState state,
      required final DateTime? dueDate,
      required final MoneyAmount? paidAmount}) = _$_CurrentPayment;

  @override
  PaymentState get state;
  @override
  DateTime? get dueDate;
  @override
  MoneyAmount? get paidAmount;
  @override
  @JsonKey(ignore: true)
  _$$_CurrentPaymentCopyWith<_$_CurrentPayment> get copyWith =>
      throw _privateConstructorUsedError;
}
