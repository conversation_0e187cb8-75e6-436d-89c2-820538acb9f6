// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'paylater_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$PaylaterFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedPaylaterFailure value)
        unexpectedFailure,
    required TResult Function(NoContentPaylaterFailure value) noContent,
    required TResult Function(TooManyRequestsPaylaterFailure value)
        tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedPaylaterFailure value)? unexpectedFailure,
    TResult? Function(NoContentPaylaterFailure value)? noContent,
    TResult? Function(TooManyRequestsPaylaterFailure value)? tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedPaylaterFailure value)? unexpectedFailure,
    TResult Function(NoContentPaylaterFailure value)? noContent,
    TResult Function(TooManyRequestsPaylaterFailure value)? tooManyRequests,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaylaterFailureCopyWith<$Res> {
  factory $PaylaterFailureCopyWith(
          PaylaterFailure value, $Res Function(PaylaterFailure) then) =
      _$PaylaterFailureCopyWithImpl<$Res, PaylaterFailure>;
}

/// @nodoc
class _$PaylaterFailureCopyWithImpl<$Res, $Val extends PaylaterFailure>
    implements $PaylaterFailureCopyWith<$Res> {
  _$PaylaterFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$UnexpectedPaylaterFailureCopyWith<$Res> {
  factory _$$UnexpectedPaylaterFailureCopyWith(
          _$UnexpectedPaylaterFailure value,
          $Res Function(_$UnexpectedPaylaterFailure) then) =
      __$$UnexpectedPaylaterFailureCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedPaylaterFailureCopyWithImpl<$Res>
    extends _$PaylaterFailureCopyWithImpl<$Res, _$UnexpectedPaylaterFailure>
    implements _$$UnexpectedPaylaterFailureCopyWith<$Res> {
  __$$UnexpectedPaylaterFailureCopyWithImpl(_$UnexpectedPaylaterFailure _value,
      $Res Function(_$UnexpectedPaylaterFailure) _then)
      : super(_value, _then);
}

/// @nodoc

class _$UnexpectedPaylaterFailure extends UnexpectedPaylaterFailure {
  const _$UnexpectedPaylaterFailure() : super._();

  @override
  String toString() {
    return 'PaylaterFailure.unexpectedFailure()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnexpectedPaylaterFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return unexpectedFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return unexpectedFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (unexpectedFailure != null) {
      return unexpectedFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedPaylaterFailure value)
        unexpectedFailure,
    required TResult Function(NoContentPaylaterFailure value) noContent,
    required TResult Function(TooManyRequestsPaylaterFailure value)
        tooManyRequests,
  }) {
    return unexpectedFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedPaylaterFailure value)? unexpectedFailure,
    TResult? Function(NoContentPaylaterFailure value)? noContent,
    TResult? Function(TooManyRequestsPaylaterFailure value)? tooManyRequests,
  }) {
    return unexpectedFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedPaylaterFailure value)? unexpectedFailure,
    TResult Function(NoContentPaylaterFailure value)? noContent,
    TResult Function(TooManyRequestsPaylaterFailure value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (unexpectedFailure != null) {
      return unexpectedFailure(this);
    }
    return orElse();
  }
}

abstract class UnexpectedPaylaterFailure extends PaylaterFailure {
  const factory UnexpectedPaylaterFailure() = _$UnexpectedPaylaterFailure;
  const UnexpectedPaylaterFailure._() : super._();
}

/// @nodoc
abstract class _$$NoContentPaylaterFailureCopyWith<$Res> {
  factory _$$NoContentPaylaterFailureCopyWith(_$NoContentPaylaterFailure value,
          $Res Function(_$NoContentPaylaterFailure) then) =
      __$$NoContentPaylaterFailureCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NoContentPaylaterFailureCopyWithImpl<$Res>
    extends _$PaylaterFailureCopyWithImpl<$Res, _$NoContentPaylaterFailure>
    implements _$$NoContentPaylaterFailureCopyWith<$Res> {
  __$$NoContentPaylaterFailureCopyWithImpl(_$NoContentPaylaterFailure _value,
      $Res Function(_$NoContentPaylaterFailure) _then)
      : super(_value, _then);
}

/// @nodoc

class _$NoContentPaylaterFailure extends NoContentPaylaterFailure {
  const _$NoContentPaylaterFailure() : super._();

  @override
  String toString() {
    return 'PaylaterFailure.noContent()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NoContentPaylaterFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return noContent();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return noContent?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (noContent != null) {
      return noContent();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedPaylaterFailure value)
        unexpectedFailure,
    required TResult Function(NoContentPaylaterFailure value) noContent,
    required TResult Function(TooManyRequestsPaylaterFailure value)
        tooManyRequests,
  }) {
    return noContent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedPaylaterFailure value)? unexpectedFailure,
    TResult? Function(NoContentPaylaterFailure value)? noContent,
    TResult? Function(TooManyRequestsPaylaterFailure value)? tooManyRequests,
  }) {
    return noContent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedPaylaterFailure value)? unexpectedFailure,
    TResult Function(NoContentPaylaterFailure value)? noContent,
    TResult Function(TooManyRequestsPaylaterFailure value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (noContent != null) {
      return noContent(this);
    }
    return orElse();
  }
}

abstract class NoContentPaylaterFailure extends PaylaterFailure {
  const factory NoContentPaylaterFailure() = _$NoContentPaylaterFailure;
  const NoContentPaylaterFailure._() : super._();
}

/// @nodoc
abstract class _$$TooManyRequestsPaylaterFailureCopyWith<$Res> {
  factory _$$TooManyRequestsPaylaterFailureCopyWith(
          _$TooManyRequestsPaylaterFailure value,
          $Res Function(_$TooManyRequestsPaylaterFailure) then) =
      __$$TooManyRequestsPaylaterFailureCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TooManyRequestsPaylaterFailureCopyWithImpl<$Res>
    extends _$PaylaterFailureCopyWithImpl<$Res,
        _$TooManyRequestsPaylaterFailure>
    implements _$$TooManyRequestsPaylaterFailureCopyWith<$Res> {
  __$$TooManyRequestsPaylaterFailureCopyWithImpl(
      _$TooManyRequestsPaylaterFailure _value,
      $Res Function(_$TooManyRequestsPaylaterFailure) _then)
      : super(_value, _then);
}

/// @nodoc

class _$TooManyRequestsPaylaterFailure extends TooManyRequestsPaylaterFailure {
  const _$TooManyRequestsPaylaterFailure() : super._();

  @override
  String toString() {
    return 'PaylaterFailure.tooManyRequests()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TooManyRequestsPaylaterFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return tooManyRequests();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return tooManyRequests?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (tooManyRequests != null) {
      return tooManyRequests();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedPaylaterFailure value)
        unexpectedFailure,
    required TResult Function(NoContentPaylaterFailure value) noContent,
    required TResult Function(TooManyRequestsPaylaterFailure value)
        tooManyRequests,
  }) {
    return tooManyRequests(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedPaylaterFailure value)? unexpectedFailure,
    TResult? Function(NoContentPaylaterFailure value)? noContent,
    TResult? Function(TooManyRequestsPaylaterFailure value)? tooManyRequests,
  }) {
    return tooManyRequests?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedPaylaterFailure value)? unexpectedFailure,
    TResult Function(NoContentPaylaterFailure value)? noContent,
    TResult Function(TooManyRequestsPaylaterFailure value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (tooManyRequests != null) {
      return tooManyRequests(this);
    }
    return orElse();
  }
}

abstract class TooManyRequestsPaylaterFailure extends PaylaterFailure {
  const factory TooManyRequestsPaylaterFailure() =
      _$TooManyRequestsPaylaterFailure;
  const TooManyRequestsPaylaterFailure._() : super._();
}
