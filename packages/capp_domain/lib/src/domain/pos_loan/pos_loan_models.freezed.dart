// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pos_loan_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$PosLoanDetail {
  Decimal? get dueAmount => throw _privateConstructorUsedError;
  DateTime? get paymentDueDate => throw _privateConstructorUsedError;
  Decimal? get loanAmount => throw _privateConstructorUsedError;
  int? get term => throw _privateConstructorUsedError;
  Decimal? get installmentAmount => throw _privateConstructorUsedError;
  String? get contractNumber => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  Decimal? get interest => throw _privateConstructorUsedError;
  Decimal? get processingFee => throw _privateConstructorUsedError;
  Decimal? get safePayFee => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  DateTime? get nextRecommendedPayment => throw _privateConstructorUsedError;
  List<PosLoanItem>? get items => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PosLoanDetailCopyWith<PosLoanDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PosLoanDetailCopyWith<$Res> {
  factory $PosLoanDetailCopyWith(
          PosLoanDetail value, $Res Function(PosLoanDetail) then) =
      _$PosLoanDetailCopyWithImpl<$Res, PosLoanDetail>;
  @useResult
  $Res call(
      {Decimal? dueAmount,
      DateTime? paymentDueDate,
      Decimal? loanAmount,
      int? term,
      Decimal? installmentAmount,
      String? contractNumber,
      DateTime? startDate,
      DateTime? endDate,
      Decimal? interest,
      Decimal? processingFee,
      Decimal? safePayFee,
      String? currency,
      DateTime? nextRecommendedPayment,
      List<PosLoanItem>? items});
}

/// @nodoc
class _$PosLoanDetailCopyWithImpl<$Res, $Val extends PosLoanDetail>
    implements $PosLoanDetailCopyWith<$Res> {
  _$PosLoanDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dueAmount = freezed,
    Object? paymentDueDate = freezed,
    Object? loanAmount = freezed,
    Object? term = freezed,
    Object? installmentAmount = freezed,
    Object? contractNumber = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? interest = freezed,
    Object? processingFee = freezed,
    Object? safePayFee = freezed,
    Object? currency = freezed,
    Object? nextRecommendedPayment = freezed,
    Object? items = freezed,
  }) {
    return _then(_value.copyWith(
      dueAmount: freezed == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      paymentDueDate: freezed == paymentDueDate
          ? _value.paymentDueDate
          : paymentDueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loanAmount: freezed == loanAmount
          ? _value.loanAmount
          : loanAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      term: freezed == term
          ? _value.term
          : term // ignore: cast_nullable_to_non_nullable
              as int?,
      installmentAmount: freezed == installmentAmount
          ? _value.installmentAmount
          : installmentAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      interest: freezed == interest
          ? _value.interest
          : interest // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      processingFee: freezed == processingFee
          ? _value.processingFee
          : processingFee // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      safePayFee: freezed == safePayFee
          ? _value.safePayFee
          : safePayFee // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      nextRecommendedPayment: freezed == nextRecommendedPayment
          ? _value.nextRecommendedPayment
          : nextRecommendedPayment // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PosLoanItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PosLoanDetailCopyWith<$Res>
    implements $PosLoanDetailCopyWith<$Res> {
  factory _$$_PosLoanDetailCopyWith(
          _$_PosLoanDetail value, $Res Function(_$_PosLoanDetail) then) =
      __$$_PosLoanDetailCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Decimal? dueAmount,
      DateTime? paymentDueDate,
      Decimal? loanAmount,
      int? term,
      Decimal? installmentAmount,
      String? contractNumber,
      DateTime? startDate,
      DateTime? endDate,
      Decimal? interest,
      Decimal? processingFee,
      Decimal? safePayFee,
      String? currency,
      DateTime? nextRecommendedPayment,
      List<PosLoanItem>? items});
}

/// @nodoc
class __$$_PosLoanDetailCopyWithImpl<$Res>
    extends _$PosLoanDetailCopyWithImpl<$Res, _$_PosLoanDetail>
    implements _$$_PosLoanDetailCopyWith<$Res> {
  __$$_PosLoanDetailCopyWithImpl(
      _$_PosLoanDetail _value, $Res Function(_$_PosLoanDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dueAmount = freezed,
    Object? paymentDueDate = freezed,
    Object? loanAmount = freezed,
    Object? term = freezed,
    Object? installmentAmount = freezed,
    Object? contractNumber = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? interest = freezed,
    Object? processingFee = freezed,
    Object? safePayFee = freezed,
    Object? currency = freezed,
    Object? nextRecommendedPayment = freezed,
    Object? items = freezed,
  }) {
    return _then(_$_PosLoanDetail(
      dueAmount: freezed == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      paymentDueDate: freezed == paymentDueDate
          ? _value.paymentDueDate
          : paymentDueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loanAmount: freezed == loanAmount
          ? _value.loanAmount
          : loanAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      term: freezed == term
          ? _value.term
          : term // ignore: cast_nullable_to_non_nullable
              as int?,
      installmentAmount: freezed == installmentAmount
          ? _value.installmentAmount
          : installmentAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      interest: freezed == interest
          ? _value.interest
          : interest // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      processingFee: freezed == processingFee
          ? _value.processingFee
          : processingFee // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      safePayFee: freezed == safePayFee
          ? _value.safePayFee
          : safePayFee // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      nextRecommendedPayment: freezed == nextRecommendedPayment
          ? _value.nextRecommendedPayment
          : nextRecommendedPayment // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PosLoanItem>?,
    ));
  }
}

/// @nodoc

class _$_PosLoanDetail implements _PosLoanDetail {
  const _$_PosLoanDetail(
      {this.dueAmount,
      this.paymentDueDate,
      this.loanAmount,
      this.term,
      this.installmentAmount,
      this.contractNumber,
      this.startDate,
      this.endDate,
      this.interest,
      this.processingFee,
      this.safePayFee,
      this.currency,
      this.nextRecommendedPayment,
      final List<PosLoanItem>? items})
      : _items = items;

  @override
  final Decimal? dueAmount;
  @override
  final DateTime? paymentDueDate;
  @override
  final Decimal? loanAmount;
  @override
  final int? term;
  @override
  final Decimal? installmentAmount;
  @override
  final String? contractNumber;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  final Decimal? interest;
  @override
  final Decimal? processingFee;
  @override
  final Decimal? safePayFee;
  @override
  final String? currency;
  @override
  final DateTime? nextRecommendedPayment;
  final List<PosLoanItem>? _items;
  @override
  List<PosLoanItem>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PosLoanDetail(dueAmount: $dueAmount, paymentDueDate: $paymentDueDate, loanAmount: $loanAmount, term: $term, installmentAmount: $installmentAmount, contractNumber: $contractNumber, startDate: $startDate, endDate: $endDate, interest: $interest, processingFee: $processingFee, safePayFee: $safePayFee, currency: $currency, nextRecommendedPayment: $nextRecommendedPayment, items: $items)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PosLoanDetail &&
            (identical(other.dueAmount, dueAmount) ||
                other.dueAmount == dueAmount) &&
            (identical(other.paymentDueDate, paymentDueDate) ||
                other.paymentDueDate == paymentDueDate) &&
            (identical(other.loanAmount, loanAmount) ||
                other.loanAmount == loanAmount) &&
            (identical(other.term, term) || other.term == term) &&
            (identical(other.installmentAmount, installmentAmount) ||
                other.installmentAmount == installmentAmount) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.interest, interest) ||
                other.interest == interest) &&
            (identical(other.processingFee, processingFee) ||
                other.processingFee == processingFee) &&
            (identical(other.safePayFee, safePayFee) ||
                other.safePayFee == safePayFee) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.nextRecommendedPayment, nextRecommendedPayment) ||
                other.nextRecommendedPayment == nextRecommendedPayment) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      dueAmount,
      paymentDueDate,
      loanAmount,
      term,
      installmentAmount,
      contractNumber,
      startDate,
      endDate,
      interest,
      processingFee,
      safePayFee,
      currency,
      nextRecommendedPayment,
      const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PosLoanDetailCopyWith<_$_PosLoanDetail> get copyWith =>
      __$$_PosLoanDetailCopyWithImpl<_$_PosLoanDetail>(this, _$identity);
}

abstract class _PosLoanDetail implements PosLoanDetail {
  const factory _PosLoanDetail(
      {final Decimal? dueAmount,
      final DateTime? paymentDueDate,
      final Decimal? loanAmount,
      final int? term,
      final Decimal? installmentAmount,
      final String? contractNumber,
      final DateTime? startDate,
      final DateTime? endDate,
      final Decimal? interest,
      final Decimal? processingFee,
      final Decimal? safePayFee,
      final String? currency,
      final DateTime? nextRecommendedPayment,
      final List<PosLoanItem>? items}) = _$_PosLoanDetail;

  @override
  Decimal? get dueAmount;
  @override
  DateTime? get paymentDueDate;
  @override
  Decimal? get loanAmount;
  @override
  int? get term;
  @override
  Decimal? get installmentAmount;
  @override
  String? get contractNumber;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  Decimal? get interest;
  @override
  Decimal? get processingFee;
  @override
  Decimal? get safePayFee;
  @override
  String? get currency;
  @override
  DateTime? get nextRecommendedPayment;
  @override
  List<PosLoanItem>? get items;
  @override
  @JsonKey(ignore: true)
  _$$_PosLoanDetailCopyWith<_$_PosLoanDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PosLoanItem {
  DateTime? get date => throw _privateConstructorUsedError;
  Decimal? get amount => throw _privateConstructorUsedError;
  InstallmentState? get state => throw _privateConstructorUsedError;
  num? get itemNumber => throw _privateConstructorUsedError;
  Decimal? get principleRepayment => throw _privateConstructorUsedError;
  Decimal? get interest => throw _privateConstructorUsedError;
  Decimal? get safePay => throw _privateConstructorUsedError;
  Decimal? get latePaymentFees => throw _privateConstructorUsedError;
  List<String>? get transactionIds => throw _privateConstructorUsedError;
  DateTime? get paymentDate => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PosLoanItemCopyWith<PosLoanItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PosLoanItemCopyWith<$Res> {
  factory $PosLoanItemCopyWith(
          PosLoanItem value, $Res Function(PosLoanItem) then) =
      _$PosLoanItemCopyWithImpl<$Res, PosLoanItem>;
  @useResult
  $Res call(
      {DateTime? date,
      Decimal? amount,
      InstallmentState? state,
      num? itemNumber,
      Decimal? principleRepayment,
      Decimal? interest,
      Decimal? safePay,
      Decimal? latePaymentFees,
      List<String>? transactionIds,
      DateTime? paymentDate});
}

/// @nodoc
class _$PosLoanItemCopyWithImpl<$Res, $Val extends PosLoanItem>
    implements $PosLoanItemCopyWith<$Res> {
  _$PosLoanItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? amount = freezed,
    Object? state = freezed,
    Object? itemNumber = freezed,
    Object? principleRepayment = freezed,
    Object? interest = freezed,
    Object? safePay = freezed,
    Object? latePaymentFees = freezed,
    Object? transactionIds = freezed,
    Object? paymentDate = freezed,
  }) {
    return _then(_value.copyWith(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as InstallmentState?,
      itemNumber: freezed == itemNumber
          ? _value.itemNumber
          : itemNumber // ignore: cast_nullable_to_non_nullable
              as num?,
      principleRepayment: freezed == principleRepayment
          ? _value.principleRepayment
          : principleRepayment // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      interest: freezed == interest
          ? _value.interest
          : interest // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      safePay: freezed == safePay
          ? _value.safePay
          : safePay // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      latePaymentFees: freezed == latePaymentFees
          ? _value.latePaymentFees
          : latePaymentFees // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      transactionIds: freezed == transactionIds
          ? _value.transactionIds
          : transactionIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      paymentDate: freezed == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PosLoanItemCopyWith<$Res>
    implements $PosLoanItemCopyWith<$Res> {
  factory _$$_PosLoanItemCopyWith(
          _$_PosLoanItem value, $Res Function(_$_PosLoanItem) then) =
      __$$_PosLoanItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime? date,
      Decimal? amount,
      InstallmentState? state,
      num? itemNumber,
      Decimal? principleRepayment,
      Decimal? interest,
      Decimal? safePay,
      Decimal? latePaymentFees,
      List<String>? transactionIds,
      DateTime? paymentDate});
}

/// @nodoc
class __$$_PosLoanItemCopyWithImpl<$Res>
    extends _$PosLoanItemCopyWithImpl<$Res, _$_PosLoanItem>
    implements _$$_PosLoanItemCopyWith<$Res> {
  __$$_PosLoanItemCopyWithImpl(
      _$_PosLoanItem _value, $Res Function(_$_PosLoanItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? amount = freezed,
    Object? state = freezed,
    Object? itemNumber = freezed,
    Object? principleRepayment = freezed,
    Object? interest = freezed,
    Object? safePay = freezed,
    Object? latePaymentFees = freezed,
    Object? transactionIds = freezed,
    Object? paymentDate = freezed,
  }) {
    return _then(_$_PosLoanItem(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as InstallmentState?,
      itemNumber: freezed == itemNumber
          ? _value.itemNumber
          : itemNumber // ignore: cast_nullable_to_non_nullable
              as num?,
      principleRepayment: freezed == principleRepayment
          ? _value.principleRepayment
          : principleRepayment // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      interest: freezed == interest
          ? _value.interest
          : interest // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      safePay: freezed == safePay
          ? _value.safePay
          : safePay // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      latePaymentFees: freezed == latePaymentFees
          ? _value.latePaymentFees
          : latePaymentFees // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      transactionIds: freezed == transactionIds
          ? _value._transactionIds
          : transactionIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      paymentDate: freezed == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$_PosLoanItem implements _PosLoanItem {
  const _$_PosLoanItem(
      {this.date,
      this.amount,
      this.state,
      this.itemNumber,
      this.principleRepayment,
      this.interest,
      this.safePay,
      this.latePaymentFees,
      final List<String>? transactionIds,
      this.paymentDate})
      : _transactionIds = transactionIds;

  @override
  final DateTime? date;
  @override
  final Decimal? amount;
  @override
  final InstallmentState? state;
  @override
  final num? itemNumber;
  @override
  final Decimal? principleRepayment;
  @override
  final Decimal? interest;
  @override
  final Decimal? safePay;
  @override
  final Decimal? latePaymentFees;
  final List<String>? _transactionIds;
  @override
  List<String>? get transactionIds {
    final value = _transactionIds;
    if (value == null) return null;
    if (_transactionIds is EqualUnmodifiableListView) return _transactionIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? paymentDate;

  @override
  String toString() {
    return 'PosLoanItem(date: $date, amount: $amount, state: $state, itemNumber: $itemNumber, principleRepayment: $principleRepayment, interest: $interest, safePay: $safePay, latePaymentFees: $latePaymentFees, transactionIds: $transactionIds, paymentDate: $paymentDate)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PosLoanItem &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.itemNumber, itemNumber) ||
                other.itemNumber == itemNumber) &&
            (identical(other.principleRepayment, principleRepayment) ||
                other.principleRepayment == principleRepayment) &&
            (identical(other.interest, interest) ||
                other.interest == interest) &&
            (identical(other.safePay, safePay) || other.safePay == safePay) &&
            (identical(other.latePaymentFees, latePaymentFees) ||
                other.latePaymentFees == latePaymentFees) &&
            const DeepCollectionEquality()
                .equals(other._transactionIds, _transactionIds) &&
            (identical(other.paymentDate, paymentDate) ||
                other.paymentDate == paymentDate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      date,
      amount,
      state,
      itemNumber,
      principleRepayment,
      interest,
      safePay,
      latePaymentFees,
      const DeepCollectionEquality().hash(_transactionIds),
      paymentDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PosLoanItemCopyWith<_$_PosLoanItem> get copyWith =>
      __$$_PosLoanItemCopyWithImpl<_$_PosLoanItem>(this, _$identity);
}

abstract class _PosLoanItem implements PosLoanItem {
  const factory _PosLoanItem(
      {final DateTime? date,
      final Decimal? amount,
      final InstallmentState? state,
      final num? itemNumber,
      final Decimal? principleRepayment,
      final Decimal? interest,
      final Decimal? safePay,
      final Decimal? latePaymentFees,
      final List<String>? transactionIds,
      final DateTime? paymentDate}) = _$_PosLoanItem;

  @override
  DateTime? get date;
  @override
  Decimal? get amount;
  @override
  InstallmentState? get state;
  @override
  num? get itemNumber;
  @override
  Decimal? get principleRepayment;
  @override
  Decimal? get interest;
  @override
  Decimal? get safePay;
  @override
  Decimal? get latePaymentFees;
  @override
  List<String>? get transactionIds;
  @override
  DateTime? get paymentDate;
  @override
  @JsonKey(ignore: true)
  _$$_PosLoanItemCopyWith<_$_PosLoanItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PosLoanOverview {
  int get loanId => throw _privateConstructorUsedError;
  PosLoanLoanType get loanType => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  Decimal get dueAmount => throw _privateConstructorUsedError;
  DateTime get dueDate => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PosLoanOverviewCopyWith<PosLoanOverview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PosLoanOverviewCopyWith<$Res> {
  factory $PosLoanOverviewCopyWith(
          PosLoanOverview value, $Res Function(PosLoanOverview) then) =
      _$PosLoanOverviewCopyWithImpl<$Res, PosLoanOverview>;
  @useResult
  $Res call(
      {int loanId,
      PosLoanLoanType loanType,
      String currency,
      Decimal dueAmount,
      DateTime dueDate});
}

/// @nodoc
class _$PosLoanOverviewCopyWithImpl<$Res, $Val extends PosLoanOverview>
    implements $PosLoanOverviewCopyWith<$Res> {
  _$PosLoanOverviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loanId = null,
    Object? loanType = null,
    Object? currency = null,
    Object? dueAmount = null,
    Object? dueDate = null,
  }) {
    return _then(_value.copyWith(
      loanId: null == loanId
          ? _value.loanId
          : loanId // ignore: cast_nullable_to_non_nullable
              as int,
      loanType: null == loanType
          ? _value.loanType
          : loanType // ignore: cast_nullable_to_non_nullable
              as PosLoanLoanType,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      dueAmount: null == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      dueDate: null == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PosLoanOverviewCopyWith<$Res>
    implements $PosLoanOverviewCopyWith<$Res> {
  factory _$$_PosLoanOverviewCopyWith(
          _$_PosLoanOverview value, $Res Function(_$_PosLoanOverview) then) =
      __$$_PosLoanOverviewCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int loanId,
      PosLoanLoanType loanType,
      String currency,
      Decimal dueAmount,
      DateTime dueDate});
}

/// @nodoc
class __$$_PosLoanOverviewCopyWithImpl<$Res>
    extends _$PosLoanOverviewCopyWithImpl<$Res, _$_PosLoanOverview>
    implements _$$_PosLoanOverviewCopyWith<$Res> {
  __$$_PosLoanOverviewCopyWithImpl(
      _$_PosLoanOverview _value, $Res Function(_$_PosLoanOverview) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loanId = null,
    Object? loanType = null,
    Object? currency = null,
    Object? dueAmount = null,
    Object? dueDate = null,
  }) {
    return _then(_$_PosLoanOverview(
      loanId: null == loanId
          ? _value.loanId
          : loanId // ignore: cast_nullable_to_non_nullable
              as int,
      loanType: null == loanType
          ? _value.loanType
          : loanType // ignore: cast_nullable_to_non_nullable
              as PosLoanLoanType,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      dueAmount: null == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      dueDate: null == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$_PosLoanOverview implements _PosLoanOverview {
  const _$_PosLoanOverview(
      {required this.loanId,
      required this.loanType,
      required this.currency,
      required this.dueAmount,
      required this.dueDate});

  @override
  final int loanId;
  @override
  final PosLoanLoanType loanType;
  @override
  final String currency;
  @override
  final Decimal dueAmount;
  @override
  final DateTime dueDate;

  @override
  String toString() {
    return 'PosLoanOverview(loanId: $loanId, loanType: $loanType, currency: $currency, dueAmount: $dueAmount, dueDate: $dueDate)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PosLoanOverview &&
            (identical(other.loanId, loanId) || other.loanId == loanId) &&
            (identical(other.loanType, loanType) ||
                other.loanType == loanType) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.dueAmount, dueAmount) ||
                other.dueAmount == dueAmount) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, loanId, loanType, currency, dueAmount, dueDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PosLoanOverviewCopyWith<_$_PosLoanOverview> get copyWith =>
      __$$_PosLoanOverviewCopyWithImpl<_$_PosLoanOverview>(this, _$identity);
}

abstract class _PosLoanOverview implements PosLoanOverview {
  const factory _PosLoanOverview(
      {required final int loanId,
      required final PosLoanLoanType loanType,
      required final String currency,
      required final Decimal dueAmount,
      required final DateTime dueDate}) = _$_PosLoanOverview;

  @override
  int get loanId;
  @override
  PosLoanLoanType get loanType;
  @override
  String get currency;
  @override
  Decimal get dueAmount;
  @override
  DateTime get dueDate;
  @override
  @JsonKey(ignore: true)
  _$$_PosLoanOverviewCopyWith<_$_PosLoanOverview> get copyWith =>
      throw _privateConstructorUsedError;
}
