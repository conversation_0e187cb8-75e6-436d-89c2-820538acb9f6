// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_services_detail.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ProductServicesDetail {
  String? get id => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get ctaButtonUrl => throw _privateConstructorUsedError;
  String? get ctaButtonText => throw _privateConstructorUsedError;
  String? get bannerImageUrl => throw _privateConstructorUsedError;
  List<String>? get widgetIds => throw _privateConstructorUsedError;
  List<ProductWidgetTypeEnum>? get widgetTypes =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ProductServicesDetailCopyWith<ProductServicesDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductServicesDetailCopyWith<$Res> {
  factory $ProductServicesDetailCopyWith(ProductServicesDetail value,
          $Res Function(ProductServicesDetail) then) =
      _$ProductServicesDetailCopyWithImpl<$Res, ProductServicesDetail>;
  @useResult
  $Res call(
      {String? id,
      String? title,
      String? ctaButtonUrl,
      String? ctaButtonText,
      String? bannerImageUrl,
      List<String>? widgetIds,
      List<ProductWidgetTypeEnum>? widgetTypes});
}

/// @nodoc
class _$ProductServicesDetailCopyWithImpl<$Res,
        $Val extends ProductServicesDetail>
    implements $ProductServicesDetailCopyWith<$Res> {
  _$ProductServicesDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? ctaButtonUrl = freezed,
    Object? ctaButtonText = freezed,
    Object? bannerImageUrl = freezed,
    Object? widgetIds = freezed,
    Object? widgetTypes = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      ctaButtonUrl: freezed == ctaButtonUrl
          ? _value.ctaButtonUrl
          : ctaButtonUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ctaButtonText: freezed == ctaButtonText
          ? _value.ctaButtonText
          : ctaButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImageUrl: freezed == bannerImageUrl
          ? _value.bannerImageUrl
          : bannerImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      widgetIds: freezed == widgetIds
          ? _value.widgetIds
          : widgetIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      widgetTypes: freezed == widgetTypes
          ? _value.widgetTypes
          : widgetTypes // ignore: cast_nullable_to_non_nullable
              as List<ProductWidgetTypeEnum>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ProductServicesDetailCopyWith<$Res>
    implements $ProductServicesDetailCopyWith<$Res> {
  factory _$$_ProductServicesDetailCopyWith(_$_ProductServicesDetail value,
          $Res Function(_$_ProductServicesDetail) then) =
      __$$_ProductServicesDetailCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? title,
      String? ctaButtonUrl,
      String? ctaButtonText,
      String? bannerImageUrl,
      List<String>? widgetIds,
      List<ProductWidgetTypeEnum>? widgetTypes});
}

/// @nodoc
class __$$_ProductServicesDetailCopyWithImpl<$Res>
    extends _$ProductServicesDetailCopyWithImpl<$Res, _$_ProductServicesDetail>
    implements _$$_ProductServicesDetailCopyWith<$Res> {
  __$$_ProductServicesDetailCopyWithImpl(_$_ProductServicesDetail _value,
      $Res Function(_$_ProductServicesDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? ctaButtonUrl = freezed,
    Object? ctaButtonText = freezed,
    Object? bannerImageUrl = freezed,
    Object? widgetIds = freezed,
    Object? widgetTypes = freezed,
  }) {
    return _then(_$_ProductServicesDetail(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      ctaButtonUrl: freezed == ctaButtonUrl
          ? _value.ctaButtonUrl
          : ctaButtonUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ctaButtonText: freezed == ctaButtonText
          ? _value.ctaButtonText
          : ctaButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImageUrl: freezed == bannerImageUrl
          ? _value.bannerImageUrl
          : bannerImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      widgetIds: freezed == widgetIds
          ? _value._widgetIds
          : widgetIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      widgetTypes: freezed == widgetTypes
          ? _value._widgetTypes
          : widgetTypes // ignore: cast_nullable_to_non_nullable
              as List<ProductWidgetTypeEnum>?,
    ));
  }
}

/// @nodoc

class _$_ProductServicesDetail implements _ProductServicesDetail {
  _$_ProductServicesDetail(
      {required this.id,
      required this.title,
      required this.ctaButtonUrl,
      required this.ctaButtonText,
      required this.bannerImageUrl,
      required final List<String>? widgetIds,
      required final List<ProductWidgetTypeEnum>? widgetTypes})
      : _widgetIds = widgetIds,
        _widgetTypes = widgetTypes;

  @override
  final String? id;
  @override
  final String? title;
  @override
  final String? ctaButtonUrl;
  @override
  final String? ctaButtonText;
  @override
  final String? bannerImageUrl;
  final List<String>? _widgetIds;
  @override
  List<String>? get widgetIds {
    final value = _widgetIds;
    if (value == null) return null;
    if (_widgetIds is EqualUnmodifiableListView) return _widgetIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductWidgetTypeEnum>? _widgetTypes;
  @override
  List<ProductWidgetTypeEnum>? get widgetTypes {
    final value = _widgetTypes;
    if (value == null) return null;
    if (_widgetTypes is EqualUnmodifiableListView) return _widgetTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProductServicesDetail(id: $id, title: $title, ctaButtonUrl: $ctaButtonUrl, ctaButtonText: $ctaButtonText, bannerImageUrl: $bannerImageUrl, widgetIds: $widgetIds, widgetTypes: $widgetTypes)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ProductServicesDetail &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.ctaButtonUrl, ctaButtonUrl) ||
                other.ctaButtonUrl == ctaButtonUrl) &&
            (identical(other.ctaButtonText, ctaButtonText) ||
                other.ctaButtonText == ctaButtonText) &&
            (identical(other.bannerImageUrl, bannerImageUrl) ||
                other.bannerImageUrl == bannerImageUrl) &&
            const DeepCollectionEquality()
                .equals(other._widgetIds, _widgetIds) &&
            const DeepCollectionEquality()
                .equals(other._widgetTypes, _widgetTypes));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      ctaButtonUrl,
      ctaButtonText,
      bannerImageUrl,
      const DeepCollectionEquality().hash(_widgetIds),
      const DeepCollectionEquality().hash(_widgetTypes));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProductServicesDetailCopyWith<_$_ProductServicesDetail> get copyWith =>
      __$$_ProductServicesDetailCopyWithImpl<_$_ProductServicesDetail>(
          this, _$identity);
}

abstract class _ProductServicesDetail implements ProductServicesDetail {
  factory _ProductServicesDetail(
          {required final String? id,
          required final String? title,
          required final String? ctaButtonUrl,
          required final String? ctaButtonText,
          required final String? bannerImageUrl,
          required final List<String>? widgetIds,
          required final List<ProductWidgetTypeEnum>? widgetTypes}) =
      _$_ProductServicesDetail;

  @override
  String? get id;
  @override
  String? get title;
  @override
  String? get ctaButtonUrl;
  @override
  String? get ctaButtonText;
  @override
  String? get bannerImageUrl;
  @override
  List<String>? get widgetIds;
  @override
  List<ProductWidgetTypeEnum>? get widgetTypes;
  @override
  @JsonKey(ignore: true)
  _$$_ProductServicesDetailCopyWith<_$_ProductServicesDetail> get copyWith =>
      throw _privateConstructorUsedError;
}
