// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'promotion.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$Promotion {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  List<PromotionSubsection> get subsections =>
      throw _privateConstructorUsedError;
  DateTime? get validFrom => throw _privateConstructorUsedError;
  DateTime? get validTo => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  String get categoryId => throw _privateConstructorUsedError;
  String get targetUrl => throw _privateConstructorUsedError;
  bool get enabled => throw _privateConstructorUsedError;
  String get ctaBtnText => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PromotionCopyWith<Promotion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PromotionCopyWith<$Res> {
  factory $PromotionCopyWith(Promotion value, $Res Function(Promotion) then) =
      _$PromotionCopyWithImpl<$Res, Promotion>;
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      List<PromotionSubsection> subsections,
      DateTime? validFrom,
      DateTime? validTo,
      DateTime? createdAt,
      String imageUrl,
      String categoryId,
      String targetUrl,
      bool enabled,
      String ctaBtnText});
}

/// @nodoc
class _$PromotionCopyWithImpl<$Res, $Val extends Promotion>
    implements $PromotionCopyWith<$Res> {
  _$PromotionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? subsections = null,
    Object? validFrom = freezed,
    Object? validTo = freezed,
    Object? createdAt = freezed,
    Object? imageUrl = null,
    Object? categoryId = null,
    Object? targetUrl = null,
    Object? enabled = null,
    Object? ctaBtnText = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      subsections: null == subsections
          ? _value.subsections
          : subsections // ignore: cast_nullable_to_non_nullable
              as List<PromotionSubsection>,
      validFrom: freezed == validFrom
          ? _value.validFrom
          : validFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      ctaBtnText: null == ctaBtnText
          ? _value.ctaBtnText
          : ctaBtnText // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PromotionCopyWith<$Res> implements $PromotionCopyWith<$Res> {
  factory _$$_PromotionCopyWith(
          _$_Promotion value, $Res Function(_$_Promotion) then) =
      __$$_PromotionCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      List<PromotionSubsection> subsections,
      DateTime? validFrom,
      DateTime? validTo,
      DateTime? createdAt,
      String imageUrl,
      String categoryId,
      String targetUrl,
      bool enabled,
      String ctaBtnText});
}

/// @nodoc
class __$$_PromotionCopyWithImpl<$Res>
    extends _$PromotionCopyWithImpl<$Res, _$_Promotion>
    implements _$$_PromotionCopyWith<$Res> {
  __$$_PromotionCopyWithImpl(
      _$_Promotion _value, $Res Function(_$_Promotion) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? subsections = null,
    Object? validFrom = freezed,
    Object? validTo = freezed,
    Object? createdAt = freezed,
    Object? imageUrl = null,
    Object? categoryId = null,
    Object? targetUrl = null,
    Object? enabled = null,
    Object? ctaBtnText = null,
  }) {
    return _then(_$_Promotion(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      subsections: null == subsections
          ? _value._subsections
          : subsections // ignore: cast_nullable_to_non_nullable
              as List<PromotionSubsection>,
      validFrom: freezed == validFrom
          ? _value.validFrom
          : validFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      validTo: freezed == validTo
          ? _value.validTo
          : validTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      ctaBtnText: null == ctaBtnText
          ? _value.ctaBtnText
          : ctaBtnText // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_Promotion implements _Promotion {
  _$_Promotion(
      {required this.id,
      required this.title,
      required this.description,
      required final List<PromotionSubsection> subsections,
      required this.validFrom,
      required this.validTo,
      required this.createdAt,
      required this.imageUrl,
      required this.categoryId,
      required this.targetUrl,
      required this.enabled,
      required this.ctaBtnText})
      : _subsections = subsections;

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  final List<PromotionSubsection> _subsections;
  @override
  List<PromotionSubsection> get subsections {
    if (_subsections is EqualUnmodifiableListView) return _subsections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subsections);
  }

  @override
  final DateTime? validFrom;
  @override
  final DateTime? validTo;
  @override
  final DateTime? createdAt;
  @override
  final String imageUrl;
  @override
  final String categoryId;
  @override
  final String targetUrl;
  @override
  final bool enabled;
  @override
  final String ctaBtnText;

  @override
  String toString() {
    return 'Promotion(id: $id, title: $title, description: $description, subsections: $subsections, validFrom: $validFrom, validTo: $validTo, createdAt: $createdAt, imageUrl: $imageUrl, categoryId: $categoryId, targetUrl: $targetUrl, enabled: $enabled, ctaBtnText: $ctaBtnText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Promotion &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality()
                .equals(other._subsections, _subsections) &&
            (identical(other.validFrom, validFrom) ||
                other.validFrom == validFrom) &&
            (identical(other.validTo, validTo) || other.validTo == validTo) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.targetUrl, targetUrl) ||
                other.targetUrl == targetUrl) &&
            (identical(other.enabled, enabled) || other.enabled == enabled) &&
            (identical(other.ctaBtnText, ctaBtnText) ||
                other.ctaBtnText == ctaBtnText));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      const DeepCollectionEquality().hash(_subsections),
      validFrom,
      validTo,
      createdAt,
      imageUrl,
      categoryId,
      targetUrl,
      enabled,
      ctaBtnText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PromotionCopyWith<_$_Promotion> get copyWith =>
      __$$_PromotionCopyWithImpl<_$_Promotion>(this, _$identity);
}

abstract class _Promotion implements Promotion {
  factory _Promotion(
      {required final String id,
      required final String title,
      required final String description,
      required final List<PromotionSubsection> subsections,
      required final DateTime? validFrom,
      required final DateTime? validTo,
      required final DateTime? createdAt,
      required final String imageUrl,
      required final String categoryId,
      required final String targetUrl,
      required final bool enabled,
      required final String ctaBtnText}) = _$_Promotion;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  List<PromotionSubsection> get subsections;
  @override
  DateTime? get validFrom;
  @override
  DateTime? get validTo;
  @override
  DateTime? get createdAt;
  @override
  String get imageUrl;
  @override
  String get categoryId;
  @override
  String get targetUrl;
  @override
  bool get enabled;
  @override
  String get ctaBtnText;
  @override
  @JsonKey(ignore: true)
  _$$_PromotionCopyWith<_$_Promotion> get copyWith =>
      throw _privateConstructorUsedError;
}
