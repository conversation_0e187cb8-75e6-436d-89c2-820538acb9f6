// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'promotion_list.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$PromotionList {
  int get pageSize => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  int get totalCount => throw _privateConstructorUsedError;
  List<PromotionHeader> get items => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PromotionListCopyWith<PromotionList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PromotionListCopyWith<$Res> {
  factory $PromotionListCopyWith(
          PromotionList value, $Res Function(PromotionList) then) =
      _$PromotionListCopyWithImpl<$Res, PromotionList>;
  @useResult
  $Res call(
      {int pageSize, int page, int totalCount, List<PromotionHeader> items});
}

/// @nodoc
class _$PromotionListCopyWithImpl<$Res, $Val extends PromotionList>
    implements $PromotionListCopyWith<$Res> {
  _$PromotionListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
    Object? page = null,
    Object? totalCount = null,
    Object? items = null,
  }) {
    return _then(_value.copyWith(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PromotionHeader>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PromotionListCopyWith<$Res>
    implements $PromotionListCopyWith<$Res> {
  factory _$$_PromotionListCopyWith(
          _$_PromotionList value, $Res Function(_$_PromotionList) then) =
      __$$_PromotionListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int pageSize, int page, int totalCount, List<PromotionHeader> items});
}

/// @nodoc
class __$$_PromotionListCopyWithImpl<$Res>
    extends _$PromotionListCopyWithImpl<$Res, _$_PromotionList>
    implements _$$_PromotionListCopyWith<$Res> {
  __$$_PromotionListCopyWithImpl(
      _$_PromotionList _value, $Res Function(_$_PromotionList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
    Object? page = null,
    Object? totalCount = null,
    Object? items = null,
  }) {
    return _then(_$_PromotionList(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PromotionHeader>,
    ));
  }
}

/// @nodoc

class _$_PromotionList implements _PromotionList {
  _$_PromotionList(
      {this.pageSize = 10,
      this.page = 1,
      this.totalCount = 0,
      final List<PromotionHeader> items = const <PromotionHeader>[]})
      : _items = items;

  @override
  @JsonKey()
  final int pageSize;
  @override
  @JsonKey()
  final int page;
  @override
  @JsonKey()
  final int totalCount;
  final List<PromotionHeader> _items;
  @override
  @JsonKey()
  List<PromotionHeader> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'PromotionList(pageSize: $pageSize, page: $page, totalCount: $totalCount, items: $items)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PromotionList &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pageSize, page, totalCount,
      const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PromotionListCopyWith<_$_PromotionList> get copyWith =>
      __$$_PromotionListCopyWithImpl<_$_PromotionList>(this, _$identity);
}

abstract class _PromotionList implements PromotionList {
  factory _PromotionList(
      {final int pageSize,
      final int page,
      final int totalCount,
      final List<PromotionHeader> items}) = _$_PromotionList;

  @override
  int get pageSize;
  @override
  int get page;
  @override
  int get totalCount;
  @override
  List<PromotionHeader> get items;
  @override
  @JsonKey(ignore: true)
  _$$_PromotionListCopyWith<_$_PromotionList> get copyWith =>
      throw _privateConstructorUsedError;
}
