// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'qwarta_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$QwartaFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedQwartaFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(QwartaNoContent value) noContent,
    required TResult Function(QwartaTooManyRequests value) tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedQwartaFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(QwartaNoContent value)? noContent,
    TResult? Function(QwartaTooManyRequests value)? tooManyRequests,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedQwartaFailure value)?
        unexpectedInstallmentFailure,
    TResult Function(QwartaNoContent value)? noContent,
    TResult Function(QwartaTooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QwartaFailureCopyWith<$Res> {
  factory $QwartaFailureCopyWith(
          QwartaFailure value, $Res Function(QwartaFailure) then) =
      _$QwartaFailureCopyWithImpl<$Res, QwartaFailure>;
}

/// @nodoc
class _$QwartaFailureCopyWithImpl<$Res, $Val extends QwartaFailure>
    implements $QwartaFailureCopyWith<$Res> {
  _$QwartaFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$UnexpectedQwartaFailureCopyWith<$Res> {
  factory _$$UnexpectedQwartaFailureCopyWith(_$UnexpectedQwartaFailure value,
          $Res Function(_$UnexpectedQwartaFailure) then) =
      __$$UnexpectedQwartaFailureCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedQwartaFailureCopyWithImpl<$Res>
    extends _$QwartaFailureCopyWithImpl<$Res, _$UnexpectedQwartaFailure>
    implements _$$UnexpectedQwartaFailureCopyWith<$Res> {
  __$$UnexpectedQwartaFailureCopyWithImpl(_$UnexpectedQwartaFailure _value,
      $Res Function(_$UnexpectedQwartaFailure) _then)
      : super(_value, _then);
}

/// @nodoc

class _$UnexpectedQwartaFailure extends UnexpectedQwartaFailure {
  const _$UnexpectedQwartaFailure() : super._();

  @override
  String toString() {
    return 'QwartaFailure.unexpectedInstallmentFailure()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnexpectedQwartaFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return unexpectedInstallmentFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return unexpectedInstallmentFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (unexpectedInstallmentFailure != null) {
      return unexpectedInstallmentFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedQwartaFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(QwartaNoContent value) noContent,
    required TResult Function(QwartaTooManyRequests value) tooManyRequests,
  }) {
    return unexpectedInstallmentFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedQwartaFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(QwartaNoContent value)? noContent,
    TResult? Function(QwartaTooManyRequests value)? tooManyRequests,
  }) {
    return unexpectedInstallmentFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedQwartaFailure value)?
        unexpectedInstallmentFailure,
    TResult Function(QwartaNoContent value)? noContent,
    TResult Function(QwartaTooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (unexpectedInstallmentFailure != null) {
      return unexpectedInstallmentFailure(this);
    }
    return orElse();
  }
}

abstract class UnexpectedQwartaFailure extends QwartaFailure {
  const factory UnexpectedQwartaFailure() = _$UnexpectedQwartaFailure;
  const UnexpectedQwartaFailure._() : super._();
}

/// @nodoc
abstract class _$$QwartaNoContentCopyWith<$Res> {
  factory _$$QwartaNoContentCopyWith(
          _$QwartaNoContent value, $Res Function(_$QwartaNoContent) then) =
      __$$QwartaNoContentCopyWithImpl<$Res>;
}

/// @nodoc
class __$$QwartaNoContentCopyWithImpl<$Res>
    extends _$QwartaFailureCopyWithImpl<$Res, _$QwartaNoContent>
    implements _$$QwartaNoContentCopyWith<$Res> {
  __$$QwartaNoContentCopyWithImpl(
      _$QwartaNoContent _value, $Res Function(_$QwartaNoContent) _then)
      : super(_value, _then);
}

/// @nodoc

class _$QwartaNoContent extends QwartaNoContent {
  const _$QwartaNoContent() : super._();

  @override
  String toString() {
    return 'QwartaFailure.noContent()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$QwartaNoContent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return noContent();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return noContent?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (noContent != null) {
      return noContent();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedQwartaFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(QwartaNoContent value) noContent,
    required TResult Function(QwartaTooManyRequests value) tooManyRequests,
  }) {
    return noContent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedQwartaFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(QwartaNoContent value)? noContent,
    TResult? Function(QwartaTooManyRequests value)? tooManyRequests,
  }) {
    return noContent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedQwartaFailure value)?
        unexpectedInstallmentFailure,
    TResult Function(QwartaNoContent value)? noContent,
    TResult Function(QwartaTooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (noContent != null) {
      return noContent(this);
    }
    return orElse();
  }
}

abstract class QwartaNoContent extends QwartaFailure {
  const factory QwartaNoContent() = _$QwartaNoContent;
  const QwartaNoContent._() : super._();
}

/// @nodoc
abstract class _$$QwartaTooManyRequestsCopyWith<$Res> {
  factory _$$QwartaTooManyRequestsCopyWith(_$QwartaTooManyRequests value,
          $Res Function(_$QwartaTooManyRequests) then) =
      __$$QwartaTooManyRequestsCopyWithImpl<$Res>;
}

/// @nodoc
class __$$QwartaTooManyRequestsCopyWithImpl<$Res>
    extends _$QwartaFailureCopyWithImpl<$Res, _$QwartaTooManyRequests>
    implements _$$QwartaTooManyRequestsCopyWith<$Res> {
  __$$QwartaTooManyRequestsCopyWithImpl(_$QwartaTooManyRequests _value,
      $Res Function(_$QwartaTooManyRequests) _then)
      : super(_value, _then);
}

/// @nodoc

class _$QwartaTooManyRequests extends QwartaTooManyRequests {
  const _$QwartaTooManyRequests() : super._();

  @override
  String toString() {
    return 'QwartaFailure.tooManyRequests()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$QwartaTooManyRequests);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unexpectedInstallmentFailure,
    required TResult Function() noContent,
    required TResult Function() tooManyRequests,
  }) {
    return tooManyRequests();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unexpectedInstallmentFailure,
    TResult? Function()? noContent,
    TResult? Function()? tooManyRequests,
  }) {
    return tooManyRequests?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unexpectedInstallmentFailure,
    TResult Function()? noContent,
    TResult Function()? tooManyRequests,
    required TResult orElse(),
  }) {
    if (tooManyRequests != null) {
      return tooManyRequests();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UnexpectedQwartaFailure value)
        unexpectedInstallmentFailure,
    required TResult Function(QwartaNoContent value) noContent,
    required TResult Function(QwartaTooManyRequests value) tooManyRequests,
  }) {
    return tooManyRequests(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UnexpectedQwartaFailure value)?
        unexpectedInstallmentFailure,
    TResult? Function(QwartaNoContent value)? noContent,
    TResult? Function(QwartaTooManyRequests value)? tooManyRequests,
  }) {
    return tooManyRequests?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UnexpectedQwartaFailure value)?
        unexpectedInstallmentFailure,
    TResult Function(QwartaNoContent value)? noContent,
    TResult Function(QwartaTooManyRequests value)? tooManyRequests,
    required TResult orElse(),
  }) {
    if (tooManyRequests != null) {
      return tooManyRequests(this);
    }
    return orElse();
  }
}

abstract class QwartaTooManyRequests extends QwartaFailure {
  const factory QwartaTooManyRequests() = _$QwartaTooManyRequests;
  const QwartaTooManyRequests._() : super._();
}
