// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'qwarta_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$QwartaOverview {
  Decimal? get actualSpend => throw _privateConstructorUsedError;
  Decimal? get available => throw _privateConstructorUsedError;
  Decimal? get availableMonthlyInstallmentLimit =>
      throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  ContractStatus? get status => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $QwartaOverviewCopyWith<QwartaOverview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QwartaOverviewCopyWith<$Res> {
  factory $QwartaOverviewCopyWith(
          QwartaOverview value, $Res Function(QwartaOverview) then) =
      _$QwartaOverviewCopyWithImpl<$Res, QwartaOverview>;
  @useResult
  $Res call(
      {Decimal? actualSpend,
      Decimal? available,
      Decimal? availableMonthlyInstallmentLimit,
      String? currency,
      ContractStatus? status});
}

/// @nodoc
class _$QwartaOverviewCopyWithImpl<$Res, $Val extends QwartaOverview>
    implements $QwartaOverviewCopyWith<$Res> {
  _$QwartaOverviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? actualSpend = freezed,
    Object? available = freezed,
    Object? availableMonthlyInstallmentLimit = freezed,
    Object? currency = freezed,
    Object? status = freezed,
  }) {
    return _then(_value.copyWith(
      actualSpend: freezed == actualSpend
          ? _value.actualSpend
          : actualSpend // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      available: freezed == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      availableMonthlyInstallmentLimit: freezed ==
              availableMonthlyInstallmentLimit
          ? _value.availableMonthlyInstallmentLimit
          : availableMonthlyInstallmentLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ContractStatus?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_QwartaOverviewCopyWith<$Res>
    implements $QwartaOverviewCopyWith<$Res> {
  factory _$$_QwartaOverviewCopyWith(
          _$_QwartaOverview value, $Res Function(_$_QwartaOverview) then) =
      __$$_QwartaOverviewCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Decimal? actualSpend,
      Decimal? available,
      Decimal? availableMonthlyInstallmentLimit,
      String? currency,
      ContractStatus? status});
}

/// @nodoc
class __$$_QwartaOverviewCopyWithImpl<$Res>
    extends _$QwartaOverviewCopyWithImpl<$Res, _$_QwartaOverview>
    implements _$$_QwartaOverviewCopyWith<$Res> {
  __$$_QwartaOverviewCopyWithImpl(
      _$_QwartaOverview _value, $Res Function(_$_QwartaOverview) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? actualSpend = freezed,
    Object? available = freezed,
    Object? availableMonthlyInstallmentLimit = freezed,
    Object? currency = freezed,
    Object? status = freezed,
  }) {
    return _then(_$_QwartaOverview(
      actualSpend: freezed == actualSpend
          ? _value.actualSpend
          : actualSpend // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      available: freezed == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      availableMonthlyInstallmentLimit: freezed ==
              availableMonthlyInstallmentLimit
          ? _value.availableMonthlyInstallmentLimit
          : availableMonthlyInstallmentLimit // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ContractStatus?,
    ));
  }
}

/// @nodoc

class _$_QwartaOverview implements _QwartaOverview {
  _$_QwartaOverview(
      {this.actualSpend,
      this.available,
      this.availableMonthlyInstallmentLimit,
      this.currency,
      this.status});

  @override
  final Decimal? actualSpend;
  @override
  final Decimal? available;
  @override
  final Decimal? availableMonthlyInstallmentLimit;
  @override
  final String? currency;
  @override
  final ContractStatus? status;

  @override
  String toString() {
    return 'QwartaOverview(actualSpend: $actualSpend, available: $available, availableMonthlyInstallmentLimit: $availableMonthlyInstallmentLimit, currency: $currency, status: $status)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_QwartaOverview &&
            (identical(other.actualSpend, actualSpend) ||
                other.actualSpend == actualSpend) &&
            (identical(other.available, available) ||
                other.available == available) &&
            (identical(other.availableMonthlyInstallmentLimit,
                    availableMonthlyInstallmentLimit) ||
                other.availableMonthlyInstallmentLimit ==
                    availableMonthlyInstallmentLimit) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, actualSpend, available,
      availableMonthlyInstallmentLimit, currency, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_QwartaOverviewCopyWith<_$_QwartaOverview> get copyWith =>
      __$$_QwartaOverviewCopyWithImpl<_$_QwartaOverview>(this, _$identity);
}

abstract class _QwartaOverview implements QwartaOverview {
  factory _QwartaOverview(
      {final Decimal? actualSpend,
      final Decimal? available,
      final Decimal? availableMonthlyInstallmentLimit,
      final String? currency,
      final ContractStatus? status}) = _$_QwartaOverview;

  @override
  Decimal? get actualSpend;
  @override
  Decimal? get available;
  @override
  Decimal? get availableMonthlyInstallmentLimit;
  @override
  String? get currency;
  @override
  ContractStatus? get status;
  @override
  @JsonKey(ignore: true)
  _$$_QwartaOverviewCopyWith<_$_QwartaOverview> get copyWith =>
      throw _privateConstructorUsedError;
}
