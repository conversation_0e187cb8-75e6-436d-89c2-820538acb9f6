// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'survey.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$Survey {
  String get surveyId => throw _privateConstructorUsedError;
  String get surveyCategoryId => throw _privateConstructorUsedError;
  String get surveySessionId => throw _privateConstructorUsedError;
  String get surveyProductId => throw _privateConstructorUsedError;
  String get surveyTitle => throw _privateConstructorUsedError;
  String get openingIllustrationUrl => throw _privateConstructorUsedError;
  String get openingTitle => throw _privateConstructorUsedError;
  String get openingText => throw _privateConstructorUsedError;
  String get closingIllustrationUrl => throw _privateConstructorUsedError;
  String get closingTitle => throw _privateConstructorUsedError;
  String get closingText => throw _privateConstructorUsedError;
  List<SurveyQuestion> get questions => throw _privateConstructorUsedError;
  int get currentQuestionId => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SurveyCopyWith<Survey> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SurveyCopyWith<$Res> {
  factory $SurveyCopyWith(Survey value, $Res Function(Survey) then) =
      _$SurveyCopyWithImpl<$Res, Survey>;
  @useResult
  $Res call(
      {String surveyId,
      String surveyCategoryId,
      String surveySessionId,
      String surveyProductId,
      String surveyTitle,
      String openingIllustrationUrl,
      String openingTitle,
      String openingText,
      String closingIllustrationUrl,
      String closingTitle,
      String closingText,
      List<SurveyQuestion> questions,
      int currentQuestionId});
}

/// @nodoc
class _$SurveyCopyWithImpl<$Res, $Val extends Survey>
    implements $SurveyCopyWith<$Res> {
  _$SurveyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? surveyId = null,
    Object? surveyCategoryId = null,
    Object? surveySessionId = null,
    Object? surveyProductId = null,
    Object? surveyTitle = null,
    Object? openingIllustrationUrl = null,
    Object? openingTitle = null,
    Object? openingText = null,
    Object? closingIllustrationUrl = null,
    Object? closingTitle = null,
    Object? closingText = null,
    Object? questions = null,
    Object? currentQuestionId = null,
  }) {
    return _then(_value.copyWith(
      surveyId: null == surveyId
          ? _value.surveyId
          : surveyId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyCategoryId: null == surveyCategoryId
          ? _value.surveyCategoryId
          : surveyCategoryId // ignore: cast_nullable_to_non_nullable
              as String,
      surveySessionId: null == surveySessionId
          ? _value.surveySessionId
          : surveySessionId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyProductId: null == surveyProductId
          ? _value.surveyProductId
          : surveyProductId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyTitle: null == surveyTitle
          ? _value.surveyTitle
          : surveyTitle // ignore: cast_nullable_to_non_nullable
              as String,
      openingIllustrationUrl: null == openingIllustrationUrl
          ? _value.openingIllustrationUrl
          : openingIllustrationUrl // ignore: cast_nullable_to_non_nullable
              as String,
      openingTitle: null == openingTitle
          ? _value.openingTitle
          : openingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      openingText: null == openingText
          ? _value.openingText
          : openingText // ignore: cast_nullable_to_non_nullable
              as String,
      closingIllustrationUrl: null == closingIllustrationUrl
          ? _value.closingIllustrationUrl
          : closingIllustrationUrl // ignore: cast_nullable_to_non_nullable
              as String,
      closingTitle: null == closingTitle
          ? _value.closingTitle
          : closingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      closingText: null == closingText
          ? _value.closingText
          : closingText // ignore: cast_nullable_to_non_nullable
              as String,
      questions: null == questions
          ? _value.questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<SurveyQuestion>,
      currentQuestionId: null == currentQuestionId
          ? _value.currentQuestionId
          : currentQuestionId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SurveyCopyWith<$Res> implements $SurveyCopyWith<$Res> {
  factory _$$_SurveyCopyWith(_$_Survey value, $Res Function(_$_Survey) then) =
      __$$_SurveyCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String surveyId,
      String surveyCategoryId,
      String surveySessionId,
      String surveyProductId,
      String surveyTitle,
      String openingIllustrationUrl,
      String openingTitle,
      String openingText,
      String closingIllustrationUrl,
      String closingTitle,
      String closingText,
      List<SurveyQuestion> questions,
      int currentQuestionId});
}

/// @nodoc
class __$$_SurveyCopyWithImpl<$Res>
    extends _$SurveyCopyWithImpl<$Res, _$_Survey>
    implements _$$_SurveyCopyWith<$Res> {
  __$$_SurveyCopyWithImpl(_$_Survey _value, $Res Function(_$_Survey) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? surveyId = null,
    Object? surveyCategoryId = null,
    Object? surveySessionId = null,
    Object? surveyProductId = null,
    Object? surveyTitle = null,
    Object? openingIllustrationUrl = null,
    Object? openingTitle = null,
    Object? openingText = null,
    Object? closingIllustrationUrl = null,
    Object? closingTitle = null,
    Object? closingText = null,
    Object? questions = null,
    Object? currentQuestionId = null,
  }) {
    return _then(_$_Survey(
      surveyId: null == surveyId
          ? _value.surveyId
          : surveyId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyCategoryId: null == surveyCategoryId
          ? _value.surveyCategoryId
          : surveyCategoryId // ignore: cast_nullable_to_non_nullable
              as String,
      surveySessionId: null == surveySessionId
          ? _value.surveySessionId
          : surveySessionId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyProductId: null == surveyProductId
          ? _value.surveyProductId
          : surveyProductId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyTitle: null == surveyTitle
          ? _value.surveyTitle
          : surveyTitle // ignore: cast_nullable_to_non_nullable
              as String,
      openingIllustrationUrl: null == openingIllustrationUrl
          ? _value.openingIllustrationUrl
          : openingIllustrationUrl // ignore: cast_nullable_to_non_nullable
              as String,
      openingTitle: null == openingTitle
          ? _value.openingTitle
          : openingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      openingText: null == openingText
          ? _value.openingText
          : openingText // ignore: cast_nullable_to_non_nullable
              as String,
      closingIllustrationUrl: null == closingIllustrationUrl
          ? _value.closingIllustrationUrl
          : closingIllustrationUrl // ignore: cast_nullable_to_non_nullable
              as String,
      closingTitle: null == closingTitle
          ? _value.closingTitle
          : closingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      closingText: null == closingText
          ? _value.closingText
          : closingText // ignore: cast_nullable_to_non_nullable
              as String,
      questions: null == questions
          ? _value._questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<SurveyQuestion>,
      currentQuestionId: null == currentQuestionId
          ? _value.currentQuestionId
          : currentQuestionId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$_Survey implements _Survey {
  _$_Survey(
      {required this.surveyId,
      required this.surveyCategoryId,
      required this.surveySessionId,
      required this.surveyProductId,
      required this.surveyTitle,
      required this.openingIllustrationUrl,
      required this.openingTitle,
      required this.openingText,
      required this.closingIllustrationUrl,
      required this.closingTitle,
      required this.closingText,
      required final List<SurveyQuestion> questions,
      required this.currentQuestionId})
      : _questions = questions;

  @override
  final String surveyId;
  @override
  final String surveyCategoryId;
  @override
  final String surveySessionId;
  @override
  final String surveyProductId;
  @override
  final String surveyTitle;
  @override
  final String openingIllustrationUrl;
  @override
  final String openingTitle;
  @override
  final String openingText;
  @override
  final String closingIllustrationUrl;
  @override
  final String closingTitle;
  @override
  final String closingText;
  final List<SurveyQuestion> _questions;
  @override
  List<SurveyQuestion> get questions {
    if (_questions is EqualUnmodifiableListView) return _questions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_questions);
  }

  @override
  final int currentQuestionId;

  @override
  String toString() {
    return 'Survey(surveyId: $surveyId, surveyCategoryId: $surveyCategoryId, surveySessionId: $surveySessionId, surveyProductId: $surveyProductId, surveyTitle: $surveyTitle, openingIllustrationUrl: $openingIllustrationUrl, openingTitle: $openingTitle, openingText: $openingText, closingIllustrationUrl: $closingIllustrationUrl, closingTitle: $closingTitle, closingText: $closingText, questions: $questions, currentQuestionId: $currentQuestionId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Survey &&
            (identical(other.surveyId, surveyId) ||
                other.surveyId == surveyId) &&
            (identical(other.surveyCategoryId, surveyCategoryId) ||
                other.surveyCategoryId == surveyCategoryId) &&
            (identical(other.surveySessionId, surveySessionId) ||
                other.surveySessionId == surveySessionId) &&
            (identical(other.surveyProductId, surveyProductId) ||
                other.surveyProductId == surveyProductId) &&
            (identical(other.surveyTitle, surveyTitle) ||
                other.surveyTitle == surveyTitle) &&
            (identical(other.openingIllustrationUrl, openingIllustrationUrl) ||
                other.openingIllustrationUrl == openingIllustrationUrl) &&
            (identical(other.openingTitle, openingTitle) ||
                other.openingTitle == openingTitle) &&
            (identical(other.openingText, openingText) ||
                other.openingText == openingText) &&
            (identical(other.closingIllustrationUrl, closingIllustrationUrl) ||
                other.closingIllustrationUrl == closingIllustrationUrl) &&
            (identical(other.closingTitle, closingTitle) ||
                other.closingTitle == closingTitle) &&
            (identical(other.closingText, closingText) ||
                other.closingText == closingText) &&
            const DeepCollectionEquality()
                .equals(other._questions, _questions) &&
            (identical(other.currentQuestionId, currentQuestionId) ||
                other.currentQuestionId == currentQuestionId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      surveyId,
      surveyCategoryId,
      surveySessionId,
      surveyProductId,
      surveyTitle,
      openingIllustrationUrl,
      openingTitle,
      openingText,
      closingIllustrationUrl,
      closingTitle,
      closingText,
      const DeepCollectionEquality().hash(_questions),
      currentQuestionId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SurveyCopyWith<_$_Survey> get copyWith =>
      __$$_SurveyCopyWithImpl<_$_Survey>(this, _$identity);
}

abstract class _Survey implements Survey {
  factory _Survey(
      {required final String surveyId,
      required final String surveyCategoryId,
      required final String surveySessionId,
      required final String surveyProductId,
      required final String surveyTitle,
      required final String openingIllustrationUrl,
      required final String openingTitle,
      required final String openingText,
      required final String closingIllustrationUrl,
      required final String closingTitle,
      required final String closingText,
      required final List<SurveyQuestion> questions,
      required final int currentQuestionId}) = _$_Survey;

  @override
  String get surveyId;
  @override
  String get surveyCategoryId;
  @override
  String get surveySessionId;
  @override
  String get surveyProductId;
  @override
  String get surveyTitle;
  @override
  String get openingIllustrationUrl;
  @override
  String get openingTitle;
  @override
  String get openingText;
  @override
  String get closingIllustrationUrl;
  @override
  String get closingTitle;
  @override
  String get closingText;
  @override
  List<SurveyQuestion> get questions;
  @override
  int get currentQuestionId;
  @override
  @JsonKey(ignore: true)
  _$$_SurveyCopyWith<_$_Survey> get copyWith =>
      throw _privateConstructorUsedError;
}
