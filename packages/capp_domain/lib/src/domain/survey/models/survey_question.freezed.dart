// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'survey_question.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$SurveyQuestion {
  int get id => throw _privateConstructorUsedError;
  String get questionText => throw _privateConstructorUsedError;
  SurveyQuestionType get questionType => throw _privateConstructorUsedError;
  int get order => throw _privateConstructorUsedError;
  List<SurveyAnswer>? get options => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SurveyQuestionCopyWith<SurveyQuestion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SurveyQuestionCopyWith<$Res> {
  factory $SurveyQuestionCopyWith(
          SurveyQuestion value, $Res Function(SurveyQuestion) then) =
      _$SurveyQuestionCopyWithImpl<$Res, SurveyQuestion>;
  @useResult
  $Res call(
      {int id,
      String questionText,
      SurveyQuestionType questionType,
      int order,
      List<SurveyAnswer>? options});
}

/// @nodoc
class _$SurveyQuestionCopyWithImpl<$Res, $Val extends SurveyQuestion>
    implements $SurveyQuestionCopyWith<$Res> {
  _$SurveyQuestionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? questionText = null,
    Object? questionType = null,
    Object? order = null,
    Object? options = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      questionText: null == questionText
          ? _value.questionText
          : questionText // ignore: cast_nullable_to_non_nullable
              as String,
      questionType: null == questionType
          ? _value.questionType
          : questionType // ignore: cast_nullable_to_non_nullable
              as SurveyQuestionType,
      order: null == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int,
      options: freezed == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<SurveyAnswer>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SurveyQuestionCopyWith<$Res>
    implements $SurveyQuestionCopyWith<$Res> {
  factory _$$_SurveyQuestionCopyWith(
          _$_SurveyQuestion value, $Res Function(_$_SurveyQuestion) then) =
      __$$_SurveyQuestionCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String questionText,
      SurveyQuestionType questionType,
      int order,
      List<SurveyAnswer>? options});
}

/// @nodoc
class __$$_SurveyQuestionCopyWithImpl<$Res>
    extends _$SurveyQuestionCopyWithImpl<$Res, _$_SurveyQuestion>
    implements _$$_SurveyQuestionCopyWith<$Res> {
  __$$_SurveyQuestionCopyWithImpl(
      _$_SurveyQuestion _value, $Res Function(_$_SurveyQuestion) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? questionText = null,
    Object? questionType = null,
    Object? order = null,
    Object? options = freezed,
  }) {
    return _then(_$_SurveyQuestion(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      questionText: null == questionText
          ? _value.questionText
          : questionText // ignore: cast_nullable_to_non_nullable
              as String,
      questionType: null == questionType
          ? _value.questionType
          : questionType // ignore: cast_nullable_to_non_nullable
              as SurveyQuestionType,
      order: null == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int,
      options: freezed == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<SurveyAnswer>?,
    ));
  }
}

/// @nodoc

class _$_SurveyQuestion implements _SurveyQuestion {
  _$_SurveyQuestion(
      {required this.id,
      required this.questionText,
      required this.questionType,
      required this.order,
      required final List<SurveyAnswer>? options})
      : _options = options;

  @override
  final int id;
  @override
  final String questionText;
  @override
  final SurveyQuestionType questionType;
  @override
  final int order;
  final List<SurveyAnswer>? _options;
  @override
  List<SurveyAnswer>? get options {
    final value = _options;
    if (value == null) return null;
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SurveyQuestion(id: $id, questionText: $questionText, questionType: $questionType, order: $order, options: $options)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SurveyQuestion &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.questionText, questionText) ||
                other.questionText == questionText) &&
            (identical(other.questionType, questionType) ||
                other.questionType == questionType) &&
            (identical(other.order, order) || other.order == order) &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, questionText, questionType,
      order, const DeepCollectionEquality().hash(_options));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SurveyQuestionCopyWith<_$_SurveyQuestion> get copyWith =>
      __$$_SurveyQuestionCopyWithImpl<_$_SurveyQuestion>(this, _$identity);
}

abstract class _SurveyQuestion implements SurveyQuestion {
  factory _SurveyQuestion(
      {required final int id,
      required final String questionText,
      required final SurveyQuestionType questionType,
      required final int order,
      required final List<SurveyAnswer>? options}) = _$_SurveyQuestion;

  @override
  int get id;
  @override
  String get questionText;
  @override
  SurveyQuestionType get questionType;
  @override
  int get order;
  @override
  List<SurveyAnswer>? get options;
  @override
  @JsonKey(ignore: true)
  _$$_SurveyQuestionCopyWith<_$_SurveyQuestion> get copyWith =>
      throw _privateConstructorUsedError;
}
