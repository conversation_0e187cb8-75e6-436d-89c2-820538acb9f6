// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workflow_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$WorkflowFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkflowFailureCopyWith<$Res> {
  factory $WorkflowFailureCopyWith(
          WorkflowFailure value, $Res Function(WorkflowFailure) then) =
      _$WorkflowFailureCopyWithImpl<$Res, WorkflowFailure>;
}

/// @nodoc
class _$WorkflowFailureCopyWithImpl<$Res, $Val extends WorkflowFailure>
    implements $WorkflowFailureCopyWith<$Res> {
  _$WorkflowFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_UnauthenticatedCopyWith<$Res> {
  factory _$$_UnauthenticatedCopyWith(
          _$_Unauthenticated value, $Res Function(_$_Unauthenticated) then) =
      __$$_UnauthenticatedCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_UnauthenticatedCopyWithImpl<$Res>
    extends _$WorkflowFailureCopyWithImpl<$Res, _$_Unauthenticated>
    implements _$$_UnauthenticatedCopyWith<$Res> {
  __$$_UnauthenticatedCopyWithImpl(
      _$_Unauthenticated _value, $Res Function(_$_Unauthenticated) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Unauthenticated implements _Unauthenticated {
  const _$_Unauthenticated();

  @override
  String toString() {
    return 'WorkflowFailure.unauthenticated()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Unauthenticated);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _Unauthenticated implements WorkflowFailure {
  const factory _Unauthenticated() = _$_Unauthenticated;
}

/// @nodoc
abstract class _$$_DifferentPhaseCopyWith<$Res> {
  factory _$$_DifferentPhaseCopyWith(
          _$_DifferentPhase value, $Res Function(_$_DifferentPhase) then) =
      __$$_DifferentPhaseCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_DifferentPhaseCopyWithImpl<$Res>
    extends _$WorkflowFailureCopyWithImpl<$Res, _$_DifferentPhase>
    implements _$$_DifferentPhaseCopyWith<$Res> {
  __$$_DifferentPhaseCopyWithImpl(
      _$_DifferentPhase _value, $Res Function(_$_DifferentPhase) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_DifferentPhase implements _DifferentPhase {
  const _$_DifferentPhase();

  @override
  String toString() {
    return 'WorkflowFailure.differentPhase()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_DifferentPhase);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) {
    return differentPhase();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) {
    return differentPhase?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) {
    if (differentPhase != null) {
      return differentPhase();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) {
    return differentPhase(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) {
    return differentPhase?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) {
    if (differentPhase != null) {
      return differentPhase(this);
    }
    return orElse();
  }
}

abstract class _DifferentPhase implements WorkflowFailure {
  const factory _DifferentPhase() = _$_DifferentPhase;
}

/// @nodoc
abstract class _$$UnexpectedFlowResponseCopyWith<$Res> {
  factory _$$UnexpectedFlowResponseCopyWith(_$UnexpectedFlowResponse value,
          $Res Function(_$UnexpectedFlowResponse) then) =
      __$$UnexpectedFlowResponseCopyWithImpl<$Res>;
  @useResult
  $Res call({String? traceparent});
}

/// @nodoc
class __$$UnexpectedFlowResponseCopyWithImpl<$Res>
    extends _$WorkflowFailureCopyWithImpl<$Res, _$UnexpectedFlowResponse>
    implements _$$UnexpectedFlowResponseCopyWith<$Res> {
  __$$UnexpectedFlowResponseCopyWithImpl(_$UnexpectedFlowResponse _value,
      $Res Function(_$UnexpectedFlowResponse) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? traceparent = freezed,
  }) {
    return _then(_$UnexpectedFlowResponse(
      traceparent: freezed == traceparent
          ? _value.traceparent
          : traceparent // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$UnexpectedFlowResponse implements UnexpectedFlowResponse {
  const _$UnexpectedFlowResponse({this.traceparent});

  @override
  final String? traceparent;

  @override
  String toString() {
    return 'WorkflowFailure.unexpected(traceparent: $traceparent)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnexpectedFlowResponse &&
            (identical(other.traceparent, traceparent) ||
                other.traceparent == traceparent));
  }

  @override
  int get hashCode => Object.hash(runtimeType, traceparent);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UnexpectedFlowResponseCopyWith<_$UnexpectedFlowResponse> get copyWith =>
      __$$UnexpectedFlowResponseCopyWithImpl<_$UnexpectedFlowResponse>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) {
    return unexpected(traceparent);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) {
    return unexpected?.call(traceparent);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(traceparent);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class UnexpectedFlowResponse implements WorkflowFailure {
  const factory UnexpectedFlowResponse({final String? traceparent}) =
      _$UnexpectedFlowResponse;

  String? get traceparent;
  @JsonKey(ignore: true)
  _$$UnexpectedFlowResponseCopyWith<_$UnexpectedFlowResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_NetworkIssueCopyWith<$Res> {
  factory _$$_NetworkIssueCopyWith(
          _$_NetworkIssue value, $Res Function(_$_NetworkIssue) then) =
      __$$_NetworkIssueCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_NetworkIssueCopyWithImpl<$Res>
    extends _$WorkflowFailureCopyWithImpl<$Res, _$_NetworkIssue>
    implements _$$_NetworkIssueCopyWith<$Res> {
  __$$_NetworkIssueCopyWithImpl(
      _$_NetworkIssue _value, $Res Function(_$_NetworkIssue) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_NetworkIssue implements _NetworkIssue {
  const _$_NetworkIssue();

  @override
  String toString() {
    return 'WorkflowFailure.networkIssue()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_NetworkIssue);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) {
    return networkIssue();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) {
    return networkIssue?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) {
    if (networkIssue != null) {
      return networkIssue();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) {
    return networkIssue(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) {
    return networkIssue?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) {
    if (networkIssue != null) {
      return networkIssue(this);
    }
    return orElse();
  }
}

abstract class _NetworkIssue implements WorkflowFailure {
  const factory _NetworkIssue() = _$_NetworkIssue;
}

/// @nodoc
abstract class _$$_StateNotFoundCopyWith<$Res> {
  factory _$$_StateNotFoundCopyWith(
          _$_StateNotFound value, $Res Function(_$_StateNotFound) then) =
      __$$_StateNotFoundCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_StateNotFoundCopyWithImpl<$Res>
    extends _$WorkflowFailureCopyWithImpl<$Res, _$_StateNotFound>
    implements _$$_StateNotFoundCopyWith<$Res> {
  __$$_StateNotFoundCopyWithImpl(
      _$_StateNotFound _value, $Res Function(_$_StateNotFound) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_StateNotFound implements _StateNotFound {
  const _$_StateNotFound();

  @override
  String toString() {
    return 'WorkflowFailure.stateNotFound()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_StateNotFound);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) {
    return stateNotFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) {
    return stateNotFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) {
    if (stateNotFound != null) {
      return stateNotFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) {
    return stateNotFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) {
    return stateNotFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) {
    if (stateNotFound != null) {
      return stateNotFound(this);
    }
    return orElse();
  }
}

abstract class _StateNotFound implements WorkflowFailure {
  const factory _StateNotFound() = _$_StateNotFound;
}

/// @nodoc
abstract class _$$_NoNextStepCopyWith<$Res> {
  factory _$$_NoNextStepCopyWith(
          _$_NoNextStep value, $Res Function(_$_NoNextStep) then) =
      __$$_NoNextStepCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_NoNextStepCopyWithImpl<$Res>
    extends _$WorkflowFailureCopyWithImpl<$Res, _$_NoNextStep>
    implements _$$_NoNextStepCopyWith<$Res> {
  __$$_NoNextStepCopyWithImpl(
      _$_NoNextStep _value, $Res Function(_$_NoNextStep) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_NoNextStep implements _NoNextStep {
  const _$_NoNextStep();

  @override
  String toString() {
    return 'WorkflowFailure.noNextStep()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_NoNextStep);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) {
    return noNextStep();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) {
    return noNextStep?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) {
    if (noNextStep != null) {
      return noNextStep();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) {
    return noNextStep(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) {
    return noNextStep?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) {
    if (noNextStep != null) {
      return noNextStep(this);
    }
    return orElse();
  }
}

abstract class _NoNextStep implements WorkflowFailure {
  const factory _NoNextStep() = _$_NoNextStep;
}

/// @nodoc
abstract class _$$_InitFailedCopyWith<$Res> {
  factory _$$_InitFailedCopyWith(
          _$_InitFailed value, $Res Function(_$_InitFailed) then) =
      __$$_InitFailedCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitFailedCopyWithImpl<$Res>
    extends _$WorkflowFailureCopyWithImpl<$Res, _$_InitFailed>
    implements _$$_InitFailedCopyWith<$Res> {
  __$$_InitFailedCopyWithImpl(
      _$_InitFailed _value, $Res Function(_$_InitFailed) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_InitFailed implements _InitFailed {
  const _$_InitFailed();

  @override
  String toString() {
    return 'WorkflowFailure.initFailed()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_InitFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) {
    return initFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) {
    return initFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) {
    if (initFailed != null) {
      return initFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) {
    return initFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) {
    return initFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) {
    if (initFailed != null) {
      return initFailed(this);
    }
    return orElse();
  }
}

abstract class _InitFailed implements WorkflowFailure {
  const factory _InitFailed() = _$_InitFailed;
}

/// @nodoc
abstract class _$$_RetryAfterCopyWith<$Res> {
  factory _$$_RetryAfterCopyWith(
          _$_RetryAfter value, $Res Function(_$_RetryAfter) then) =
      __$$_RetryAfterCopyWithImpl<$Res>;
  @useResult
  $Res call({int? secondsToRetry});
}

/// @nodoc
class __$$_RetryAfterCopyWithImpl<$Res>
    extends _$WorkflowFailureCopyWithImpl<$Res, _$_RetryAfter>
    implements _$$_RetryAfterCopyWith<$Res> {
  __$$_RetryAfterCopyWithImpl(
      _$_RetryAfter _value, $Res Function(_$_RetryAfter) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? secondsToRetry = freezed,
  }) {
    return _then(_$_RetryAfter(
      freezed == secondsToRetry
          ? _value.secondsToRetry
          : secondsToRetry // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$_RetryAfter implements _RetryAfter {
  const _$_RetryAfter(this.secondsToRetry);

  @override
  final int? secondsToRetry;

  @override
  String toString() {
    return 'WorkflowFailure.retryAfter(secondsToRetry: $secondsToRetry)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RetryAfter &&
            (identical(other.secondsToRetry, secondsToRetry) ||
                other.secondsToRetry == secondsToRetry));
  }

  @override
  int get hashCode => Object.hash(runtimeType, secondsToRetry);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RetryAfterCopyWith<_$_RetryAfter> get copyWith =>
      __$$_RetryAfterCopyWithImpl<_$_RetryAfter>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) {
    return retryAfter(secondsToRetry);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) {
    return retryAfter?.call(secondsToRetry);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) {
    if (retryAfter != null) {
      return retryAfter(secondsToRetry);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) {
    return retryAfter(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) {
    return retryAfter?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) {
    if (retryAfter != null) {
      return retryAfter(this);
    }
    return orElse();
  }
}

abstract class _RetryAfter implements WorkflowFailure {
  const factory _RetryAfter(final int? secondsToRetry) = _$_RetryAfter;

  int? get secondsToRetry;
  @JsonKey(ignore: true)
  _$$_RetryAfterCopyWith<_$_RetryAfter> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ValidationsErrorCopyWith<$Res> {
  factory _$$_ValidationsErrorCopyWith(
          _$_ValidationsError value, $Res Function(_$_ValidationsError) then) =
      __$$_ValidationsErrorCopyWithImpl<$Res>;
  @useResult
  $Res call({WorkflowValidationErrorResponse validationErrorResponse});

  $WorkflowValidationErrorResponseCopyWith<$Res> get validationErrorResponse;
}

/// @nodoc
class __$$_ValidationsErrorCopyWithImpl<$Res>
    extends _$WorkflowFailureCopyWithImpl<$Res, _$_ValidationsError>
    implements _$$_ValidationsErrorCopyWith<$Res> {
  __$$_ValidationsErrorCopyWithImpl(
      _$_ValidationsError _value, $Res Function(_$_ValidationsError) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? validationErrorResponse = null,
  }) {
    return _then(_$_ValidationsError(
      null == validationErrorResponse
          ? _value.validationErrorResponse
          : validationErrorResponse // ignore: cast_nullable_to_non_nullable
              as WorkflowValidationErrorResponse,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $WorkflowValidationErrorResponseCopyWith<$Res> get validationErrorResponse {
    return $WorkflowValidationErrorResponseCopyWith<$Res>(
        _value.validationErrorResponse, (value) {
      return _then(_value.copyWith(validationErrorResponse: value));
    });
  }
}

/// @nodoc

class _$_ValidationsError implements _ValidationsError {
  const _$_ValidationsError(this.validationErrorResponse);

  @override
  final WorkflowValidationErrorResponse validationErrorResponse;

  @override
  String toString() {
    return 'WorkflowFailure.validationsError(validationErrorResponse: $validationErrorResponse)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ValidationsError &&
            (identical(
                    other.validationErrorResponse, validationErrorResponse) ||
                other.validationErrorResponse == validationErrorResponse));
  }

  @override
  int get hashCode => Object.hash(runtimeType, validationErrorResponse);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ValidationsErrorCopyWith<_$_ValidationsError> get copyWith =>
      __$$_ValidationsErrorCopyWithImpl<_$_ValidationsError>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() unauthenticated,
    required TResult Function() differentPhase,
    required TResult Function(String? traceparent) unexpected,
    required TResult Function() networkIssue,
    required TResult Function() stateNotFound,
    required TResult Function() noNextStep,
    required TResult Function() initFailed,
    required TResult Function(int? secondsToRetry) retryAfter,
    required TResult Function(
            WorkflowValidationErrorResponse validationErrorResponse)
        validationsError,
  }) {
    return validationsError(validationErrorResponse);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? unauthenticated,
    TResult? Function()? differentPhase,
    TResult? Function(String? traceparent)? unexpected,
    TResult? Function()? networkIssue,
    TResult? Function()? stateNotFound,
    TResult? Function()? noNextStep,
    TResult? Function()? initFailed,
    TResult? Function(int? secondsToRetry)? retryAfter,
    TResult? Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
  }) {
    return validationsError?.call(validationErrorResponse);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? unauthenticated,
    TResult Function()? differentPhase,
    TResult Function(String? traceparent)? unexpected,
    TResult Function()? networkIssue,
    TResult Function()? stateNotFound,
    TResult Function()? noNextStep,
    TResult Function()? initFailed,
    TResult Function(int? secondsToRetry)? retryAfter,
    TResult Function(WorkflowValidationErrorResponse validationErrorResponse)?
        validationsError,
    required TResult orElse(),
  }) {
    if (validationsError != null) {
      return validationsError(validationErrorResponse);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_DifferentPhase value) differentPhase,
    required TResult Function(UnexpectedFlowResponse value) unexpected,
    required TResult Function(_NetworkIssue value) networkIssue,
    required TResult Function(_StateNotFound value) stateNotFound,
    required TResult Function(_NoNextStep value) noNextStep,
    required TResult Function(_InitFailed value) initFailed,
    required TResult Function(_RetryAfter value) retryAfter,
    required TResult Function(_ValidationsError value) validationsError,
  }) {
    return validationsError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_DifferentPhase value)? differentPhase,
    TResult? Function(UnexpectedFlowResponse value)? unexpected,
    TResult? Function(_NetworkIssue value)? networkIssue,
    TResult? Function(_StateNotFound value)? stateNotFound,
    TResult? Function(_NoNextStep value)? noNextStep,
    TResult? Function(_InitFailed value)? initFailed,
    TResult? Function(_RetryAfter value)? retryAfter,
    TResult? Function(_ValidationsError value)? validationsError,
  }) {
    return validationsError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_DifferentPhase value)? differentPhase,
    TResult Function(UnexpectedFlowResponse value)? unexpected,
    TResult Function(_NetworkIssue value)? networkIssue,
    TResult Function(_StateNotFound value)? stateNotFound,
    TResult Function(_NoNextStep value)? noNextStep,
    TResult Function(_InitFailed value)? initFailed,
    TResult Function(_RetryAfter value)? retryAfter,
    TResult Function(_ValidationsError value)? validationsError,
    required TResult orElse(),
  }) {
    if (validationsError != null) {
      return validationsError(this);
    }
    return orElse();
  }
}

abstract class _ValidationsError implements WorkflowFailure {
  const factory _ValidationsError(
          final WorkflowValidationErrorResponse validationErrorResponse) =
      _$_ValidationsError;

  WorkflowValidationErrorResponse get validationErrorResponse;
  @JsonKey(ignore: true)
  _$$_ValidationsErrorCopyWith<_$_ValidationsError> get copyWith =>
      throw _privateConstructorUsedError;
}
