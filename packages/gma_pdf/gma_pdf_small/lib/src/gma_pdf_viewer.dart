import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:gma_pdf_core/gma_pdf_core.dart' as core;
import 'package:pdfx/pdfx.dart';

class GmaPdfViewer extends StatefulWidget {
  final core.PdfDocument document;
  final bool pageSnap;
  final bool swipeHorizontal;
  const GmaPdfViewer({
    Key? key,
    required this.document,
    this.pageSnap = true,
    this.swipeHorizontal = false,
  }) : super(key: key);

  @override
  GmaPdfViewerState createState() => GmaPdfViewerState();
}

class GmaPdfViewerState extends State<GmaPdfViewer> with WidgetsBindingObserver {
  String errorMessage = '';
  late PdfController pdfController;
  @override
  void initState() {
    pdfController = PdfController(
      document: PdfDocument.openData(Future.value(widget.document.fileData)),
    );
    super.initState();
  }

  static PhotoViewGalleryPageOptions _pageBuilder(
    BuildContext context,
    Future<PdfPageImage> pageImage,
    int index,
    PdfDocument document,
  ) =>
      PhotoViewGalleryPageOptions(
        imageProvider: PdfPageImageProvider(
          pageImage,
          index,
          document.id,
        ),
        minScale: PhotoViewComputedScale.contained * 1,
        maxScale: PhotoViewComputedScale.contained * 7.0,
        initialScale: PhotoViewComputedScale.contained * 1.0,
        heroAttributes: PhotoViewHeroAttributes(tag: '${document.id}-$index'),
      );

  @override
  Widget build(BuildContext context) {
    if (errorMessage.isNotEmpty) {
      return core.PdfError(
        errorMessageDescription: errorMessage,
      );
    }

    return Stack(
      children: [
        Positioned(
          top: 16,
          right: 16,
          child: PdfPageNumber(
            controller: pdfController,
            builder: (_, loadingState, page, pagesCount) => Container(
              alignment: Alignment.topRight,
              decoration: BoxDecoration(
                color: HciColors.supplementary900.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: KoyalText.caption1(
                  '${L10nCappUi.of(context).page} $page/${pagesCount ?? 0}',
                  color: Theme.of(context).scaffoldBackgroundColor,
                ),
              ),
            ),
          ),
        ),
        PdfView(
          controller: pdfController,
          renderer: (page) => page.render(
            width: page.width * 4,
            height: page.height * 4,
          ),
          builders: PdfViewBuilders<DefaultBuilderOptions>(
            options: const DefaultBuilderOptions(),
            documentLoaderBuilder: (_) => const Center(child: CircularProgressIndicator()),
            pageLoaderBuilder: (_) => const Center(child: CircularProgressIndicator()),
            pageBuilder: _pageBuilder,
          ),
          scrollDirection: widget.swipeHorizontal ? Axis.horizontal : Axis.vertical,
          pageSnapping: widget.pageSnap,
          onDocumentError: (error) {
            setState(() {
              errorMessage = error.toString();
            });
          },
        ),
      ],
    );
  }
}
