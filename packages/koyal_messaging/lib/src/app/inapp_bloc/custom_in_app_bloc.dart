import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:bloc/bloc.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../koyal_messaging.dart';
import '../../core/custom_in_app_helper.dart';

part 'custom_in_app_bloc.freezed.dart';
part 'custom_in_app_event.dart';
part 'custom_in_app_state.dart';

class CustomInAppBloc extends Bloc<CustomInAppEvent, CustomInAppState> {
  final ICustomInAppRepository inAppRepository;
  final String countryCode;
  String? _lastMsid;

  final IKoyalMessagingRepository messagingRepository;
  StreamSubscription<NotificationMessage>? _messageSubscription;
  final InAppMessagingService inAppMessagingService;

  Map<String, dynamic>? _offerPayLoad;

  CustomInAppBloc({
    required this.inAppRepository,
    required this.countryCode,
    required this.messagingRepository,
    required this.inAppMessagingService,
  }) : super(CustomInAppState.initialize()) {
    on<CustomInAppShowMessage>(_showInAppMessage);
    on<CustomInAppShowCustomMessage>(_showCustomInAppMessage);

    _messageSubscription = messagingRepository.onReceived.listen((message) {
      if ((message.data?.gmaInApp ?? false) &&
          !inAppMessagingService.shouldShowWelcomeScreenInAppMsg(message.data?.gmaInAppType)) {
        _offerPayLoad = _getInAppPayLoad(message.data?.gmaInAppPayLoad);
        final type = message.data?.gmaInAppType;
        add(
          CustomInAppEvent.showInAppMessage(
            templateId: message.data?.gmaTemplateId ?? 0,
            msid: message.data?.msId,
            inAppMessageType: type,
          ),
        );
      }
    });
  }
  Future<void> _showInAppMessage(CustomInAppShowMessage event, Emitter<CustomInAppState> emit) async {
    // Currently there is bug in fcm lib duplication messages on iOS
    // https://github.com/firebase/flutterfire/issues/13366
    // It will be fixed when fcm lib is updated during flutter upgrade to 2.9.3
    if (Platform.isIOS && _lastMsid == event.msid) {
      return;
    }

    _lastMsid = event.msid;

    emit(
      state.copyWith(
        inAppMessage: null,
        msid: null,
      ),
    );
    final result = await inAppRepository.getCustomInApp(
      templateId: event.templateId,
    );
    emit(
      result.fold((l) => state.copyWith(), (r) {
        if (inAppMessagingService.shouldShowInAppMsgPersonalized(event.inAppMessageType)) {
          final resultData = inAppMessagingService.handlePersonalizedData(
            r,
            _offerPayLoad ?? {},
          );
          if (resultData == null) {
            return state.copyWith();
          } else {
            return state.copyWith(
              inAppMessage: resultData,
              msid: event.msid,
              onClose: null,
              isDismissible: isDismissible(r),
              inAppType: event.inAppMessageType,
            );
          }
        }
        return state.copyWith(
          inAppMessage: r,
          msid: event.msid,
          onClose: null,
          isDismissible: isDismissible(r),
        );
      }),
    );
  }

  Future<void> _showCustomInAppMessage(CustomInAppShowCustomMessage event, Emitter<CustomInAppState> emit) async {
    // Resetting state to re-trigger inapp
    emit(
      state.copyWith(
        inAppMessage: null,
      ),
    );
    emit(
      state.copyWith(
        inAppMessage: event.customInApp,
        msid: null,
        onClose: event.onClose,
        isDismissible: isDismissible(event.customInApp),
      ),
    );
  }

  // US 169335 for IN is is dismissible only when the tertiaryCtaButtonText is not null but for other countries it is always dismissible
  bool isDismissible(CustomInApp customInApp) =>
      countryCode != 'IN' ||
      (customInApp.tertiaryCtaButtonText != null && customInApp.tertiaryCtaButtonText!.isNotEmpty);

  Map<String, dynamic>? _getInAppPayLoad(String? data) {
    if (data == null || data.isEmpty) return null;

    try {
      final decoded = json.decode(data);
      if (decoded is Map<String, dynamic>) {
        final value = decoded['value'];
        if (value is Map<String, dynamic>) {
          return value;
        }
      }
    } catch (e) {
      return null;
    }

    return null;
  }

  @override
  Future<void> close() {
    _messageSubscription?.cancel();
    return super.close();
  }
}
