// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'custom_in_app_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CustomInAppState {
  CustomInApp? get inAppMessage => throw _privateConstructorUsedError;
  String? get msid => throw _privateConstructorUsedError;
  bool get isDismissible => throw _privateConstructorUsedError;
  VoidCallback? get onClose => throw _privateConstructorUsedError;
  String? get inAppType => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CustomInAppStateCopyWith<CustomInAppState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomInAppStateCopyWith<$Res> {
  factory $CustomInAppStateCopyWith(
          CustomInAppState value, $Res Function(CustomInAppState) then) =
      _$CustomInAppStateCopyWithImpl<$Res, CustomInAppState>;
  @useResult
  $Res call(
      {CustomInApp? inAppMessage,
      String? msid,
      bool isDismissible,
      VoidCallback? onClose,
      String? inAppType});

  $CustomInAppCopyWith<$Res>? get inAppMessage;
}

/// @nodoc
class _$CustomInAppStateCopyWithImpl<$Res, $Val extends CustomInAppState>
    implements $CustomInAppStateCopyWith<$Res> {
  _$CustomInAppStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inAppMessage = freezed,
    Object? msid = freezed,
    Object? isDismissible = null,
    Object? onClose = freezed,
    Object? inAppType = freezed,
  }) {
    return _then(_value.copyWith(
      inAppMessage: freezed == inAppMessage
          ? _value.inAppMessage
          : inAppMessage // ignore: cast_nullable_to_non_nullable
              as CustomInApp?,
      msid: freezed == msid
          ? _value.msid
          : msid // ignore: cast_nullable_to_non_nullable
              as String?,
      isDismissible: null == isDismissible
          ? _value.isDismissible
          : isDismissible // ignore: cast_nullable_to_non_nullable
              as bool,
      onClose: freezed == onClose
          ? _value.onClose
          : onClose // ignore: cast_nullable_to_non_nullable
              as VoidCallback?,
      inAppType: freezed == inAppType
          ? _value.inAppType
          : inAppType // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CustomInAppCopyWith<$Res>? get inAppMessage {
    if (_value.inAppMessage == null) {
      return null;
    }

    return $CustomInAppCopyWith<$Res>(_value.inAppMessage!, (value) {
      return _then(_value.copyWith(inAppMessage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CustomInAppStateCopyWith<$Res>
    implements $CustomInAppStateCopyWith<$Res> {
  factory _$$_CustomInAppStateCopyWith(
          _$_CustomInAppState value, $Res Function(_$_CustomInAppState) then) =
      __$$_CustomInAppStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CustomInApp? inAppMessage,
      String? msid,
      bool isDismissible,
      VoidCallback? onClose,
      String? inAppType});

  @override
  $CustomInAppCopyWith<$Res>? get inAppMessage;
}

/// @nodoc
class __$$_CustomInAppStateCopyWithImpl<$Res>
    extends _$CustomInAppStateCopyWithImpl<$Res, _$_CustomInAppState>
    implements _$$_CustomInAppStateCopyWith<$Res> {
  __$$_CustomInAppStateCopyWithImpl(
      _$_CustomInAppState _value, $Res Function(_$_CustomInAppState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inAppMessage = freezed,
    Object? msid = freezed,
    Object? isDismissible = null,
    Object? onClose = freezed,
    Object? inAppType = freezed,
  }) {
    return _then(_$_CustomInAppState(
      inAppMessage: freezed == inAppMessage
          ? _value.inAppMessage
          : inAppMessage // ignore: cast_nullable_to_non_nullable
              as CustomInApp?,
      msid: freezed == msid
          ? _value.msid
          : msid // ignore: cast_nullable_to_non_nullable
              as String?,
      isDismissible: null == isDismissible
          ? _value.isDismissible
          : isDismissible // ignore: cast_nullable_to_non_nullable
              as bool,
      onClose: freezed == onClose
          ? _value.onClose
          : onClose // ignore: cast_nullable_to_non_nullable
              as VoidCallback?,
      inAppType: freezed == inAppType
          ? _value.inAppType
          : inAppType // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_CustomInAppState extends _CustomInAppState {
  _$_CustomInAppState(
      {this.inAppMessage = null,
      this.msid = null,
      this.isDismissible = true,
      this.onClose = null,
      this.inAppType = null})
      : super._();

  @override
  @JsonKey()
  final CustomInApp? inAppMessage;
  @override
  @JsonKey()
  final String? msid;
  @override
  @JsonKey()
  final bool isDismissible;
  @override
  @JsonKey()
  final VoidCallback? onClose;
  @override
  @JsonKey()
  final String? inAppType;

  @override
  String toString() {
    return 'CustomInAppState(inAppMessage: $inAppMessage, msid: $msid, isDismissible: $isDismissible, onClose: $onClose, inAppType: $inAppType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CustomInAppState &&
            (identical(other.inAppMessage, inAppMessage) ||
                other.inAppMessage == inAppMessage) &&
            (identical(other.msid, msid) || other.msid == msid) &&
            (identical(other.isDismissible, isDismissible) ||
                other.isDismissible == isDismissible) &&
            (identical(other.onClose, onClose) || other.onClose == onClose) &&
            (identical(other.inAppType, inAppType) ||
                other.inAppType == inAppType));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, inAppMessage, msid, isDismissible, onClose, inAppType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CustomInAppStateCopyWith<_$_CustomInAppState> get copyWith =>
      __$$_CustomInAppStateCopyWithImpl<_$_CustomInAppState>(this, _$identity);
}

abstract class _CustomInAppState extends CustomInAppState {
  factory _CustomInAppState(
      {final CustomInApp? inAppMessage,
      final String? msid,
      final bool isDismissible,
      final VoidCallback? onClose,
      final String? inAppType}) = _$_CustomInAppState;
  _CustomInAppState._() : super._();

  @override
  CustomInApp? get inAppMessage;
  @override
  String? get msid;
  @override
  bool get isDismissible;
  @override
  VoidCallback? get onClose;
  @override
  String? get inAppType;
  @override
  @JsonKey(ignore: true)
  _$$_CustomInAppStateCopyWith<_$_CustomInAppState> get copyWith =>
      throw _privateConstructorUsedError;
}
