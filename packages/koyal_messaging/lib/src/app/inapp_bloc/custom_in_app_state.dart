part of 'custom_in_app_bloc.dart';

@Freezed(
  fromJson: false,
  toJson: false,
  map: FreezedMapOptions.none,
  when: FreezedWhenOptions.none,
)
class CustomInAppState with _$CustomInAppState {
  factory CustomInAppState({
    @Default(null) CustomInApp? inAppMessage,
    @Default(null) String? msid,
    @Default(true) bool isDismissible,
    @Default(null) VoidCallback? onClose,
    @Default(null) String? inAppType,
  }) = _CustomInAppState;
  factory CustomInAppState.initialize() => CustomInAppState();
  CustomInAppState._();
}
