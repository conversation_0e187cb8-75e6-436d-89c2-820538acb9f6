// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'koyal_messaging_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$KoyalMessagingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KoyalMessagingEventCopyWith<$Res> {
  factory $KoyalMessagingEventCopyWith(
          KoyalMessagingEvent value, $Res Function(KoyalMessagingEvent) then) =
      _$KoyalMessagingEventCopyWithImpl<$Res, KoyalMessagingEvent>;
}

/// @nodoc
class _$KoyalMessagingEventCopyWithImpl<$Res, $Val extends KoyalMessagingEvent>
    implements $KoyalMessagingEventCopyWith<$Res> {
  _$KoyalMessagingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitCopyWith<$Res> {
  factory _$$_InitCopyWith(_$_Init value, $Res Function(_$_Init) then) =
      __$$_InitCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitCopyWithImpl<$Res>
    extends _$KoyalMessagingEventCopyWithImpl<$Res, _$_Init>
    implements _$$_InitCopyWith<$Res> {
  __$$_InitCopyWithImpl(_$_Init _value, $Res Function(_$_Init) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Init with DiagnosticableTreeMixin implements _Init {
  const _$_Init();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingEvent.init()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'KoyalMessagingEvent.init'));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Init);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) {
    return init();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) {
    return init?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) {
    return init?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class _Init implements KoyalMessagingEvent {
  const factory _Init() = _$_Init;
}

/// @nodoc
abstract class _$$_BreakNotificationsCopyWith<$Res> {
  factory _$$_BreakNotificationsCopyWith(_$_BreakNotifications value,
          $Res Function(_$_BreakNotifications) then) =
      __$$_BreakNotificationsCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_BreakNotificationsCopyWithImpl<$Res>
    extends _$KoyalMessagingEventCopyWithImpl<$Res, _$_BreakNotifications>
    implements _$$_BreakNotificationsCopyWith<$Res> {
  __$$_BreakNotificationsCopyWithImpl(
      _$_BreakNotifications _value, $Res Function(_$_BreakNotifications) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_BreakNotifications
    with DiagnosticableTreeMixin
    implements _BreakNotifications {
  const _$_BreakNotifications();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingEvent.breakNotifications()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(
        DiagnosticsProperty('type', 'KoyalMessagingEvent.breakNotifications'));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_BreakNotifications);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) {
    return breakNotifications();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) {
    return breakNotifications?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (breakNotifications != null) {
      return breakNotifications();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) {
    return breakNotifications(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) {
    return breakNotifications?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (breakNotifications != null) {
      return breakNotifications(this);
    }
    return orElse();
  }
}

abstract class _BreakNotifications implements KoyalMessagingEvent {
  const factory _BreakNotifications() = _$_BreakNotifications;
}

/// @nodoc
abstract class _$$_RegisterPushTokenCopyWith<$Res> {
  factory _$$_RegisterPushTokenCopyWith(_$_RegisterPushToken value,
          $Res Function(_$_RegisterPushToken) then) =
      __$$_RegisterPushTokenCopyWithImpl<$Res>;
  @useResult
  $Res call({bool resetRetryLimit, bool forceRegister});
}

/// @nodoc
class __$$_RegisterPushTokenCopyWithImpl<$Res>
    extends _$KoyalMessagingEventCopyWithImpl<$Res, _$_RegisterPushToken>
    implements _$$_RegisterPushTokenCopyWith<$Res> {
  __$$_RegisterPushTokenCopyWithImpl(
      _$_RegisterPushToken _value, $Res Function(_$_RegisterPushToken) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? resetRetryLimit = null,
    Object? forceRegister = null,
  }) {
    return _then(_$_RegisterPushToken(
      resetRetryLimit: null == resetRetryLimit
          ? _value.resetRetryLimit
          : resetRetryLimit // ignore: cast_nullable_to_non_nullable
              as bool,
      forceRegister: null == forceRegister
          ? _value.forceRegister
          : forceRegister // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_RegisterPushToken
    with DiagnosticableTreeMixin
    implements _RegisterPushToken {
  const _$_RegisterPushToken(
      {this.resetRetryLimit = false, this.forceRegister = false});

  @override
  @JsonKey()
  final bool resetRetryLimit;
  @override
  @JsonKey()
  final bool forceRegister;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingEvent.registerPushToken(resetRetryLimit: $resetRetryLimit, forceRegister: $forceRegister)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(
          DiagnosticsProperty('type', 'KoyalMessagingEvent.registerPushToken'))
      ..add(DiagnosticsProperty('resetRetryLimit', resetRetryLimit))
      ..add(DiagnosticsProperty('forceRegister', forceRegister));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RegisterPushToken &&
            (identical(other.resetRetryLimit, resetRetryLimit) ||
                other.resetRetryLimit == resetRetryLimit) &&
            (identical(other.forceRegister, forceRegister) ||
                other.forceRegister == forceRegister));
  }

  @override
  int get hashCode => Object.hash(runtimeType, resetRetryLimit, forceRegister);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RegisterPushTokenCopyWith<_$_RegisterPushToken> get copyWith =>
      __$$_RegisterPushTokenCopyWithImpl<_$_RegisterPushToken>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) {
    return registerPushToken(resetRetryLimit, forceRegister);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) {
    return registerPushToken?.call(resetRetryLimit, forceRegister);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (registerPushToken != null) {
      return registerPushToken(resetRetryLimit, forceRegister);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) {
    return registerPushToken(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) {
    return registerPushToken?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (registerPushToken != null) {
      return registerPushToken(this);
    }
    return orElse();
  }
}

abstract class _RegisterPushToken implements KoyalMessagingEvent {
  const factory _RegisterPushToken(
      {final bool resetRetryLimit,
      final bool forceRegister}) = _$_RegisterPushToken;

  bool get resetRetryLimit;
  bool get forceRegister;
  @JsonKey(ignore: true)
  _$$_RegisterPushTokenCopyWith<_$_RegisterPushToken> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_RegisterUserCopyWith<$Res> {
  factory _$$_RegisterUserCopyWith(
          _$_RegisterUser value, $Res Function(_$_RegisterUser) then) =
      __$$_RegisterUserCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_RegisterUserCopyWithImpl<$Res>
    extends _$KoyalMessagingEventCopyWithImpl<$Res, _$_RegisterUser>
    implements _$$_RegisterUserCopyWith<$Res> {
  __$$_RegisterUserCopyWithImpl(
      _$_RegisterUser _value, $Res Function(_$_RegisterUser) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_RegisterUser with DiagnosticableTreeMixin implements _RegisterUser {
  const _$_RegisterUser();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingEvent.registerUser()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
        .add(DiagnosticsProperty('type', 'KoyalMessagingEvent.registerUser'));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_RegisterUser);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) {
    return registerUser();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) {
    return registerUser?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (registerUser != null) {
      return registerUser();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) {
    return registerUser(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) {
    return registerUser?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (registerUser != null) {
      return registerUser(this);
    }
    return orElse();
  }
}

abstract class _RegisterUser implements KoyalMessagingEvent {
  const factory _RegisterUser() = _$_RegisterUser;
}

/// @nodoc
abstract class _$$_TestNotificationsCopyWith<$Res> {
  factory _$$_TestNotificationsCopyWith(_$_TestNotifications value,
          $Res Function(_$_TestNotifications) then) =
      __$$_TestNotificationsCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_TestNotificationsCopyWithImpl<$Res>
    extends _$KoyalMessagingEventCopyWithImpl<$Res, _$_TestNotifications>
    implements _$$_TestNotificationsCopyWith<$Res> {
  __$$_TestNotificationsCopyWithImpl(
      _$_TestNotifications _value, $Res Function(_$_TestNotifications) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_TestNotifications
    with DiagnosticableTreeMixin
    implements _TestNotifications {
  const _$_TestNotifications();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingEvent.testNotifications()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(
        DiagnosticsProperty('type', 'KoyalMessagingEvent.testNotifications'));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_TestNotifications);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) {
    return testNotifications();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) {
    return testNotifications?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (testNotifications != null) {
      return testNotifications();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) {
    return testNotifications(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) {
    return testNotifications?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (testNotifications != null) {
      return testNotifications(this);
    }
    return orElse();
  }
}

abstract class _TestNotifications implements KoyalMessagingEvent {
  const factory _TestNotifications() = _$_TestNotifications;
}

/// @nodoc
abstract class _$$_CheckTestNotificationRetrievementCopyWith<$Res> {
  factory _$$_CheckTestNotificationRetrievementCopyWith(
          _$_CheckTestNotificationRetrievement value,
          $Res Function(_$_CheckTestNotificationRetrievement) then) =
      __$$_CheckTestNotificationRetrievementCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_CheckTestNotificationRetrievementCopyWithImpl<$Res>
    extends _$KoyalMessagingEventCopyWithImpl<$Res,
        _$_CheckTestNotificationRetrievement>
    implements _$$_CheckTestNotificationRetrievementCopyWith<$Res> {
  __$$_CheckTestNotificationRetrievementCopyWithImpl(
      _$_CheckTestNotificationRetrievement _value,
      $Res Function(_$_CheckTestNotificationRetrievement) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_CheckTestNotificationRetrievement
    with DiagnosticableTreeMixin
    implements _CheckTestNotificationRetrievement {
  const _$_CheckTestNotificationRetrievement();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingEvent.checkTestNotificationRetrievement()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty(
        'type', 'KoyalMessagingEvent.checkTestNotificationRetrievement'));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CheckTestNotificationRetrievement);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) {
    return checkTestNotificationRetrievement();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) {
    return checkTestNotificationRetrievement?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (checkTestNotificationRetrievement != null) {
      return checkTestNotificationRetrievement();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) {
    return checkTestNotificationRetrievement(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) {
    return checkTestNotificationRetrievement?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (checkTestNotificationRetrievement != null) {
      return checkTestNotificationRetrievement(this);
    }
    return orElse();
  }
}

abstract class _CheckTestNotificationRetrievement
    implements KoyalMessagingEvent {
  const factory _CheckTestNotificationRetrievement() =
      _$_CheckTestNotificationRetrievement;
}

/// @nodoc
abstract class _$$_MessageNotificationReceivedCopyWith<$Res> {
  factory _$$_MessageNotificationReceivedCopyWith(
          _$_MessageNotificationReceived value,
          $Res Function(_$_MessageNotificationReceived) then) =
      __$$_MessageNotificationReceivedCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationMessage message});

  $NotificationMessageCopyWith<$Res> get message;
}

/// @nodoc
class __$$_MessageNotificationReceivedCopyWithImpl<$Res>
    extends _$KoyalMessagingEventCopyWithImpl<$Res,
        _$_MessageNotificationReceived>
    implements _$$_MessageNotificationReceivedCopyWith<$Res> {
  __$$_MessageNotificationReceivedCopyWithImpl(
      _$_MessageNotificationReceived _value,
      $Res Function(_$_MessageNotificationReceived) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$_MessageNotificationReceived(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as NotificationMessage,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $NotificationMessageCopyWith<$Res> get message {
    return $NotificationMessageCopyWith<$Res>(_value.message, (value) {
      return _then(_value.copyWith(message: value));
    });
  }
}

/// @nodoc

class _$_MessageNotificationReceived
    with DiagnosticableTreeMixin
    implements _MessageNotificationReceived {
  const _$_MessageNotificationReceived(this.message);

  @override
  final NotificationMessage message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingEvent.received(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'KoyalMessagingEvent.received'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MessageNotificationReceived &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MessageNotificationReceivedCopyWith<_$_MessageNotificationReceived>
      get copyWith => __$$_MessageNotificationReceivedCopyWithImpl<
          _$_MessageNotificationReceived>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) {
    return received(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) {
    return received?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (received != null) {
      return received(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) {
    return received(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) {
    return received?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (received != null) {
      return received(this);
    }
    return orElse();
  }
}

abstract class _MessageNotificationReceived implements KoyalMessagingEvent {
  const factory _MessageNotificationReceived(
      final NotificationMessage message) = _$_MessageNotificationReceived;

  NotificationMessage get message;
  @JsonKey(ignore: true)
  _$$_MessageNotificationReceivedCopyWith<_$_MessageNotificationReceived>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_MessageNotificationTappedCopyWith<$Res> {
  factory _$$_MessageNotificationTappedCopyWith(
          _$_MessageNotificationTapped value,
          $Res Function(_$_MessageNotificationTapped) then) =
      __$$_MessageNotificationTappedCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationMessage message});

  $NotificationMessageCopyWith<$Res> get message;
}

/// @nodoc
class __$$_MessageNotificationTappedCopyWithImpl<$Res>
    extends _$KoyalMessagingEventCopyWithImpl<$Res,
        _$_MessageNotificationTapped>
    implements _$$_MessageNotificationTappedCopyWith<$Res> {
  __$$_MessageNotificationTappedCopyWithImpl(
      _$_MessageNotificationTapped _value,
      $Res Function(_$_MessageNotificationTapped) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$_MessageNotificationTapped(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as NotificationMessage,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $NotificationMessageCopyWith<$Res> get message {
    return $NotificationMessageCopyWith<$Res>(_value.message, (value) {
      return _then(_value.copyWith(message: value));
    });
  }
}

/// @nodoc

class _$_MessageNotificationTapped
    with DiagnosticableTreeMixin
    implements _MessageNotificationTapped {
  const _$_MessageNotificationTapped(this.message);

  @override
  final NotificationMessage message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingEvent.tapped(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'KoyalMessagingEvent.tapped'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MessageNotificationTapped &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MessageNotificationTappedCopyWith<_$_MessageNotificationTapped>
      get copyWith => __$$_MessageNotificationTappedCopyWithImpl<
          _$_MessageNotificationTapped>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) {
    return tapped(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) {
    return tapped?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (tapped != null) {
      return tapped(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) {
    return tapped(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) {
    return tapped?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (tapped != null) {
      return tapped(this);
    }
    return orElse();
  }
}

abstract class _MessageNotificationTapped implements KoyalMessagingEvent {
  const factory _MessageNotificationTapped(final NotificationMessage message) =
      _$_MessageNotificationTapped;

  NotificationMessage get message;
  @JsonKey(ignore: true)
  _$$_MessageNotificationTappedCopyWith<_$_MessageNotificationTapped>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ShowLiveActivityNotificationCopyWith<$Res> {
  factory _$$_ShowLiveActivityNotificationCopyWith(
          _$_ShowLiveActivityNotification value,
          $Res Function(_$_ShowLiveActivityNotification) then) =
      __$$_ShowLiveActivityNotificationCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationMessage notificationMessage});

  $NotificationMessageCopyWith<$Res> get notificationMessage;
}

/// @nodoc
class __$$_ShowLiveActivityNotificationCopyWithImpl<$Res>
    extends _$KoyalMessagingEventCopyWithImpl<$Res,
        _$_ShowLiveActivityNotification>
    implements _$$_ShowLiveActivityNotificationCopyWith<$Res> {
  __$$_ShowLiveActivityNotificationCopyWithImpl(
      _$_ShowLiveActivityNotification _value,
      $Res Function(_$_ShowLiveActivityNotification) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationMessage = null,
  }) {
    return _then(_$_ShowLiveActivityNotification(
      null == notificationMessage
          ? _value.notificationMessage
          : notificationMessage // ignore: cast_nullable_to_non_nullable
              as NotificationMessage,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $NotificationMessageCopyWith<$Res> get notificationMessage {
    return $NotificationMessageCopyWith<$Res>(_value.notificationMessage,
        (value) {
      return _then(_value.copyWith(notificationMessage: value));
    });
  }
}

/// @nodoc

class _$_ShowLiveActivityNotification
    with DiagnosticableTreeMixin
    implements _ShowLiveActivityNotification {
  const _$_ShowLiveActivityNotification(this.notificationMessage);

  @override
  final NotificationMessage notificationMessage;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingEvent.showLiveActivityNotification(notificationMessage: $notificationMessage)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty(
          'type', 'KoyalMessagingEvent.showLiveActivityNotification'))
      ..add(DiagnosticsProperty('notificationMessage', notificationMessage));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShowLiveActivityNotification &&
            (identical(other.notificationMessage, notificationMessage) ||
                other.notificationMessage == notificationMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notificationMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShowLiveActivityNotificationCopyWith<_$_ShowLiveActivityNotification>
      get copyWith => __$$_ShowLiveActivityNotificationCopyWithImpl<
          _$_ShowLiveActivityNotification>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() breakNotifications,
    required TResult Function(bool resetRetryLimit, bool forceRegister)
        registerPushToken,
    required TResult Function() registerUser,
    required TResult Function() testNotifications,
    required TResult Function() checkTestNotificationRetrievement,
    required TResult Function(NotificationMessage message) received,
    required TResult Function(NotificationMessage message) tapped,
    required TResult Function(NotificationMessage notificationMessage)
        showLiveActivityNotification,
  }) {
    return showLiveActivityNotification(notificationMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? breakNotifications,
    TResult? Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult? Function()? registerUser,
    TResult? Function()? testNotifications,
    TResult? Function()? checkTestNotificationRetrievement,
    TResult? Function(NotificationMessage message)? received,
    TResult? Function(NotificationMessage message)? tapped,
    TResult? Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
  }) {
    return showLiveActivityNotification?.call(notificationMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? breakNotifications,
    TResult Function(bool resetRetryLimit, bool forceRegister)?
        registerPushToken,
    TResult Function()? registerUser,
    TResult Function()? testNotifications,
    TResult Function()? checkTestNotificationRetrievement,
    TResult Function(NotificationMessage message)? received,
    TResult Function(NotificationMessage message)? tapped,
    TResult Function(NotificationMessage notificationMessage)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (showLiveActivityNotification != null) {
      return showLiveActivityNotification(notificationMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_BreakNotifications value) breakNotifications,
    required TResult Function(_RegisterPushToken value) registerPushToken,
    required TResult Function(_RegisterUser value) registerUser,
    required TResult Function(_TestNotifications value) testNotifications,
    required TResult Function(_CheckTestNotificationRetrievement value)
        checkTestNotificationRetrievement,
    required TResult Function(_MessageNotificationReceived value) received,
    required TResult Function(_MessageNotificationTapped value) tapped,
    required TResult Function(_ShowLiveActivityNotification value)
        showLiveActivityNotification,
  }) {
    return showLiveActivityNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_BreakNotifications value)? breakNotifications,
    TResult? Function(_RegisterPushToken value)? registerPushToken,
    TResult? Function(_RegisterUser value)? registerUser,
    TResult? Function(_TestNotifications value)? testNotifications,
    TResult? Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult? Function(_MessageNotificationReceived value)? received,
    TResult? Function(_MessageNotificationTapped value)? tapped,
    TResult? Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
  }) {
    return showLiveActivityNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_BreakNotifications value)? breakNotifications,
    TResult Function(_RegisterPushToken value)? registerPushToken,
    TResult Function(_RegisterUser value)? registerUser,
    TResult Function(_TestNotifications value)? testNotifications,
    TResult Function(_CheckTestNotificationRetrievement value)?
        checkTestNotificationRetrievement,
    TResult Function(_MessageNotificationReceived value)? received,
    TResult Function(_MessageNotificationTapped value)? tapped,
    TResult Function(_ShowLiveActivityNotification value)?
        showLiveActivityNotification,
    required TResult orElse(),
  }) {
    if (showLiveActivityNotification != null) {
      return showLiveActivityNotification(this);
    }
    return orElse();
  }
}

abstract class _ShowLiveActivityNotification implements KoyalMessagingEvent {
  const factory _ShowLiveActivityNotification(
          final NotificationMessage notificationMessage) =
      _$_ShowLiveActivityNotification;

  NotificationMessage get notificationMessage;
  @JsonKey(ignore: true)
  _$$_ShowLiveActivityNotificationCopyWith<_$_ShowLiveActivityNotification>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$KoyalMessagingState {
  NotificationMessage? get tappedMessage => throw _privateConstructorUsedError;
  NotificationMessage? get receivedMessage =>
      throw _privateConstructorUsedError;
  PushTokenStatus get pushTokenStatus => throw _privateConstructorUsedError;
  bool get showDebugSnackbar => throw _privateConstructorUsedError;
  bool get retrievedTestNotification => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $KoyalMessagingStateCopyWith<KoyalMessagingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KoyalMessagingStateCopyWith<$Res> {
  factory $KoyalMessagingStateCopyWith(
          KoyalMessagingState value, $Res Function(KoyalMessagingState) then) =
      _$KoyalMessagingStateCopyWithImpl<$Res, KoyalMessagingState>;
  @useResult
  $Res call(
      {NotificationMessage? tappedMessage,
      NotificationMessage? receivedMessage,
      PushTokenStatus pushTokenStatus,
      bool showDebugSnackbar,
      bool retrievedTestNotification});

  $NotificationMessageCopyWith<$Res>? get tappedMessage;
  $NotificationMessageCopyWith<$Res>? get receivedMessage;
}

/// @nodoc
class _$KoyalMessagingStateCopyWithImpl<$Res, $Val extends KoyalMessagingState>
    implements $KoyalMessagingStateCopyWith<$Res> {
  _$KoyalMessagingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tappedMessage = freezed,
    Object? receivedMessage = freezed,
    Object? pushTokenStatus = null,
    Object? showDebugSnackbar = null,
    Object? retrievedTestNotification = null,
  }) {
    return _then(_value.copyWith(
      tappedMessage: freezed == tappedMessage
          ? _value.tappedMessage
          : tappedMessage // ignore: cast_nullable_to_non_nullable
              as NotificationMessage?,
      receivedMessage: freezed == receivedMessage
          ? _value.receivedMessage
          : receivedMessage // ignore: cast_nullable_to_non_nullable
              as NotificationMessage?,
      pushTokenStatus: null == pushTokenStatus
          ? _value.pushTokenStatus
          : pushTokenStatus // ignore: cast_nullable_to_non_nullable
              as PushTokenStatus,
      showDebugSnackbar: null == showDebugSnackbar
          ? _value.showDebugSnackbar
          : showDebugSnackbar // ignore: cast_nullable_to_non_nullable
              as bool,
      retrievedTestNotification: null == retrievedTestNotification
          ? _value.retrievedTestNotification
          : retrievedTestNotification // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $NotificationMessageCopyWith<$Res>? get tappedMessage {
    if (_value.tappedMessage == null) {
      return null;
    }

    return $NotificationMessageCopyWith<$Res>(_value.tappedMessage!, (value) {
      return _then(_value.copyWith(tappedMessage: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $NotificationMessageCopyWith<$Res>? get receivedMessage {
    if (_value.receivedMessage == null) {
      return null;
    }

    return $NotificationMessageCopyWith<$Res>(_value.receivedMessage!, (value) {
      return _then(_value.copyWith(receivedMessage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_KoyalMessagingStateCopyWith<$Res>
    implements $KoyalMessagingStateCopyWith<$Res> {
  factory _$$_KoyalMessagingStateCopyWith(_$_KoyalMessagingState value,
          $Res Function(_$_KoyalMessagingState) then) =
      __$$_KoyalMessagingStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {NotificationMessage? tappedMessage,
      NotificationMessage? receivedMessage,
      PushTokenStatus pushTokenStatus,
      bool showDebugSnackbar,
      bool retrievedTestNotification});

  @override
  $NotificationMessageCopyWith<$Res>? get tappedMessage;
  @override
  $NotificationMessageCopyWith<$Res>? get receivedMessage;
}

/// @nodoc
class __$$_KoyalMessagingStateCopyWithImpl<$Res>
    extends _$KoyalMessagingStateCopyWithImpl<$Res, _$_KoyalMessagingState>
    implements _$$_KoyalMessagingStateCopyWith<$Res> {
  __$$_KoyalMessagingStateCopyWithImpl(_$_KoyalMessagingState _value,
      $Res Function(_$_KoyalMessagingState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tappedMessage = freezed,
    Object? receivedMessage = freezed,
    Object? pushTokenStatus = null,
    Object? showDebugSnackbar = null,
    Object? retrievedTestNotification = null,
  }) {
    return _then(_$_KoyalMessagingState(
      tappedMessage: freezed == tappedMessage
          ? _value.tappedMessage
          : tappedMessage // ignore: cast_nullable_to_non_nullable
              as NotificationMessage?,
      receivedMessage: freezed == receivedMessage
          ? _value.receivedMessage
          : receivedMessage // ignore: cast_nullable_to_non_nullable
              as NotificationMessage?,
      pushTokenStatus: null == pushTokenStatus
          ? _value.pushTokenStatus
          : pushTokenStatus // ignore: cast_nullable_to_non_nullable
              as PushTokenStatus,
      showDebugSnackbar: null == showDebugSnackbar
          ? _value.showDebugSnackbar
          : showDebugSnackbar // ignore: cast_nullable_to_non_nullable
              as bool,
      retrievedTestNotification: null == retrievedTestNotification
          ? _value.retrievedTestNotification
          : retrievedTestNotification // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_KoyalMessagingState extends _KoyalMessagingState
    with DiagnosticableTreeMixin {
  _$_KoyalMessagingState(
      {this.tappedMessage,
      this.receivedMessage,
      this.pushTokenStatus = PushTokenStatus.notRegistered,
      this.showDebugSnackbar = false,
      this.retrievedTestNotification = false})
      : super._();

  @override
  final NotificationMessage? tappedMessage;
  @override
  final NotificationMessage? receivedMessage;
  @override
  @JsonKey()
  final PushTokenStatus pushTokenStatus;
  @override
  @JsonKey()
  final bool showDebugSnackbar;
  @override
  @JsonKey()
  final bool retrievedTestNotification;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'KoyalMessagingState(tappedMessage: $tappedMessage, receivedMessage: $receivedMessage, pushTokenStatus: $pushTokenStatus, showDebugSnackbar: $showDebugSnackbar, retrievedTestNotification: $retrievedTestNotification)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'KoyalMessagingState'))
      ..add(DiagnosticsProperty('tappedMessage', tappedMessage))
      ..add(DiagnosticsProperty('receivedMessage', receivedMessage))
      ..add(DiagnosticsProperty('pushTokenStatus', pushTokenStatus))
      ..add(DiagnosticsProperty('showDebugSnackbar', showDebugSnackbar))
      ..add(DiagnosticsProperty(
          'retrievedTestNotification', retrievedTestNotification));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_KoyalMessagingState &&
            (identical(other.tappedMessage, tappedMessage) ||
                other.tappedMessage == tappedMessage) &&
            (identical(other.receivedMessage, receivedMessage) ||
                other.receivedMessage == receivedMessage) &&
            (identical(other.pushTokenStatus, pushTokenStatus) ||
                other.pushTokenStatus == pushTokenStatus) &&
            (identical(other.showDebugSnackbar, showDebugSnackbar) ||
                other.showDebugSnackbar == showDebugSnackbar) &&
            (identical(other.retrievedTestNotification,
                    retrievedTestNotification) ||
                other.retrievedTestNotification == retrievedTestNotification));
  }

  @override
  int get hashCode => Object.hash(runtimeType, tappedMessage, receivedMessage,
      pushTokenStatus, showDebugSnackbar, retrievedTestNotification);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_KoyalMessagingStateCopyWith<_$_KoyalMessagingState> get copyWith =>
      __$$_KoyalMessagingStateCopyWithImpl<_$_KoyalMessagingState>(
          this, _$identity);
}

abstract class _KoyalMessagingState extends KoyalMessagingState {
  factory _KoyalMessagingState(
      {final NotificationMessage? tappedMessage,
      final NotificationMessage? receivedMessage,
      final PushTokenStatus pushTokenStatus,
      final bool showDebugSnackbar,
      final bool retrievedTestNotification}) = _$_KoyalMessagingState;
  _KoyalMessagingState._() : super._();

  @override
  NotificationMessage? get tappedMessage;
  @override
  NotificationMessage? get receivedMessage;
  @override
  PushTokenStatus get pushTokenStatus;
  @override
  bool get showDebugSnackbar;
  @override
  bool get retrievedTestNotification;
  @override
  @JsonKey(ignore: true)
  _$$_KoyalMessagingStateCopyWith<_$_KoyalMessagingState> get copyWith =>
      throw _privateConstructorUsedError;
}
