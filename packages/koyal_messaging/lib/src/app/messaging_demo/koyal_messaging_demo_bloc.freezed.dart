// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'koyal_messaging_demo_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$KoyalMessagingDemoEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KoyalMessagingDemoEventCopyWith<$Res> {
  factory $KoyalMessagingDemoEventCopyWith(KoyalMessagingDemoEvent value,
          $Res Function(KoyalMessagingDemoEvent) then) =
      _$KoyalMessagingDemoEventCopyWithImpl<$Res, KoyalMessagingDemoEvent>;
}

/// @nodoc
class _$KoyalMessagingDemoEventCopyWithImpl<$Res,
        $Val extends KoyalMessagingDemoEvent>
    implements $KoyalMessagingDemoEventCopyWith<$Res> {
  _$KoyalMessagingDemoEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitCopyWith<$Res> {
  factory _$$_InitCopyWith(_$_Init value, $Res Function(_$_Init) then) =
      __$$_InitCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res, _$_Init>
    implements _$$_InitCopyWith<$Res> {
  __$$_InitCopyWithImpl(_$_Init _value, $Res Function(_$_Init) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Init implements _Init {
  const _$_Init();

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.init()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Init);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return init();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return init?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return init?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class _Init implements KoyalMessagingDemoEvent {
  const factory _Init() = _$_Init;
}

/// @nodoc
abstract class _$$_SendTestNotificationByTypeCopyWith<$Res> {
  factory _$$_SendTestNotificationByTypeCopyWith(
          _$_SendTestNotificationByType value,
          $Res Function(_$_SendTestNotificationByType) then) =
      __$$_SendTestNotificationByTypeCopyWithImpl<$Res>;
  @useResult
  $Res call({MessageType type});
}

/// @nodoc
class __$$_SendTestNotificationByTypeCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res,
        _$_SendTestNotificationByType>
    implements _$$_SendTestNotificationByTypeCopyWith<$Res> {
  __$$_SendTestNotificationByTypeCopyWithImpl(
      _$_SendTestNotificationByType _value,
      $Res Function(_$_SendTestNotificationByType) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
  }) {
    return _then(_$_SendTestNotificationByType(
      null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as MessageType,
    ));
  }
}

/// @nodoc

class _$_SendTestNotificationByType implements _SendTestNotificationByType {
  const _$_SendTestNotificationByType(this.type);

  @override
  final MessageType type;

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.sendTestNotificationByType(type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SendTestNotificationByType &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SendTestNotificationByTypeCopyWith<_$_SendTestNotificationByType>
      get copyWith => __$$_SendTestNotificationByTypeCopyWithImpl<
          _$_SendTestNotificationByType>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return sendTestNotificationByType(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return sendTestNotificationByType?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (sendTestNotificationByType != null) {
      return sendTestNotificationByType(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return sendTestNotificationByType(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return sendTestNotificationByType?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (sendTestNotificationByType != null) {
      return sendTestNotificationByType(this);
    }
    return orElse();
  }
}

abstract class _SendTestNotificationByType implements KoyalMessagingDemoEvent {
  const factory _SendTestNotificationByType(final MessageType type) =
      _$_SendTestNotificationByType;

  MessageType get type;
  @JsonKey(ignore: true)
  _$$_SendTestNotificationByTypeCopyWith<_$_SendTestNotificationByType>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ShowLocalNotificationByTypeCopyWith<$Res> {
  factory _$$_ShowLocalNotificationByTypeCopyWith(
          _$_ShowLocalNotificationByType value,
          $Res Function(_$_ShowLocalNotificationByType) then) =
      __$$_ShowLocalNotificationByTypeCopyWithImpl<$Res>;
  @useResult
  $Res call({MessageType type});
}

/// @nodoc
class __$$_ShowLocalNotificationByTypeCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res,
        _$_ShowLocalNotificationByType>
    implements _$$_ShowLocalNotificationByTypeCopyWith<$Res> {
  __$$_ShowLocalNotificationByTypeCopyWithImpl(
      _$_ShowLocalNotificationByType _value,
      $Res Function(_$_ShowLocalNotificationByType) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
  }) {
    return _then(_$_ShowLocalNotificationByType(
      null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as MessageType,
    ));
  }
}

/// @nodoc

class _$_ShowLocalNotificationByType implements _ShowLocalNotificationByType {
  const _$_ShowLocalNotificationByType(this.type);

  @override
  final MessageType type;

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.showLocalNotificationByType(type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShowLocalNotificationByType &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShowLocalNotificationByTypeCopyWith<_$_ShowLocalNotificationByType>
      get copyWith => __$$_ShowLocalNotificationByTypeCopyWithImpl<
          _$_ShowLocalNotificationByType>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return showLocalNotificationByType(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return showLocalNotificationByType?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (showLocalNotificationByType != null) {
      return showLocalNotificationByType(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return showLocalNotificationByType(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return showLocalNotificationByType?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (showLocalNotificationByType != null) {
      return showLocalNotificationByType(this);
    }
    return orElse();
  }
}

abstract class _ShowLocalNotificationByType implements KoyalMessagingDemoEvent {
  const factory _ShowLocalNotificationByType(final MessageType type) =
      _$_ShowLocalNotificationByType;

  MessageType get type;
  @JsonKey(ignore: true)
  _$$_ShowLocalNotificationByTypeCopyWith<_$_ShowLocalNotificationByType>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ShowNotificationQueueCopyWith<$Res> {
  factory _$$_ShowNotificationQueueCopyWith(_$_ShowNotificationQueue value,
          $Res Function(_$_ShowNotificationQueue) then) =
      __$$_ShowNotificationQueueCopyWithImpl<$Res>;
  @useResult
  $Res call({MessageType type});
}

/// @nodoc
class __$$_ShowNotificationQueueCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res,
        _$_ShowNotificationQueue>
    implements _$$_ShowNotificationQueueCopyWith<$Res> {
  __$$_ShowNotificationQueueCopyWithImpl(_$_ShowNotificationQueue _value,
      $Res Function(_$_ShowNotificationQueue) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
  }) {
    return _then(_$_ShowNotificationQueue(
      null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as MessageType,
    ));
  }
}

/// @nodoc

class _$_ShowNotificationQueue implements _ShowNotificationQueue {
  const _$_ShowNotificationQueue(this.type);

  @override
  final MessageType type;

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.showNotificationQueue(type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShowNotificationQueue &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShowNotificationQueueCopyWith<_$_ShowNotificationQueue> get copyWith =>
      __$$_ShowNotificationQueueCopyWithImpl<_$_ShowNotificationQueue>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return showNotificationQueue(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return showNotificationQueue?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (showNotificationQueue != null) {
      return showNotificationQueue(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return showNotificationQueue(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return showNotificationQueue?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (showNotificationQueue != null) {
      return showNotificationQueue(this);
    }
    return orElse();
  }
}

abstract class _ShowNotificationQueue implements KoyalMessagingDemoEvent {
  const factory _ShowNotificationQueue(final MessageType type) =
      _$_ShowNotificationQueue;

  MessageType get type;
  @JsonKey(ignore: true)
  _$$_ShowNotificationQueueCopyWith<_$_ShowNotificationQueue> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ChangeAsDataMessageCopyWith<$Res> {
  factory _$$_ChangeAsDataMessageCopyWith(_$_ChangeAsDataMessage value,
          $Res Function(_$_ChangeAsDataMessage) then) =
      __$$_ChangeAsDataMessageCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$_ChangeAsDataMessageCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res, _$_ChangeAsDataMessage>
    implements _$$_ChangeAsDataMessageCopyWith<$Res> {
  __$$_ChangeAsDataMessageCopyWithImpl(_$_ChangeAsDataMessage _value,
      $Res Function(_$_ChangeAsDataMessage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$_ChangeAsDataMessage(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_ChangeAsDataMessage implements _ChangeAsDataMessage {
  const _$_ChangeAsDataMessage({required this.value});

  @override
  final bool value;

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.changeAsDataMessage(value: $value)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ChangeAsDataMessage &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChangeAsDataMessageCopyWith<_$_ChangeAsDataMessage> get copyWith =>
      __$$_ChangeAsDataMessageCopyWithImpl<_$_ChangeAsDataMessage>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return changeAsDataMessage(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return changeAsDataMessage?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (changeAsDataMessage != null) {
      return changeAsDataMessage(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return changeAsDataMessage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return changeAsDataMessage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (changeAsDataMessage != null) {
      return changeAsDataMessage(this);
    }
    return orElse();
  }
}

abstract class _ChangeAsDataMessage implements KoyalMessagingDemoEvent {
  const factory _ChangeAsDataMessage({required final bool value}) =
      _$_ChangeAsDataMessage;

  bool get value;
  @JsonKey(ignore: true)
  _$$_ChangeAsDataMessageCopyWith<_$_ChangeAsDataMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_AddCustomFieldCopyWith<$Res> {
  factory _$$_AddCustomFieldCopyWith(
          _$_AddCustomField value, $Res Function(_$_AddCustomField) then) =
      __$$_AddCustomFieldCopyWithImpl<$Res>;
  @useResult
  $Res call({Tuple2<String, String>? value});
}

/// @nodoc
class __$$_AddCustomFieldCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res, _$_AddCustomField>
    implements _$$_AddCustomFieldCopyWith<$Res> {
  __$$_AddCustomFieldCopyWithImpl(
      _$_AddCustomField _value, $Res Function(_$_AddCustomField) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
  }) {
    return _then(_$_AddCustomField(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as Tuple2<String, String>?,
    ));
  }
}

/// @nodoc

class _$_AddCustomField implements _AddCustomField {
  const _$_AddCustomField({this.value});

  @override
  final Tuple2<String, String>? value;

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.addCustomField(value: $value)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AddCustomField &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AddCustomFieldCopyWith<_$_AddCustomField> get copyWith =>
      __$$_AddCustomFieldCopyWithImpl<_$_AddCustomField>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return addCustomField(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return addCustomField?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (addCustomField != null) {
      return addCustomField(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return addCustomField(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return addCustomField?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (addCustomField != null) {
      return addCustomField(this);
    }
    return orElse();
  }
}

abstract class _AddCustomField implements KoyalMessagingDemoEvent {
  const factory _AddCustomField({final Tuple2<String, String>? value}) =
      _$_AddCustomField;

  Tuple2<String, String>? get value;
  @JsonKey(ignore: true)
  _$$_AddCustomFieldCopyWith<_$_AddCustomField> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_PreparedMessageCopyWith<$Res> {
  factory _$$_PreparedMessageCopyWith(
          _$_PreparedMessage value, $Res Function(_$_PreparedMessage) then) =
      __$$_PreparedMessageCopyWithImpl<$Res>;
  @useResult
  $Res call({PreparedDemoMessage? type});
}

/// @nodoc
class __$$_PreparedMessageCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res, _$_PreparedMessage>
    implements _$$_PreparedMessageCopyWith<$Res> {
  __$$_PreparedMessageCopyWithImpl(
      _$_PreparedMessage _value, $Res Function(_$_PreparedMessage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
  }) {
    return _then(_$_PreparedMessage(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as PreparedDemoMessage?,
    ));
  }
}

/// @nodoc

class _$_PreparedMessage implements _PreparedMessage {
  const _$_PreparedMessage({this.type});

  @override
  final PreparedDemoMessage? type;

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.preparedMessage(type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PreparedMessage &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PreparedMessageCopyWith<_$_PreparedMessage> get copyWith =>
      __$$_PreparedMessageCopyWithImpl<_$_PreparedMessage>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return preparedMessage(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return preparedMessage?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (preparedMessage != null) {
      return preparedMessage(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return preparedMessage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return preparedMessage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (preparedMessage != null) {
      return preparedMessage(this);
    }
    return orElse();
  }
}

abstract class _PreparedMessage implements KoyalMessagingDemoEvent {
  const factory _PreparedMessage({final PreparedDemoMessage? type}) =
      _$_PreparedMessage;

  PreparedDemoMessage? get type;
  @JsonKey(ignore: true)
  _$$_PreparedMessageCopyWith<_$_PreparedMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_UpdateCustomFieldCopyWith<$Res> {
  factory _$$_UpdateCustomFieldCopyWith(_$_UpdateCustomField value,
          $Res Function(_$_UpdateCustomField) then) =
      __$$_UpdateCustomFieldCopyWithImpl<$Res>;
  @useResult
  $Res call({int? index, Tuple2<String, String>? value});
}

/// @nodoc
class __$$_UpdateCustomFieldCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res, _$_UpdateCustomField>
    implements _$$_UpdateCustomFieldCopyWith<$Res> {
  __$$_UpdateCustomFieldCopyWithImpl(
      _$_UpdateCustomField _value, $Res Function(_$_UpdateCustomField) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = freezed,
    Object? value = freezed,
  }) {
    return _then(_$_UpdateCustomField(
      index: freezed == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as Tuple2<String, String>?,
    ));
  }
}

/// @nodoc

class _$_UpdateCustomField implements _UpdateCustomField {
  const _$_UpdateCustomField({this.index, this.value});

  @override
  final int? index;
  @override
  final Tuple2<String, String>? value;

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.updateCustomField(index: $index, value: $value)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UpdateCustomField &&
            (identical(other.index, index) || other.index == index) &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, index, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UpdateCustomFieldCopyWith<_$_UpdateCustomField> get copyWith =>
      __$$_UpdateCustomFieldCopyWithImpl<_$_UpdateCustomField>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return updateCustomField(index, value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return updateCustomField?.call(index, value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (updateCustomField != null) {
      return updateCustomField(index, value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return updateCustomField(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return updateCustomField?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (updateCustomField != null) {
      return updateCustomField(this);
    }
    return orElse();
  }
}

abstract class _UpdateCustomField implements KoyalMessagingDemoEvent {
  const factory _UpdateCustomField(
      {final int? index,
      final Tuple2<String, String>? value}) = _$_UpdateCustomField;

  int? get index;
  Tuple2<String, String>? get value;
  @JsonKey(ignore: true)
  _$$_UpdateCustomFieldCopyWith<_$_UpdateCustomField> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ChangeTitleCopyWith<$Res> {
  factory _$$_ChangeTitleCopyWith(
          _$_ChangeTitle value, $Res Function(_$_ChangeTitle) then) =
      __$$_ChangeTitleCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$_ChangeTitleCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res, _$_ChangeTitle>
    implements _$$_ChangeTitleCopyWith<$Res> {
  __$$_ChangeTitleCopyWithImpl(
      _$_ChangeTitle _value, $Res Function(_$_ChangeTitle) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$_ChangeTitle(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_ChangeTitle implements _ChangeTitle {
  const _$_ChangeTitle(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.changeTitle(value: $value)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ChangeTitle &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChangeTitleCopyWith<_$_ChangeTitle> get copyWith =>
      __$$_ChangeTitleCopyWithImpl<_$_ChangeTitle>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return changeTitle(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return changeTitle?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (changeTitle != null) {
      return changeTitle(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return changeTitle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return changeTitle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (changeTitle != null) {
      return changeTitle(this);
    }
    return orElse();
  }
}

abstract class _ChangeTitle implements KoyalMessagingDemoEvent {
  const factory _ChangeTitle(final String value) = _$_ChangeTitle;

  String get value;
  @JsonKey(ignore: true)
  _$$_ChangeTitleCopyWith<_$_ChangeTitle> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ChangeBodyCopyWith<$Res> {
  factory _$$_ChangeBodyCopyWith(
          _$_ChangeBody value, $Res Function(_$_ChangeBody) then) =
      __$$_ChangeBodyCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$_ChangeBodyCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res, _$_ChangeBody>
    implements _$$_ChangeBodyCopyWith<$Res> {
  __$$_ChangeBodyCopyWithImpl(
      _$_ChangeBody _value, $Res Function(_$_ChangeBody) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$_ChangeBody(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_ChangeBody implements _ChangeBody {
  const _$_ChangeBody(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.changeBody(value: $value)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ChangeBody &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChangeBodyCopyWith<_$_ChangeBody> get copyWith =>
      __$$_ChangeBodyCopyWithImpl<_$_ChangeBody>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return changeBody(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return changeBody?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (changeBody != null) {
      return changeBody(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return changeBody(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return changeBody?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (changeBody != null) {
      return changeBody(this);
    }
    return orElse();
  }
}

abstract class _ChangeBody implements KoyalMessagingDemoEvent {
  const factory _ChangeBody(final String value) = _$_ChangeBody;

  String get value;
  @JsonKey(ignore: true)
  _$$_ChangeBodyCopyWith<_$_ChangeBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SendCustomNotificationCopyWith<$Res> {
  factory _$$_SendCustomNotificationCopyWith(_$_SendCustomNotification value,
          $Res Function(_$_SendCustomNotification) then) =
      __$$_SendCustomNotificationCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SendCustomNotificationCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res,
        _$_SendCustomNotification>
    implements _$$_SendCustomNotificationCopyWith<$Res> {
  __$$_SendCustomNotificationCopyWithImpl(_$_SendCustomNotification _value,
      $Res Function(_$_SendCustomNotification) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SendCustomNotification implements _SendCustomNotification {
  const _$_SendCustomNotification();

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.sendCustomNotification()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SendCustomNotification);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return sendCustomNotification();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return sendCustomNotification?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (sendCustomNotification != null) {
      return sendCustomNotification();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return sendCustomNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return sendCustomNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (sendCustomNotification != null) {
      return sendCustomNotification(this);
    }
    return orElse();
  }
}

abstract class _SendCustomNotification implements KoyalMessagingDemoEvent {
  const factory _SendCustomNotification() = _$_SendCustomNotification;
}

/// @nodoc
abstract class _$$_MarkTokenAsExpiredCopyWith<$Res> {
  factory _$$_MarkTokenAsExpiredCopyWith(_$_MarkTokenAsExpired value,
          $Res Function(_$_MarkTokenAsExpired) then) =
      __$$_MarkTokenAsExpiredCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_MarkTokenAsExpiredCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoEventCopyWithImpl<$Res, _$_MarkTokenAsExpired>
    implements _$$_MarkTokenAsExpiredCopyWith<$Res> {
  __$$_MarkTokenAsExpiredCopyWithImpl(
      _$_MarkTokenAsExpired _value, $Res Function(_$_MarkTokenAsExpired) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_MarkTokenAsExpired implements _MarkTokenAsExpired {
  const _$_MarkTokenAsExpired();

  @override
  String toString() {
    return 'KoyalMessagingDemoEvent.markTokenAsExpired()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_MarkTokenAsExpired);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(MessageType type) sendTestNotificationByType,
    required TResult Function(MessageType type) showLocalNotificationByType,
    required TResult Function(MessageType type) showNotificationQueue,
    required TResult Function(bool value) changeAsDataMessage,
    required TResult Function(Tuple2<String, String>? value) addCustomField,
    required TResult Function(PreparedDemoMessage? type) preparedMessage,
    required TResult Function(int? index, Tuple2<String, String>? value)
        updateCustomField,
    required TResult Function(String value) changeTitle,
    required TResult Function(String value) changeBody,
    required TResult Function() sendCustomNotification,
    required TResult Function() markTokenAsExpired,
  }) {
    return markTokenAsExpired();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(MessageType type)? sendTestNotificationByType,
    TResult? Function(MessageType type)? showLocalNotificationByType,
    TResult? Function(MessageType type)? showNotificationQueue,
    TResult? Function(bool value)? changeAsDataMessage,
    TResult? Function(Tuple2<String, String>? value)? addCustomField,
    TResult? Function(PreparedDemoMessage? type)? preparedMessage,
    TResult? Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult? Function(String value)? changeTitle,
    TResult? Function(String value)? changeBody,
    TResult? Function()? sendCustomNotification,
    TResult? Function()? markTokenAsExpired,
  }) {
    return markTokenAsExpired?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(MessageType type)? sendTestNotificationByType,
    TResult Function(MessageType type)? showLocalNotificationByType,
    TResult Function(MessageType type)? showNotificationQueue,
    TResult Function(bool value)? changeAsDataMessage,
    TResult Function(Tuple2<String, String>? value)? addCustomField,
    TResult Function(PreparedDemoMessage? type)? preparedMessage,
    TResult Function(int? index, Tuple2<String, String>? value)?
        updateCustomField,
    TResult Function(String value)? changeTitle,
    TResult Function(String value)? changeBody,
    TResult Function()? sendCustomNotification,
    TResult Function()? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (markTokenAsExpired != null) {
      return markTokenAsExpired();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_SendTestNotificationByType value)
        sendTestNotificationByType,
    required TResult Function(_ShowLocalNotificationByType value)
        showLocalNotificationByType,
    required TResult Function(_ShowNotificationQueue value)
        showNotificationQueue,
    required TResult Function(_ChangeAsDataMessage value) changeAsDataMessage,
    required TResult Function(_AddCustomField value) addCustomField,
    required TResult Function(_PreparedMessage value) preparedMessage,
    required TResult Function(_UpdateCustomField value) updateCustomField,
    required TResult Function(_ChangeTitle value) changeTitle,
    required TResult Function(_ChangeBody value) changeBody,
    required TResult Function(_SendCustomNotification value)
        sendCustomNotification,
    required TResult Function(_MarkTokenAsExpired value) markTokenAsExpired,
  }) {
    return markTokenAsExpired(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult? Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult? Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult? Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult? Function(_AddCustomField value)? addCustomField,
    TResult? Function(_PreparedMessage value)? preparedMessage,
    TResult? Function(_UpdateCustomField value)? updateCustomField,
    TResult? Function(_ChangeTitle value)? changeTitle,
    TResult? Function(_ChangeBody value)? changeBody,
    TResult? Function(_SendCustomNotification value)? sendCustomNotification,
    TResult? Function(_MarkTokenAsExpired value)? markTokenAsExpired,
  }) {
    return markTokenAsExpired?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_SendTestNotificationByType value)?
        sendTestNotificationByType,
    TResult Function(_ShowLocalNotificationByType value)?
        showLocalNotificationByType,
    TResult Function(_ShowNotificationQueue value)? showNotificationQueue,
    TResult Function(_ChangeAsDataMessage value)? changeAsDataMessage,
    TResult Function(_AddCustomField value)? addCustomField,
    TResult Function(_PreparedMessage value)? preparedMessage,
    TResult Function(_UpdateCustomField value)? updateCustomField,
    TResult Function(_ChangeTitle value)? changeTitle,
    TResult Function(_ChangeBody value)? changeBody,
    TResult Function(_SendCustomNotification value)? sendCustomNotification,
    TResult Function(_MarkTokenAsExpired value)? markTokenAsExpired,
    required TResult orElse(),
  }) {
    if (markTokenAsExpired != null) {
      return markTokenAsExpired(this);
    }
    return orElse();
  }
}

abstract class _MarkTokenAsExpired implements KoyalMessagingDemoEvent {
  const factory _MarkTokenAsExpired() = _$_MarkTokenAsExpired;
}

/// @nodoc
mixin _$KoyalMessagingDemoState {
  LoadingState? get loadingState => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get body => throw _privateConstructorUsedError;
  bool get sendAsDataMessage => throw _privateConstructorUsedError;
  List<Tuple2<String, String>>? get customKeys =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $KoyalMessagingDemoStateCopyWith<KoyalMessagingDemoState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KoyalMessagingDemoStateCopyWith<$Res> {
  factory $KoyalMessagingDemoStateCopyWith(KoyalMessagingDemoState value,
          $Res Function(KoyalMessagingDemoState) then) =
      _$KoyalMessagingDemoStateCopyWithImpl<$Res, KoyalMessagingDemoState>;
  @useResult
  $Res call(
      {LoadingState? loadingState,
      String? title,
      String? body,
      bool sendAsDataMessage,
      List<Tuple2<String, String>>? customKeys});
}

/// @nodoc
class _$KoyalMessagingDemoStateCopyWithImpl<$Res,
        $Val extends KoyalMessagingDemoState>
    implements $KoyalMessagingDemoStateCopyWith<$Res> {
  _$KoyalMessagingDemoStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = freezed,
    Object? title = freezed,
    Object? body = freezed,
    Object? sendAsDataMessage = null,
    Object? customKeys = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: freezed == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      body: freezed == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String?,
      sendAsDataMessage: null == sendAsDataMessage
          ? _value.sendAsDataMessage
          : sendAsDataMessage // ignore: cast_nullable_to_non_nullable
              as bool,
      customKeys: freezed == customKeys
          ? _value.customKeys
          : customKeys // ignore: cast_nullable_to_non_nullable
              as List<Tuple2<String, String>>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MessagingStateCopyWith<$Res>
    implements $KoyalMessagingDemoStateCopyWith<$Res> {
  factory _$$_MessagingStateCopyWith(
          _$_MessagingState value, $Res Function(_$_MessagingState) then) =
      __$$_MessagingStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState? loadingState,
      String? title,
      String? body,
      bool sendAsDataMessage,
      List<Tuple2<String, String>>? customKeys});
}

/// @nodoc
class __$$_MessagingStateCopyWithImpl<$Res>
    extends _$KoyalMessagingDemoStateCopyWithImpl<$Res, _$_MessagingState>
    implements _$$_MessagingStateCopyWith<$Res> {
  __$$_MessagingStateCopyWithImpl(
      _$_MessagingState _value, $Res Function(_$_MessagingState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = freezed,
    Object? title = freezed,
    Object? body = freezed,
    Object? sendAsDataMessage = null,
    Object? customKeys = freezed,
  }) {
    return _then(_$_MessagingState(
      loadingState: freezed == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      body: freezed == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String?,
      sendAsDataMessage: null == sendAsDataMessage
          ? _value.sendAsDataMessage
          : sendAsDataMessage // ignore: cast_nullable_to_non_nullable
              as bool,
      customKeys: freezed == customKeys
          ? _value._customKeys
          : customKeys // ignore: cast_nullable_to_non_nullable
              as List<Tuple2<String, String>>?,
    ));
  }
}

/// @nodoc

class _$_MessagingState extends _MessagingState {
  _$_MessagingState(
      {this.loadingState,
      this.title,
      this.body,
      this.sendAsDataMessage = false,
      final List<Tuple2<String, String>>? customKeys})
      : _customKeys = customKeys,
        super._();

  @override
  final LoadingState? loadingState;
  @override
  final String? title;
  @override
  final String? body;
  @override
  @JsonKey()
  final bool sendAsDataMessage;
  final List<Tuple2<String, String>>? _customKeys;
  @override
  List<Tuple2<String, String>>? get customKeys {
    final value = _customKeys;
    if (value == null) return null;
    if (_customKeys is EqualUnmodifiableListView) return _customKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'KoyalMessagingDemoState(loadingState: $loadingState, title: $title, body: $body, sendAsDataMessage: $sendAsDataMessage, customKeys: $customKeys)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MessagingState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.sendAsDataMessage, sendAsDataMessage) ||
                other.sendAsDataMessage == sendAsDataMessage) &&
            const DeepCollectionEquality()
                .equals(other._customKeys, _customKeys));
  }

  @override
  int get hashCode => Object.hash(runtimeType, loadingState, title, body,
      sendAsDataMessage, const DeepCollectionEquality().hash(_customKeys));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MessagingStateCopyWith<_$_MessagingState> get copyWith =>
      __$$_MessagingStateCopyWithImpl<_$_MessagingState>(this, _$identity);
}

abstract class _MessagingState extends KoyalMessagingDemoState {
  factory _MessagingState(
      {final LoadingState? loadingState,
      final String? title,
      final String? body,
      final bool sendAsDataMessage,
      final List<Tuple2<String, String>>? customKeys}) = _$_MessagingState;
  _MessagingState._() : super._();

  @override
  LoadingState? get loadingState;
  @override
  String? get title;
  @override
  String? get body;
  @override
  bool get sendAsDataMessage;
  @override
  List<Tuple2<String, String>>? get customKeys;
  @override
  @JsonKey(ignore: true)
  _$$_MessagingStateCopyWith<_$_MessagingState> get copyWith =>
      throw _privateConstructorUsedError;
}
