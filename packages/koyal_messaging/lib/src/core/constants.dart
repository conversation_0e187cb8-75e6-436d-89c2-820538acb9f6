class MessageDataKeys {
  // Generic
  static const String notificationTypeDataKey = 'notificationType';
  static const String deeplinkDataKey = 'deeplink';
  static const String imageDataKey = 'FCM_IMAGE';
  static const String msidDataKey = 'MSID';
  static const String screenLabelDataKey = 'SCREEN_LABEL';
  static const String urlToOpenDataKey = 'URL_TO_OPEN';
  static const String titleDataKey = 'title';
  static const String bodyDataKey = 'body';
  static const String sasTemplateId = 'SAS_TEMPLATE_ID';
  static const String cuid = 'CUID';
  static const String contractNumber = 'CONTRACT_NUM';

  // Id keys - CAPP
  static const String inboxIdDataKey = 'inboxItemId';
  static const String productIdDataKey = 'productId';
  static const String rewardIdDataKey = 'rewardId';
  static const String orderIdDataKey = 'orderId';
  static const String logoutUserId = 'LogoutUserId';

  // Id keys - MAPP
  static const String userIdDataKey = 'userId';
  static const String posIdDataKey = 'posId';

  // Tracking keys
  static const String questTrackingNameDataKey = 'questTrackingName';
  static const String trackingNameDataKey = 'trackingName';
  static const String traceIdDataKey = 'traceId';
  // LevelUp Keys
  static const String imageUrlDataKey = 'imageUrl';
  static const String rewardEligibleDataKey = 'rewardEligible';
  static const String newLevelDataKey = 'newLevel';
  static const String xpAwardedDataKey = 'xpAwarded';
  static const String orderStateDataKey = 'orderState';

  // Others
  static const String waitForTypeDataKey = 'waitForType';
  static const String flowStepDataKey = 'flowStep';
  static const String phoneUpdateApproved = 'Approved';
  static const String phoneUpdateNewPhoneNumber = 'NewPhoneNumber';
  static const String transactionIdKey = 'trxID';

  // live activities
  static const String flowTypeDataKey = 'type';
  static const String statusDataKey = 'status';
  static const String liveActivityEventDataKey = 'liveActivityEvent';
  static const String timeToWaitingDataKey = 'timeToWaiting';
  static const String ctaDataKey = 'cta';

  // welcome screen offer
  static const String gmaInAppType = 'GMA_INAPP_TYPE';
  static const String gmaInAppPayLoad = 'GMA_INAPP_VAR_PAYLOAD';
  static const String gmaProductName = 'GMA_PRODUCT_NAME';
  static const String gmaProductType = 'GMA_PRODUCT_TYPE';

  // In app messages
  static const String gmaInApp = 'GMA_INAPP';
  static const String gmaTemplateId = 'GMA_TEMPLATE_ID';
  static const String gmaExpiresTimestamp = 'expires';
}

const String messagingDebugApiInstanceName = 'koyal_messaging_instance_debug_api';
const String notifyDebugApiInstanceName = 'koyal_messaging_instance_notification_api';

// Peridoc resend constants
const Duration periodicResendDelay = Duration(seconds: 10);
const int registrationRetryLimit = 3;
const Duration testNotificationRetrievementCheckDelay = Duration(minutes: 2);
