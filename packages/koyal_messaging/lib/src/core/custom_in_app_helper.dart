import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:decimal/decimal.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

class InAppMessagingService {
  InAppMessagingService({required this.featureFlagRepository});
  final IFeatureFlagRepository featureFlagRepository;

  bool shouldShowInAppMsgPersonalized(String? inAppType) =>
      featureFlagRepository.isEnabledCached(FeatureFlag.inAppMsgPersonalized) &&
      inAppType.getInAppMessageType() == InAppMessageType.inAppPersonalized;

  bool shouldShowWelcomeScreenInAppMsg(String? inAppType) =>
      featureFlagRepository.isEnabledCached(FeatureFlag.welcomeScreenOffer) &&
      inAppType.getInAppMessageType() == InAppMessageType.welcomeScreen;

  CustomInApp? handlePersonalizedData(CustomInApp inAppData, Map<String, dynamic> offerPayLoad) {
    var titleResult = '';
    var bodyTextResult = '';
    var ctaButtonTextResult = '';
    var ctaButtonUrlResult = '';
    var tertiaryCtaButtonTextResult = '';

    if (inAppData.title?.isNotEmpty == true) {
      final title = _replacePlaceholders(offerPayLoad, inAppData.title!) ?? '';
      if (title.isEmpty) {
        return null;
      }
      titleResult = title;
    }

    if (inAppData.bodyText?.isNotEmpty == true) {
      final bodyText = _replacePlaceholders(offerPayLoad, inAppData.bodyText!) ?? '';
      if (bodyText.isEmpty) {
        return null;
      }
      bodyTextResult = bodyText;
    }

    if (inAppData.ctaButtonText?.isNotEmpty == true) {
      final ctaButtonText = _replacePlaceholders(offerPayLoad, inAppData.ctaButtonText!) ?? '';
      if (ctaButtonText.isEmpty) {
        return null;
      }
      ctaButtonTextResult = ctaButtonText;
    }

    if (inAppData.ctaButtonUrl?.isNotEmpty == true) {
      final ctaButtonUrl = _replacePlaceholders(offerPayLoad, inAppData.ctaButtonUrl!) ?? '';
      if (ctaButtonUrl.isEmpty) {
        return null;
      }
      ctaButtonUrlResult = ctaButtonUrl;
    }

    if (inAppData.tertiaryCtaButtonText?.isNotEmpty == true) {
      final tertiaryCtaButtonText = _replacePlaceholders(offerPayLoad, inAppData.tertiaryCtaButtonText!) ?? '';
      if (tertiaryCtaButtonText.isEmpty) {
        return null;
      }
      tertiaryCtaButtonTextResult = tertiaryCtaButtonText;
    }

    return CustomInApp(
      id: inAppData.id,
      templateId: inAppData.templateId,
      title: titleResult,
      bodyText: bodyTextResult,
      imageId: inAppData.imageId,
      ctaButtonText: ctaButtonTextResult,
      ctaButtonUrl: ctaButtonUrlResult,
      tertiaryCtaButtonText: tertiaryCtaButtonTextResult,
    );
  }

  String? _replacePlaceholders(Map<String, dynamic> offerPayLoad, String description) {
    final squarePattern = RegExp(r'\[([^\[\]]+)\]');
    final dollarPattern = RegExp(r'\$([^\$]+)\$');

    // First, validate that all keys exist in the map
    final squareMatches = squarePattern.allMatches(description);
    final dollarMatches = dollarPattern.allMatches(description);

    for (final match in [...squareMatches, ...dollarMatches]) {
      final key = match.group(1);
      if (!offerPayLoad.containsKey(key)) {
        return null; // Missing key, will return when missing any key
      }
    }

    // Replace square brackets
    final result = description.replaceAllMapped(squarePattern, (match) {
      final key = match.group(1);
      return offerPayLoad[key] ?? '';
    });

    // Replace dollar signs with formatted currency
    return result.replaceAllMapped(dollarPattern, (match) {
      final key = match.group(1)!;
      final value = offerPayLoad[key] ?? '';
      return _getAmountCurrency(value) ?? '';
    });
  }

  String? _getAmountCurrency(String value) {
    final amountInt = int.tryParse(value);
    if (amountInt == null) return null;
    return Decimal.fromInt(amountInt).formatCurrency();
  }
}
