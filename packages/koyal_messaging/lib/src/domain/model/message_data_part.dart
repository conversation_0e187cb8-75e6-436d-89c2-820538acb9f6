// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

import '../../core/constants.dart';
import 'message_type.dart';

part 'message_data_part.freezed.dart';
part 'message_data_part.g.dart';

@freezed
class MessageDataPart with _$MessageDataPart {
  factory MessageDataPart({
    // Definition of data message
    @Json<PERSON>ey(unknownEnumValue: MessageType.unknown) MessageType? notificationType,
    @<PERSON><PERSON><PERSON><PERSON>(name: MessageDataKeys.deeplinkDataKey) String? deeplink,
    @<PERSON><PERSON><PERSON><PERSON>(name: MessageDataKeys.imageDataKey) String? image,
    @<PERSON><PERSON><PERSON><PERSON>(name: MessageDataKeys.msidDataKey) String? msId,
    @<PERSON><PERSON><PERSON><PERSON>(name: MessageDataKeys.screenLabelDataKey) String? screenLabel,
    @JsonKey(name: MessageDataKeys.urlToOpenDataKey) String? urlToOpen,
    @<PERSON><PERSON><PERSON><PERSON>(name: MessageDataKeys.sasTemplateId, fromJson: _stringToInt, toJson: _stringFromInt) int? sasTemplateId,
    @Json<PERSON><PERSON>(name: MessageDataKeys.cuid, fromJson: _stringToInt, toJson: _stringFromInt) int? cuid,
    @JsonKey(name: MessageDataKeys.contractNumber, fromJson: _stringToInt, toJson: _stringFromInt) int? contractNumber,
    // Generic labels
    @JsonKey(name: MessageDataKeys.titleDataKey) String? title,
    @JsonKey(name: MessageDataKeys.bodyDataKey) String? body,
    // Item ids
    @JsonKey(name: MessageDataKeys.inboxIdDataKey) String? inboxItemId,
    @JsonKey(name: MessageDataKeys.productIdDataKey) String? productId,
    @JsonKey(name: MessageDataKeys.rewardIdDataKey) String? rewardId,
    @JsonKey(name: MessageDataKeys.orderIdDataKey) String? orderId,
    @JsonKey(name: MessageDataKeys.logoutUserId) String? logoutUserId,
    // Tracking keys
    @JsonKey(name: MessageDataKeys.questTrackingNameDataKey) String? questTrackingName,
    @JsonKey(name: MessageDataKeys.trackingNameDataKey) String? trackingName,
    @JsonKey(name: MessageDataKeys.traceIdDataKey) String? traceId,
    // Others
    @JsonKey(name: MessageDataKeys.imageUrlDataKey) String? imageUrl,
    @JsonKey(name: MessageDataKeys.rewardEligibleDataKey, fromJson: _stringToBool, toJson: _stringFromBool)
    bool? rewardEligible,
    @JsonKey(name: MessageDataKeys.newLevelDataKey, fromJson: _stringToInt, toJson: _stringFromInt) int? newLevel,
    @JsonKey(name: MessageDataKeys.xpAwardedDataKey) String? xpAwarded,
    @JsonKey(name: MessageDataKeys.orderStateDataKey) String? orderState,
    @JsonKey(name: MessageDataKeys.waitForTypeDataKey) String? waitForType,
    @JsonKey(name: MessageDataKeys.flowStepDataKey) String? flowStep,
    @JsonKey(name: MessageDataKeys.transactionIdKey) String? transactionId,
    // Live activities
    @JsonKey(name: MessageDataKeys.flowTypeDataKey) String? flowType,
    @JsonKey(name: MessageDataKeys.statusDataKey) String? status,
    @JsonKey(name: MessageDataKeys.liveActivityEventDataKey) String? liveActivityEvent,
    @JsonKey(name: MessageDataKeys.timeToWaitingDataKey) String? timeToWaiting,
    @JsonKey(name: MessageDataKeys.ctaDataKey) String? cta,
    // Welcome screen offer
    @JsonKey(name: MessageDataKeys.gmaInAppType) String? gmaInAppType,
    @JsonKey(name: MessageDataKeys.gmaInAppPayLoad) String? gmaInAppPayLoad,
    @JsonKey(name: MessageDataKeys.gmaProductName) String? gmaProductName,
    @JsonKey(name: MessageDataKeys.gmaProductType) String? gmaProductType,
    // In app messages
    @JsonKey(name: MessageDataKeys.gmaInApp, fromJson: _stringToBool, toJson: _stringFromBool) bool? gmaInApp,
    @JsonKey(name: MessageDataKeys.gmaTemplateId, fromJson: _stringToInt, toJson: _stringFromInt) int? gmaTemplateId,
    @JsonKey(name: MessageDataKeys.gmaExpiresTimestamp) String? expiredTimestamp,
    Map<String, dynamic>? payload,
  }) = _MessageDataPart;

  MessageDataPart._();

  factory MessageDataPart.fromJson(Map<String, dynamic> json) => _$MessageDataPartFromJson(json);

  factory MessageDataPart.parse(Map<String, dynamic> json) {
    final parsed = MessageDataPart.fromJson(json);
    final updated = parsed.copyWith(payload: json);
    return updated;
  }
}

int? _stringToInt(String? number) => number == null ? null : int.parse(number);

String? _stringFromInt(int? number) => number?.toString();

bool? _stringToBool(String? value) => value == null ? null : value == 'true';

String? _stringFromBool(bool? value) => value?.toString();
