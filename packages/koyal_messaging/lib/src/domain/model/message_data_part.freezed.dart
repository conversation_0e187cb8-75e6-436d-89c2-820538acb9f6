// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'message_data_part.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessageDataPart _$MessageDataPartFromJson(Map<String, dynamic> json) {
  return _MessageDataPart.fromJson(json);
}

/// @nodoc
mixin _$MessageDataPart {
// Definition of data message
  @JsonKey(unknownEnumValue: MessageType.unknown)
  MessageType? get notificationType => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.deeplinkDataKey)
  String? get deeplink => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.imageDataKey)
  String? get image => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.msidDataKey)
  String? get msId => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.screenLabelDataKey)
  String? get screenLabel => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.urlToOpenDataKey)
  String? get urlToOpen => throw _privateConstructorUsedError;
  @JsonKey(
      name: MessageDataKeys.sasTemplateId,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get sasTemplateId => throw _privateConstructorUsedError;
  @JsonKey(
      name: MessageDataKeys.cuid,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get cuid => throw _privateConstructorUsedError;
  @JsonKey(
      name: MessageDataKeys.contractNumber,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get contractNumber =>
      throw _privateConstructorUsedError; // Generic labels
  @JsonKey(name: MessageDataKeys.titleDataKey)
  String? get title => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.bodyDataKey)
  String? get body => throw _privateConstructorUsedError; // Item ids
  @JsonKey(name: MessageDataKeys.inboxIdDataKey)
  String? get inboxItemId => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.productIdDataKey)
  String? get productId => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.rewardIdDataKey)
  String? get rewardId => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.orderIdDataKey)
  String? get orderId => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.logoutUserId)
  String? get logoutUserId =>
      throw _privateConstructorUsedError; // Tracking keys
  @JsonKey(name: MessageDataKeys.questTrackingNameDataKey)
  String? get questTrackingName => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.trackingNameDataKey)
  String? get trackingName => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.traceIdDataKey)
  String? get traceId => throw _privateConstructorUsedError; // Others
  @JsonKey(name: MessageDataKeys.imageUrlDataKey)
  String? get imageUrl => throw _privateConstructorUsedError;
  @JsonKey(
      name: MessageDataKeys.rewardEligibleDataKey,
      fromJson: _stringToBool,
      toJson: _stringFromBool)
  bool? get rewardEligible => throw _privateConstructorUsedError;
  @JsonKey(
      name: MessageDataKeys.newLevelDataKey,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get newLevel => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.xpAwardedDataKey)
  String? get xpAwarded => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.orderStateDataKey)
  String? get orderState => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.waitForTypeDataKey)
  String? get waitForType => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.flowStepDataKey)
  String? get flowStep => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.transactionIdKey)
  String? get transactionId =>
      throw _privateConstructorUsedError; // Live activities
  @JsonKey(name: MessageDataKeys.flowTypeDataKey)
  String? get flowType => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.statusDataKey)
  String? get status => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.liveActivityEventDataKey)
  String? get liveActivityEvent => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.timeToWaitingDataKey)
  String? get timeToWaiting => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.ctaDataKey)
  String? get cta => throw _privateConstructorUsedError; // Welcome screen offer
  @JsonKey(name: MessageDataKeys.gmaInAppType)
  String? get gmaInAppType => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.gmaInAppPayLoad)
  String? get gmaInAppPayLoad => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.gmaProductName)
  String? get gmaProductName => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.gmaProductType)
  String? get gmaProductType =>
      throw _privateConstructorUsedError; // In app messages
  @JsonKey(
      name: MessageDataKeys.gmaInApp,
      fromJson: _stringToBool,
      toJson: _stringFromBool)
  bool? get gmaInApp => throw _privateConstructorUsedError;
  @JsonKey(
      name: MessageDataKeys.gmaTemplateId,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get gmaTemplateId => throw _privateConstructorUsedError;
  @JsonKey(name: MessageDataKeys.gmaExpiresTimestamp)
  String? get expiredTimestamp => throw _privateConstructorUsedError;
  Map<String, dynamic>? get payload => throw _privateConstructorUsedError;

  /// Serializes this MessageDataPart to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MessageDataPart
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MessageDataPartCopyWith<MessageDataPart> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageDataPartCopyWith<$Res> {
  factory $MessageDataPartCopyWith(
          MessageDataPart value, $Res Function(MessageDataPart) then) =
      _$MessageDataPartCopyWithImpl<$Res, MessageDataPart>;
  @useResult
  $Res call(
      {@JsonKey(unknownEnumValue: MessageType.unknown)
      MessageType? notificationType,
      @JsonKey(name: MessageDataKeys.deeplinkDataKey) String? deeplink,
      @JsonKey(name: MessageDataKeys.imageDataKey) String? image,
      @JsonKey(name: MessageDataKeys.msidDataKey) String? msId,
      @JsonKey(name: MessageDataKeys.screenLabelDataKey) String? screenLabel,
      @JsonKey(name: MessageDataKeys.urlToOpenDataKey) String? urlToOpen,
      @JsonKey(
          name: MessageDataKeys.sasTemplateId,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? sasTemplateId,
      @JsonKey(
          name: MessageDataKeys.cuid,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? cuid,
      @JsonKey(
          name: MessageDataKeys.contractNumber,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? contractNumber,
      @JsonKey(name: MessageDataKeys.titleDataKey) String? title,
      @JsonKey(name: MessageDataKeys.bodyDataKey) String? body,
      @JsonKey(name: MessageDataKeys.inboxIdDataKey) String? inboxItemId,
      @JsonKey(name: MessageDataKeys.productIdDataKey) String? productId,
      @JsonKey(name: MessageDataKeys.rewardIdDataKey) String? rewardId,
      @JsonKey(name: MessageDataKeys.orderIdDataKey) String? orderId,
      @JsonKey(name: MessageDataKeys.logoutUserId) String? logoutUserId,
      @JsonKey(name: MessageDataKeys.questTrackingNameDataKey)
      String? questTrackingName,
      @JsonKey(name: MessageDataKeys.trackingNameDataKey) String? trackingName,
      @JsonKey(name: MessageDataKeys.traceIdDataKey) String? traceId,
      @JsonKey(name: MessageDataKeys.imageUrlDataKey) String? imageUrl,
      @JsonKey(
          name: MessageDataKeys.rewardEligibleDataKey,
          fromJson: _stringToBool,
          toJson: _stringFromBool)
      bool? rewardEligible,
      @JsonKey(
          name: MessageDataKeys.newLevelDataKey,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? newLevel,
      @JsonKey(name: MessageDataKeys.xpAwardedDataKey) String? xpAwarded,
      @JsonKey(name: MessageDataKeys.orderStateDataKey) String? orderState,
      @JsonKey(name: MessageDataKeys.waitForTypeDataKey) String? waitForType,
      @JsonKey(name: MessageDataKeys.flowStepDataKey) String? flowStep,
      @JsonKey(name: MessageDataKeys.transactionIdKey) String? transactionId,
      @JsonKey(name: MessageDataKeys.flowTypeDataKey) String? flowType,
      @JsonKey(name: MessageDataKeys.statusDataKey) String? status,
      @JsonKey(name: MessageDataKeys.liveActivityEventDataKey)
      String? liveActivityEvent,
      @JsonKey(name: MessageDataKeys.timeToWaitingDataKey)
      String? timeToWaiting,
      @JsonKey(name: MessageDataKeys.ctaDataKey) String? cta,
      @JsonKey(name: MessageDataKeys.gmaInAppType) String? gmaInAppType,
      @JsonKey(name: MessageDataKeys.gmaInAppPayLoad) String? gmaInAppPayLoad,
      @JsonKey(name: MessageDataKeys.gmaProductName) String? gmaProductName,
      @JsonKey(name: MessageDataKeys.gmaProductType) String? gmaProductType,
      @JsonKey(
          name: MessageDataKeys.gmaInApp,
          fromJson: _stringToBool,
          toJson: _stringFromBool)
      bool? gmaInApp,
      @JsonKey(
          name: MessageDataKeys.gmaTemplateId,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? gmaTemplateId,
      @JsonKey(name: MessageDataKeys.gmaExpiresTimestamp)
      String? expiredTimestamp,
      Map<String, dynamic>? payload});
}

/// @nodoc
class _$MessageDataPartCopyWithImpl<$Res, $Val extends MessageDataPart>
    implements $MessageDataPartCopyWith<$Res> {
  _$MessageDataPartCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MessageDataPart
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationType = freezed,
    Object? deeplink = freezed,
    Object? image = freezed,
    Object? msId = freezed,
    Object? screenLabel = freezed,
    Object? urlToOpen = freezed,
    Object? sasTemplateId = freezed,
    Object? cuid = freezed,
    Object? contractNumber = freezed,
    Object? title = freezed,
    Object? body = freezed,
    Object? inboxItemId = freezed,
    Object? productId = freezed,
    Object? rewardId = freezed,
    Object? orderId = freezed,
    Object? logoutUserId = freezed,
    Object? questTrackingName = freezed,
    Object? trackingName = freezed,
    Object? traceId = freezed,
    Object? imageUrl = freezed,
    Object? rewardEligible = freezed,
    Object? newLevel = freezed,
    Object? xpAwarded = freezed,
    Object? orderState = freezed,
    Object? waitForType = freezed,
    Object? flowStep = freezed,
    Object? transactionId = freezed,
    Object? flowType = freezed,
    Object? status = freezed,
    Object? liveActivityEvent = freezed,
    Object? timeToWaiting = freezed,
    Object? cta = freezed,
    Object? gmaInAppType = freezed,
    Object? gmaInAppPayLoad = freezed,
    Object? gmaProductName = freezed,
    Object? gmaProductType = freezed,
    Object? gmaInApp = freezed,
    Object? gmaTemplateId = freezed,
    Object? expiredTimestamp = freezed,
    Object? payload = freezed,
  }) {
    return _then(_value.copyWith(
      notificationType: freezed == notificationType
          ? _value.notificationType
          : notificationType // ignore: cast_nullable_to_non_nullable
              as MessageType?,
      deeplink: freezed == deeplink
          ? _value.deeplink
          : deeplink // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      msId: freezed == msId
          ? _value.msId
          : msId // ignore: cast_nullable_to_non_nullable
              as String?,
      screenLabel: freezed == screenLabel
          ? _value.screenLabel
          : screenLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      urlToOpen: freezed == urlToOpen
          ? _value.urlToOpen
          : urlToOpen // ignore: cast_nullable_to_non_nullable
              as String?,
      sasTemplateId: freezed == sasTemplateId
          ? _value.sasTemplateId
          : sasTemplateId // ignore: cast_nullable_to_non_nullable
              as int?,
      cuid: freezed == cuid
          ? _value.cuid
          : cuid // ignore: cast_nullable_to_non_nullable
              as int?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      body: freezed == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String?,
      inboxItemId: freezed == inboxItemId
          ? _value.inboxItemId
          : inboxItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardId: freezed == rewardId
          ? _value.rewardId
          : rewardId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      logoutUserId: freezed == logoutUserId
          ? _value.logoutUserId
          : logoutUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      questTrackingName: freezed == questTrackingName
          ? _value.questTrackingName
          : questTrackingName // ignore: cast_nullable_to_non_nullable
              as String?,
      trackingName: freezed == trackingName
          ? _value.trackingName
          : trackingName // ignore: cast_nullable_to_non_nullable
              as String?,
      traceId: freezed == traceId
          ? _value.traceId
          : traceId // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardEligible: freezed == rewardEligible
          ? _value.rewardEligible
          : rewardEligible // ignore: cast_nullable_to_non_nullable
              as bool?,
      newLevel: freezed == newLevel
          ? _value.newLevel
          : newLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      xpAwarded: freezed == xpAwarded
          ? _value.xpAwarded
          : xpAwarded // ignore: cast_nullable_to_non_nullable
              as String?,
      orderState: freezed == orderState
          ? _value.orderState
          : orderState // ignore: cast_nullable_to_non_nullable
              as String?,
      waitForType: freezed == waitForType
          ? _value.waitForType
          : waitForType // ignore: cast_nullable_to_non_nullable
              as String?,
      flowStep: freezed == flowStep
          ? _value.flowStep
          : flowStep // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      flowType: freezed == flowType
          ? _value.flowType
          : flowType // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      liveActivityEvent: freezed == liveActivityEvent
          ? _value.liveActivityEvent
          : liveActivityEvent // ignore: cast_nullable_to_non_nullable
              as String?,
      timeToWaiting: freezed == timeToWaiting
          ? _value.timeToWaiting
          : timeToWaiting // ignore: cast_nullable_to_non_nullable
              as String?,
      cta: freezed == cta
          ? _value.cta
          : cta // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaInAppType: freezed == gmaInAppType
          ? _value.gmaInAppType
          : gmaInAppType // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaInAppPayLoad: freezed == gmaInAppPayLoad
          ? _value.gmaInAppPayLoad
          : gmaInAppPayLoad // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaProductName: freezed == gmaProductName
          ? _value.gmaProductName
          : gmaProductName // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaProductType: freezed == gmaProductType
          ? _value.gmaProductType
          : gmaProductType // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaInApp: freezed == gmaInApp
          ? _value.gmaInApp
          : gmaInApp // ignore: cast_nullable_to_non_nullable
              as bool?,
      gmaTemplateId: freezed == gmaTemplateId
          ? _value.gmaTemplateId
          : gmaTemplateId // ignore: cast_nullable_to_non_nullable
              as int?,
      expiredTimestamp: freezed == expiredTimestamp
          ? _value.expiredTimestamp
          : expiredTimestamp // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MessageDataPartImplCopyWith<$Res>
    implements $MessageDataPartCopyWith<$Res> {
  factory _$$MessageDataPartImplCopyWith(_$MessageDataPartImpl value,
          $Res Function(_$MessageDataPartImpl) then) =
      __$$MessageDataPartImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(unknownEnumValue: MessageType.unknown)
      MessageType? notificationType,
      @JsonKey(name: MessageDataKeys.deeplinkDataKey) String? deeplink,
      @JsonKey(name: MessageDataKeys.imageDataKey) String? image,
      @JsonKey(name: MessageDataKeys.msidDataKey) String? msId,
      @JsonKey(name: MessageDataKeys.screenLabelDataKey) String? screenLabel,
      @JsonKey(name: MessageDataKeys.urlToOpenDataKey) String? urlToOpen,
      @JsonKey(
          name: MessageDataKeys.sasTemplateId,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? sasTemplateId,
      @JsonKey(
          name: MessageDataKeys.cuid,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? cuid,
      @JsonKey(
          name: MessageDataKeys.contractNumber,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? contractNumber,
      @JsonKey(name: MessageDataKeys.titleDataKey) String? title,
      @JsonKey(name: MessageDataKeys.bodyDataKey) String? body,
      @JsonKey(name: MessageDataKeys.inboxIdDataKey) String? inboxItemId,
      @JsonKey(name: MessageDataKeys.productIdDataKey) String? productId,
      @JsonKey(name: MessageDataKeys.rewardIdDataKey) String? rewardId,
      @JsonKey(name: MessageDataKeys.orderIdDataKey) String? orderId,
      @JsonKey(name: MessageDataKeys.logoutUserId) String? logoutUserId,
      @JsonKey(name: MessageDataKeys.questTrackingNameDataKey)
      String? questTrackingName,
      @JsonKey(name: MessageDataKeys.trackingNameDataKey) String? trackingName,
      @JsonKey(name: MessageDataKeys.traceIdDataKey) String? traceId,
      @JsonKey(name: MessageDataKeys.imageUrlDataKey) String? imageUrl,
      @JsonKey(
          name: MessageDataKeys.rewardEligibleDataKey,
          fromJson: _stringToBool,
          toJson: _stringFromBool)
      bool? rewardEligible,
      @JsonKey(
          name: MessageDataKeys.newLevelDataKey,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? newLevel,
      @JsonKey(name: MessageDataKeys.xpAwardedDataKey) String? xpAwarded,
      @JsonKey(name: MessageDataKeys.orderStateDataKey) String? orderState,
      @JsonKey(name: MessageDataKeys.waitForTypeDataKey) String? waitForType,
      @JsonKey(name: MessageDataKeys.flowStepDataKey) String? flowStep,
      @JsonKey(name: MessageDataKeys.transactionIdKey) String? transactionId,
      @JsonKey(name: MessageDataKeys.flowTypeDataKey) String? flowType,
      @JsonKey(name: MessageDataKeys.statusDataKey) String? status,
      @JsonKey(name: MessageDataKeys.liveActivityEventDataKey)
      String? liveActivityEvent,
      @JsonKey(name: MessageDataKeys.timeToWaitingDataKey)
      String? timeToWaiting,
      @JsonKey(name: MessageDataKeys.ctaDataKey) String? cta,
      @JsonKey(name: MessageDataKeys.gmaInAppType) String? gmaInAppType,
      @JsonKey(name: MessageDataKeys.gmaInAppPayLoad) String? gmaInAppPayLoad,
      @JsonKey(name: MessageDataKeys.gmaProductName) String? gmaProductName,
      @JsonKey(name: MessageDataKeys.gmaProductType) String? gmaProductType,
      @JsonKey(
          name: MessageDataKeys.gmaInApp,
          fromJson: _stringToBool,
          toJson: _stringFromBool)
      bool? gmaInApp,
      @JsonKey(
          name: MessageDataKeys.gmaTemplateId,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      int? gmaTemplateId,
      @JsonKey(name: MessageDataKeys.gmaExpiresTimestamp)
      String? expiredTimestamp,
      Map<String, dynamic>? payload});
}

/// @nodoc
class __$$MessageDataPartImplCopyWithImpl<$Res>
    extends _$MessageDataPartCopyWithImpl<$Res, _$MessageDataPartImpl>
    implements _$$MessageDataPartImplCopyWith<$Res> {
  __$$MessageDataPartImplCopyWithImpl(
      _$MessageDataPartImpl _value, $Res Function(_$MessageDataPartImpl) _then)
      : super(_value, _then);

  /// Create a copy of MessageDataPart
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationType = freezed,
    Object? deeplink = freezed,
    Object? image = freezed,
    Object? msId = freezed,
    Object? screenLabel = freezed,
    Object? urlToOpen = freezed,
    Object? sasTemplateId = freezed,
    Object? cuid = freezed,
    Object? contractNumber = freezed,
    Object? title = freezed,
    Object? body = freezed,
    Object? inboxItemId = freezed,
    Object? productId = freezed,
    Object? rewardId = freezed,
    Object? orderId = freezed,
    Object? logoutUserId = freezed,
    Object? questTrackingName = freezed,
    Object? trackingName = freezed,
    Object? traceId = freezed,
    Object? imageUrl = freezed,
    Object? rewardEligible = freezed,
    Object? newLevel = freezed,
    Object? xpAwarded = freezed,
    Object? orderState = freezed,
    Object? waitForType = freezed,
    Object? flowStep = freezed,
    Object? transactionId = freezed,
    Object? flowType = freezed,
    Object? status = freezed,
    Object? liveActivityEvent = freezed,
    Object? timeToWaiting = freezed,
    Object? cta = freezed,
    Object? gmaInAppType = freezed,
    Object? gmaInAppPayLoad = freezed,
    Object? gmaProductName = freezed,
    Object? gmaProductType = freezed,
    Object? gmaInApp = freezed,
    Object? gmaTemplateId = freezed,
    Object? expiredTimestamp = freezed,
    Object? payload = freezed,
  }) {
    return _then(_$MessageDataPartImpl(
      notificationType: freezed == notificationType
          ? _value.notificationType
          : notificationType // ignore: cast_nullable_to_non_nullable
              as MessageType?,
      deeplink: freezed == deeplink
          ? _value.deeplink
          : deeplink // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      msId: freezed == msId
          ? _value.msId
          : msId // ignore: cast_nullable_to_non_nullable
              as String?,
      screenLabel: freezed == screenLabel
          ? _value.screenLabel
          : screenLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      urlToOpen: freezed == urlToOpen
          ? _value.urlToOpen
          : urlToOpen // ignore: cast_nullable_to_non_nullable
              as String?,
      sasTemplateId: freezed == sasTemplateId
          ? _value.sasTemplateId
          : sasTemplateId // ignore: cast_nullable_to_non_nullable
              as int?,
      cuid: freezed == cuid
          ? _value.cuid
          : cuid // ignore: cast_nullable_to_non_nullable
              as int?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      body: freezed == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String?,
      inboxItemId: freezed == inboxItemId
          ? _value.inboxItemId
          : inboxItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardId: freezed == rewardId
          ? _value.rewardId
          : rewardId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      logoutUserId: freezed == logoutUserId
          ? _value.logoutUserId
          : logoutUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      questTrackingName: freezed == questTrackingName
          ? _value.questTrackingName
          : questTrackingName // ignore: cast_nullable_to_non_nullable
              as String?,
      trackingName: freezed == trackingName
          ? _value.trackingName
          : trackingName // ignore: cast_nullable_to_non_nullable
              as String?,
      traceId: freezed == traceId
          ? _value.traceId
          : traceId // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardEligible: freezed == rewardEligible
          ? _value.rewardEligible
          : rewardEligible // ignore: cast_nullable_to_non_nullable
              as bool?,
      newLevel: freezed == newLevel
          ? _value.newLevel
          : newLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      xpAwarded: freezed == xpAwarded
          ? _value.xpAwarded
          : xpAwarded // ignore: cast_nullable_to_non_nullable
              as String?,
      orderState: freezed == orderState
          ? _value.orderState
          : orderState // ignore: cast_nullable_to_non_nullable
              as String?,
      waitForType: freezed == waitForType
          ? _value.waitForType
          : waitForType // ignore: cast_nullable_to_non_nullable
              as String?,
      flowStep: freezed == flowStep
          ? _value.flowStep
          : flowStep // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      flowType: freezed == flowType
          ? _value.flowType
          : flowType // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      liveActivityEvent: freezed == liveActivityEvent
          ? _value.liveActivityEvent
          : liveActivityEvent // ignore: cast_nullable_to_non_nullable
              as String?,
      timeToWaiting: freezed == timeToWaiting
          ? _value.timeToWaiting
          : timeToWaiting // ignore: cast_nullable_to_non_nullable
              as String?,
      cta: freezed == cta
          ? _value.cta
          : cta // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaInAppType: freezed == gmaInAppType
          ? _value.gmaInAppType
          : gmaInAppType // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaInAppPayLoad: freezed == gmaInAppPayLoad
          ? _value.gmaInAppPayLoad
          : gmaInAppPayLoad // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaProductName: freezed == gmaProductName
          ? _value.gmaProductName
          : gmaProductName // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaProductType: freezed == gmaProductType
          ? _value.gmaProductType
          : gmaProductType // ignore: cast_nullable_to_non_nullable
              as String?,
      gmaInApp: freezed == gmaInApp
          ? _value.gmaInApp
          : gmaInApp // ignore: cast_nullable_to_non_nullable
              as bool?,
      gmaTemplateId: freezed == gmaTemplateId
          ? _value.gmaTemplateId
          : gmaTemplateId // ignore: cast_nullable_to_non_nullable
              as int?,
      expiredTimestamp: freezed == expiredTimestamp
          ? _value.expiredTimestamp
          : expiredTimestamp // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _value._payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageDataPartImpl extends _MessageDataPart {
  _$MessageDataPartImpl(
      {@JsonKey(unknownEnumValue: MessageType.unknown) this.notificationType,
      @JsonKey(name: MessageDataKeys.deeplinkDataKey) this.deeplink,
      @JsonKey(name: MessageDataKeys.imageDataKey) this.image,
      @JsonKey(name: MessageDataKeys.msidDataKey) this.msId,
      @JsonKey(name: MessageDataKeys.screenLabelDataKey) this.screenLabel,
      @JsonKey(name: MessageDataKeys.urlToOpenDataKey) this.urlToOpen,
      @JsonKey(
          name: MessageDataKeys.sasTemplateId,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      this.sasTemplateId,
      @JsonKey(
          name: MessageDataKeys.cuid,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      this.cuid,
      @JsonKey(
          name: MessageDataKeys.contractNumber,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      this.contractNumber,
      @JsonKey(name: MessageDataKeys.titleDataKey) this.title,
      @JsonKey(name: MessageDataKeys.bodyDataKey) this.body,
      @JsonKey(name: MessageDataKeys.inboxIdDataKey) this.inboxItemId,
      @JsonKey(name: MessageDataKeys.productIdDataKey) this.productId,
      @JsonKey(name: MessageDataKeys.rewardIdDataKey) this.rewardId,
      @JsonKey(name: MessageDataKeys.orderIdDataKey) this.orderId,
      @JsonKey(name: MessageDataKeys.logoutUserId) this.logoutUserId,
      @JsonKey(name: MessageDataKeys.questTrackingNameDataKey)
      this.questTrackingName,
      @JsonKey(name: MessageDataKeys.trackingNameDataKey) this.trackingName,
      @JsonKey(name: MessageDataKeys.traceIdDataKey) this.traceId,
      @JsonKey(name: MessageDataKeys.imageUrlDataKey) this.imageUrl,
      @JsonKey(
          name: MessageDataKeys.rewardEligibleDataKey,
          fromJson: _stringToBool,
          toJson: _stringFromBool)
      this.rewardEligible,
      @JsonKey(
          name: MessageDataKeys.newLevelDataKey,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      this.newLevel,
      @JsonKey(name: MessageDataKeys.xpAwardedDataKey) this.xpAwarded,
      @JsonKey(name: MessageDataKeys.orderStateDataKey) this.orderState,
      @JsonKey(name: MessageDataKeys.waitForTypeDataKey) this.waitForType,
      @JsonKey(name: MessageDataKeys.flowStepDataKey) this.flowStep,
      @JsonKey(name: MessageDataKeys.transactionIdKey) this.transactionId,
      @JsonKey(name: MessageDataKeys.flowTypeDataKey) this.flowType,
      @JsonKey(name: MessageDataKeys.statusDataKey) this.status,
      @JsonKey(
          name: MessageDataKeys.liveActivityEventDataKey)
      this.liveActivityEvent,
      @JsonKey(name: MessageDataKeys.timeToWaitingDataKey) this.timeToWaiting,
      @JsonKey(name: MessageDataKeys.ctaDataKey) this.cta,
      @JsonKey(name: MessageDataKeys.gmaInAppType) this.gmaInAppType,
      @JsonKey(name: MessageDataKeys.gmaInAppPayLoad) this.gmaInAppPayLoad,
      @JsonKey(name: MessageDataKeys.gmaProductName) this.gmaProductName,
      @JsonKey(name: MessageDataKeys.gmaProductType) this.gmaProductType,
      @JsonKey(
          name: MessageDataKeys.gmaInApp,
          fromJson: _stringToBool,
          toJson: _stringFromBool)
      this.gmaInApp,
      @JsonKey(
          name: MessageDataKeys.gmaTemplateId,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      this.gmaTemplateId,
      @JsonKey(name: MessageDataKeys.gmaExpiresTimestamp) this.expiredTimestamp,
      final Map<String, dynamic>? payload})
      : _payload = payload,
        super._();

  factory _$MessageDataPartImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageDataPartImplFromJson(json);

// Definition of data message
  @override
  @JsonKey(unknownEnumValue: MessageType.unknown)
  final MessageType? notificationType;
  @override
  @JsonKey(name: MessageDataKeys.deeplinkDataKey)
  final String? deeplink;
  @override
  @JsonKey(name: MessageDataKeys.imageDataKey)
  final String? image;
  @override
  @JsonKey(name: MessageDataKeys.msidDataKey)
  final String? msId;
  @override
  @JsonKey(name: MessageDataKeys.screenLabelDataKey)
  final String? screenLabel;
  @override
  @JsonKey(name: MessageDataKeys.urlToOpenDataKey)
  final String? urlToOpen;
  @override
  @JsonKey(
      name: MessageDataKeys.sasTemplateId,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  final int? sasTemplateId;
  @override
  @JsonKey(
      name: MessageDataKeys.cuid,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  final int? cuid;
  @override
  @JsonKey(
      name: MessageDataKeys.contractNumber,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  final int? contractNumber;
// Generic labels
  @override
  @JsonKey(name: MessageDataKeys.titleDataKey)
  final String? title;
  @override
  @JsonKey(name: MessageDataKeys.bodyDataKey)
  final String? body;
// Item ids
  @override
  @JsonKey(name: MessageDataKeys.inboxIdDataKey)
  final String? inboxItemId;
  @override
  @JsonKey(name: MessageDataKeys.productIdDataKey)
  final String? productId;
  @override
  @JsonKey(name: MessageDataKeys.rewardIdDataKey)
  final String? rewardId;
  @override
  @JsonKey(name: MessageDataKeys.orderIdDataKey)
  final String? orderId;
  @override
  @JsonKey(name: MessageDataKeys.logoutUserId)
  final String? logoutUserId;
// Tracking keys
  @override
  @JsonKey(name: MessageDataKeys.questTrackingNameDataKey)
  final String? questTrackingName;
  @override
  @JsonKey(name: MessageDataKeys.trackingNameDataKey)
  final String? trackingName;
  @override
  @JsonKey(name: MessageDataKeys.traceIdDataKey)
  final String? traceId;
// Others
  @override
  @JsonKey(name: MessageDataKeys.imageUrlDataKey)
  final String? imageUrl;
  @override
  @JsonKey(
      name: MessageDataKeys.rewardEligibleDataKey,
      fromJson: _stringToBool,
      toJson: _stringFromBool)
  final bool? rewardEligible;
  @override
  @JsonKey(
      name: MessageDataKeys.newLevelDataKey,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  final int? newLevel;
  @override
  @JsonKey(name: MessageDataKeys.xpAwardedDataKey)
  final String? xpAwarded;
  @override
  @JsonKey(name: MessageDataKeys.orderStateDataKey)
  final String? orderState;
  @override
  @JsonKey(name: MessageDataKeys.waitForTypeDataKey)
  final String? waitForType;
  @override
  @JsonKey(name: MessageDataKeys.flowStepDataKey)
  final String? flowStep;
  @override
  @JsonKey(name: MessageDataKeys.transactionIdKey)
  final String? transactionId;
// Live activities
  @override
  @JsonKey(name: MessageDataKeys.flowTypeDataKey)
  final String? flowType;
  @override
  @JsonKey(name: MessageDataKeys.statusDataKey)
  final String? status;
  @override
  @JsonKey(name: MessageDataKeys.liveActivityEventDataKey)
  final String? liveActivityEvent;
  @override
  @JsonKey(name: MessageDataKeys.timeToWaitingDataKey)
  final String? timeToWaiting;
  @override
  @JsonKey(name: MessageDataKeys.ctaDataKey)
  final String? cta;
// Welcome screen offer
  @override
  @JsonKey(name: MessageDataKeys.gmaInAppType)
  final String? gmaInAppType;
  @override
  @JsonKey(name: MessageDataKeys.gmaInAppPayLoad)
  final String? gmaInAppPayLoad;
  @override
  @JsonKey(name: MessageDataKeys.gmaProductName)
  final String? gmaProductName;
  @override
  @JsonKey(name: MessageDataKeys.gmaProductType)
  final String? gmaProductType;
// In app messages
  @override
  @JsonKey(
      name: MessageDataKeys.gmaInApp,
      fromJson: _stringToBool,
      toJson: _stringFromBool)
  final bool? gmaInApp;
  @override
  @JsonKey(
      name: MessageDataKeys.gmaTemplateId,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  final int? gmaTemplateId;
  @override
  @JsonKey(name: MessageDataKeys.gmaExpiresTimestamp)
  final String? expiredTimestamp;
  final Map<String, dynamic>? _payload;
  @override
  Map<String, dynamic>? get payload {
    final value = _payload;
    if (value == null) return null;
    if (_payload is EqualUnmodifiableMapView) return _payload;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'MessageDataPart(notificationType: $notificationType, deeplink: $deeplink, image: $image, msId: $msId, screenLabel: $screenLabel, urlToOpen: $urlToOpen, sasTemplateId: $sasTemplateId, cuid: $cuid, contractNumber: $contractNumber, title: $title, body: $body, inboxItemId: $inboxItemId, productId: $productId, rewardId: $rewardId, orderId: $orderId, logoutUserId: $logoutUserId, questTrackingName: $questTrackingName, trackingName: $trackingName, traceId: $traceId, imageUrl: $imageUrl, rewardEligible: $rewardEligible, newLevel: $newLevel, xpAwarded: $xpAwarded, orderState: $orderState, waitForType: $waitForType, flowStep: $flowStep, transactionId: $transactionId, flowType: $flowType, status: $status, liveActivityEvent: $liveActivityEvent, timeToWaiting: $timeToWaiting, cta: $cta, gmaInAppType: $gmaInAppType, gmaInAppPayLoad: $gmaInAppPayLoad, gmaProductName: $gmaProductName, gmaProductType: $gmaProductType, gmaInApp: $gmaInApp, gmaTemplateId: $gmaTemplateId, expiredTimestamp: $expiredTimestamp, payload: $payload)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageDataPartImpl &&
            (identical(other.notificationType, notificationType) ||
                other.notificationType == notificationType) &&
            (identical(other.deeplink, deeplink) ||
                other.deeplink == deeplink) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.msId, msId) || other.msId == msId) &&
            (identical(other.screenLabel, screenLabel) ||
                other.screenLabel == screenLabel) &&
            (identical(other.urlToOpen, urlToOpen) ||
                other.urlToOpen == urlToOpen) &&
            (identical(other.sasTemplateId, sasTemplateId) ||
                other.sasTemplateId == sasTemplateId) &&
            (identical(other.cuid, cuid) || other.cuid == cuid) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.inboxItemId, inboxItemId) ||
                other.inboxItemId == inboxItemId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.rewardId, rewardId) ||
                other.rewardId == rewardId) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.logoutUserId, logoutUserId) ||
                other.logoutUserId == logoutUserId) &&
            (identical(other.questTrackingName, questTrackingName) ||
                other.questTrackingName == questTrackingName) &&
            (identical(other.trackingName, trackingName) ||
                other.trackingName == trackingName) &&
            (identical(other.traceId, traceId) || other.traceId == traceId) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.rewardEligible, rewardEligible) ||
                other.rewardEligible == rewardEligible) &&
            (identical(other.newLevel, newLevel) ||
                other.newLevel == newLevel) &&
            (identical(other.xpAwarded, xpAwarded) ||
                other.xpAwarded == xpAwarded) &&
            (identical(other.orderState, orderState) ||
                other.orderState == orderState) &&
            (identical(other.waitForType, waitForType) ||
                other.waitForType == waitForType) &&
            (identical(other.flowStep, flowStep) ||
                other.flowStep == flowStep) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.flowType, flowType) ||
                other.flowType == flowType) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.liveActivityEvent, liveActivityEvent) ||
                other.liveActivityEvent == liveActivityEvent) &&
            (identical(other.timeToWaiting, timeToWaiting) ||
                other.timeToWaiting == timeToWaiting) &&
            (identical(other.cta, cta) || other.cta == cta) &&
            (identical(other.gmaInAppType, gmaInAppType) ||
                other.gmaInAppType == gmaInAppType) &&
            (identical(other.gmaInAppPayLoad, gmaInAppPayLoad) ||
                other.gmaInAppPayLoad == gmaInAppPayLoad) &&
            (identical(other.gmaProductName, gmaProductName) ||
                other.gmaProductName == gmaProductName) &&
            (identical(other.gmaProductType, gmaProductType) ||
                other.gmaProductType == gmaProductType) &&
            (identical(other.gmaInApp, gmaInApp) ||
                other.gmaInApp == gmaInApp) &&
            (identical(other.gmaTemplateId, gmaTemplateId) ||
                other.gmaTemplateId == gmaTemplateId) &&
            (identical(other.expiredTimestamp, expiredTimestamp) ||
                other.expiredTimestamp == expiredTimestamp) &&
            const DeepCollectionEquality().equals(other._payload, _payload));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        notificationType,
        deeplink,
        image,
        msId,
        screenLabel,
        urlToOpen,
        sasTemplateId,
        cuid,
        contractNumber,
        title,
        body,
        inboxItemId,
        productId,
        rewardId,
        orderId,
        logoutUserId,
        questTrackingName,
        trackingName,
        traceId,
        imageUrl,
        rewardEligible,
        newLevel,
        xpAwarded,
        orderState,
        waitForType,
        flowStep,
        transactionId,
        flowType,
        status,
        liveActivityEvent,
        timeToWaiting,
        cta,
        gmaInAppType,
        gmaInAppPayLoad,
        gmaProductName,
        gmaProductType,
        gmaInApp,
        gmaTemplateId,
        expiredTimestamp,
        const DeepCollectionEquality().hash(_payload)
      ]);

  /// Create a copy of MessageDataPart
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageDataPartImplCopyWith<_$MessageDataPartImpl> get copyWith =>
      __$$MessageDataPartImplCopyWithImpl<_$MessageDataPartImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageDataPartImplToJson(
      this,
    );
  }
}

abstract class _MessageDataPart extends MessageDataPart {
  factory _MessageDataPart(
      {@JsonKey(unknownEnumValue: MessageType.unknown)
      final MessageType? notificationType,
      @JsonKey(name: MessageDataKeys.deeplinkDataKey) final String? deeplink,
      @JsonKey(name: MessageDataKeys.imageDataKey) final String? image,
      @JsonKey(name: MessageDataKeys.msidDataKey) final String? msId,
      @JsonKey(name: MessageDataKeys.screenLabelDataKey)
      final String? screenLabel,
      @JsonKey(name: MessageDataKeys.urlToOpenDataKey) final String? urlToOpen,
      @JsonKey(
          name: MessageDataKeys.sasTemplateId,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      final int? sasTemplateId,
      @JsonKey(name: MessageDataKeys.cuid, fromJson: _stringToInt, toJson: _stringFromInt)
      final int? cuid,
      @JsonKey(
          name: MessageDataKeys.contractNumber,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      final int? contractNumber,
      @JsonKey(name: MessageDataKeys.titleDataKey) final String? title,
      @JsonKey(name: MessageDataKeys.bodyDataKey) final String? body,
      @JsonKey(name: MessageDataKeys.inboxIdDataKey) final String? inboxItemId,
      @JsonKey(name: MessageDataKeys.productIdDataKey) final String? productId,
      @JsonKey(name: MessageDataKeys.rewardIdDataKey) final String? rewardId,
      @JsonKey(name: MessageDataKeys.orderIdDataKey) final String? orderId,
      @JsonKey(name: MessageDataKeys.logoutUserId) final String? logoutUserId,
      @JsonKey(name: MessageDataKeys.questTrackingNameDataKey)
      final String? questTrackingName,
      @JsonKey(name: MessageDataKeys.trackingNameDataKey)
      final String? trackingName,
      @JsonKey(name: MessageDataKeys.traceIdDataKey) final String? traceId,
      @JsonKey(name: MessageDataKeys.imageUrlDataKey) final String? imageUrl,
      @JsonKey(
          name: MessageDataKeys.rewardEligibleDataKey,
          fromJson: _stringToBool,
          toJson: _stringFromBool)
      final bool? rewardEligible,
      @JsonKey(
          name: MessageDataKeys.newLevelDataKey,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      final int? newLevel,
      @JsonKey(name: MessageDataKeys.xpAwardedDataKey) final String? xpAwarded,
      @JsonKey(name: MessageDataKeys.orderStateDataKey)
      final String? orderState,
      @JsonKey(name: MessageDataKeys.waitForTypeDataKey)
      final String? waitForType,
      @JsonKey(name: MessageDataKeys.flowStepDataKey) final String? flowStep,
      @JsonKey(name: MessageDataKeys.transactionIdKey)
      final String? transactionId,
      @JsonKey(name: MessageDataKeys.flowTypeDataKey) final String? flowType,
      @JsonKey(name: MessageDataKeys.statusDataKey) final String? status,
      @JsonKey(name: MessageDataKeys.liveActivityEventDataKey)
      final String? liveActivityEvent,
      @JsonKey(name: MessageDataKeys.timeToWaitingDataKey)
      final String? timeToWaiting,
      @JsonKey(name: MessageDataKeys.ctaDataKey) final String? cta,
      @JsonKey(name: MessageDataKeys.gmaInAppType) final String? gmaInAppType,
      @JsonKey(name: MessageDataKeys.gmaInAppPayLoad)
      final String? gmaInAppPayLoad,
      @JsonKey(name: MessageDataKeys.gmaProductName)
      final String? gmaProductName,
      @JsonKey(name: MessageDataKeys.gmaProductType)
      final String? gmaProductType,
      @JsonKey(name: MessageDataKeys.gmaInApp, fromJson: _stringToBool, toJson: _stringFromBool)
      final bool? gmaInApp,
      @JsonKey(
          name: MessageDataKeys.gmaTemplateId,
          fromJson: _stringToInt,
          toJson: _stringFromInt)
      final int? gmaTemplateId,
      @JsonKey(name: MessageDataKeys.gmaExpiresTimestamp)
      final String? expiredTimestamp,
      final Map<String, dynamic>? payload}) = _$MessageDataPartImpl;
  _MessageDataPart._() : super._();

  factory _MessageDataPart.fromJson(Map<String, dynamic> json) =
      _$MessageDataPartImpl.fromJson;

// Definition of data message
  @override
  @JsonKey(unknownEnumValue: MessageType.unknown)
  MessageType? get notificationType;
  @override
  @JsonKey(name: MessageDataKeys.deeplinkDataKey)
  String? get deeplink;
  @override
  @JsonKey(name: MessageDataKeys.imageDataKey)
  String? get image;
  @override
  @JsonKey(name: MessageDataKeys.msidDataKey)
  String? get msId;
  @override
  @JsonKey(name: MessageDataKeys.screenLabelDataKey)
  String? get screenLabel;
  @override
  @JsonKey(name: MessageDataKeys.urlToOpenDataKey)
  String? get urlToOpen;
  @override
  @JsonKey(
      name: MessageDataKeys.sasTemplateId,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get sasTemplateId;
  @override
  @JsonKey(
      name: MessageDataKeys.cuid,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get cuid;
  @override
  @JsonKey(
      name: MessageDataKeys.contractNumber,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get contractNumber; // Generic labels
  @override
  @JsonKey(name: MessageDataKeys.titleDataKey)
  String? get title;
  @override
  @JsonKey(name: MessageDataKeys.bodyDataKey)
  String? get body; // Item ids
  @override
  @JsonKey(name: MessageDataKeys.inboxIdDataKey)
  String? get inboxItemId;
  @override
  @JsonKey(name: MessageDataKeys.productIdDataKey)
  String? get productId;
  @override
  @JsonKey(name: MessageDataKeys.rewardIdDataKey)
  String? get rewardId;
  @override
  @JsonKey(name: MessageDataKeys.orderIdDataKey)
  String? get orderId;
  @override
  @JsonKey(name: MessageDataKeys.logoutUserId)
  String? get logoutUserId; // Tracking keys
  @override
  @JsonKey(name: MessageDataKeys.questTrackingNameDataKey)
  String? get questTrackingName;
  @override
  @JsonKey(name: MessageDataKeys.trackingNameDataKey)
  String? get trackingName;
  @override
  @JsonKey(name: MessageDataKeys.traceIdDataKey)
  String? get traceId; // Others
  @override
  @JsonKey(name: MessageDataKeys.imageUrlDataKey)
  String? get imageUrl;
  @override
  @JsonKey(
      name: MessageDataKeys.rewardEligibleDataKey,
      fromJson: _stringToBool,
      toJson: _stringFromBool)
  bool? get rewardEligible;
  @override
  @JsonKey(
      name: MessageDataKeys.newLevelDataKey,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get newLevel;
  @override
  @JsonKey(name: MessageDataKeys.xpAwardedDataKey)
  String? get xpAwarded;
  @override
  @JsonKey(name: MessageDataKeys.orderStateDataKey)
  String? get orderState;
  @override
  @JsonKey(name: MessageDataKeys.waitForTypeDataKey)
  String? get waitForType;
  @override
  @JsonKey(name: MessageDataKeys.flowStepDataKey)
  String? get flowStep;
  @override
  @JsonKey(name: MessageDataKeys.transactionIdKey)
  String? get transactionId; // Live activities
  @override
  @JsonKey(name: MessageDataKeys.flowTypeDataKey)
  String? get flowType;
  @override
  @JsonKey(name: MessageDataKeys.statusDataKey)
  String? get status;
  @override
  @JsonKey(name: MessageDataKeys.liveActivityEventDataKey)
  String? get liveActivityEvent;
  @override
  @JsonKey(name: MessageDataKeys.timeToWaitingDataKey)
  String? get timeToWaiting;
  @override
  @JsonKey(name: MessageDataKeys.ctaDataKey)
  String? get cta; // Welcome screen offer
  @override
  @JsonKey(name: MessageDataKeys.gmaInAppType)
  String? get gmaInAppType;
  @override
  @JsonKey(name: MessageDataKeys.gmaInAppPayLoad)
  String? get gmaInAppPayLoad;
  @override
  @JsonKey(name: MessageDataKeys.gmaProductName)
  String? get gmaProductName;
  @override
  @JsonKey(name: MessageDataKeys.gmaProductType)
  String? get gmaProductType; // In app messages
  @override
  @JsonKey(
      name: MessageDataKeys.gmaInApp,
      fromJson: _stringToBool,
      toJson: _stringFromBool)
  bool? get gmaInApp;
  @override
  @JsonKey(
      name: MessageDataKeys.gmaTemplateId,
      fromJson: _stringToInt,
      toJson: _stringFromInt)
  int? get gmaTemplateId;
  @override
  @JsonKey(name: MessageDataKeys.gmaExpiresTimestamp)
  String? get expiredTimestamp;
  @override
  Map<String, dynamic>? get payload;

  /// Create a copy of MessageDataPart
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MessageDataPartImplCopyWith<_$MessageDataPartImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
