// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_data_part.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessageDataPartImpl _$$MessageDataPartImplFromJson(
        Map<String, dynamic> json) =>
    _$MessageDataPartImpl(
      notificationType: $enumDecodeNullable(
          _$MessageTypeEnumMap, json['notificationType'],
          unknownValue: MessageType.unknown),
      deeplink: json['deeplink'] as String?,
      image: json['FCM_IMAGE'] as String?,
      msId: json['MSID'] as String?,
      screenLabel: json['SCREEN_LABEL'] as String?,
      urlToOpen: json['URL_TO_OPEN'] as String?,
      sasTemplateId: _stringToInt(json['SAS_TEMPLATE_ID'] as String?),
      cuid: _stringToInt(json['CUID'] as String?),
      contractNumber: _stringToInt(json['CONTRACT_NUM'] as String?),
      title: json['title'] as String?,
      body: json['body'] as String?,
      inboxItemId: json['inboxItemId'] as String?,
      productId: json['productId'] as String?,
      rewardId: json['rewardId'] as String?,
      orderId: json['orderId'] as String?,
      logoutUserId: json['LogoutUserId'] as String?,
      questTrackingName: json['questTrackingName'] as String?,
      trackingName: json['trackingName'] as String?,
      traceId: json['traceId'] as String?,
      imageUrl: json['imageUrl'] as String?,
      rewardEligible: _stringToBool(json['rewardEligible'] as String?),
      newLevel: _stringToInt(json['newLevel'] as String?),
      xpAwarded: json['xpAwarded'] as String?,
      orderState: json['orderState'] as String?,
      waitForType: json['waitForType'] as String?,
      flowStep: json['flowStep'] as String?,
      transactionId: json['trxID'] as String?,
      flowType: json['type'] as String?,
      status: json['status'] as String?,
      liveActivityEvent: json['liveActivityEvent'] as String?,
      timeToWaiting: json['timeToWaiting'] as String?,
      cta: json['cta'] as String?,
      gmaInAppType: json['GMA_INAPP_TYPE'] as String?,
      gmaInAppPayLoad: json['GMA_INAPP_VAR_PAYLOAD'] as String?,
      gmaProductName: json['GMA_PRODUCT_NAME'] as String?,
      gmaProductType: json['GMA_PRODUCT_TYPE'] as String?,
      gmaInApp: _stringToBool(json['GMA_INAPP'] as String?),
      gmaTemplateId: _stringToInt(json['GMA_TEMPLATE_ID'] as String?),
      expiredTimestamp: json['expires'] as String?,
      payload: json['payload'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$MessageDataPartImplToJson(
        _$MessageDataPartImpl instance) =>
    <String, dynamic>{
      'notificationType': _$MessageTypeEnumMap[instance.notificationType],
      'deeplink': instance.deeplink,
      'FCM_IMAGE': instance.image,
      'MSID': instance.msId,
      'SCREEN_LABEL': instance.screenLabel,
      'URL_TO_OPEN': instance.urlToOpen,
      'SAS_TEMPLATE_ID': _stringFromInt(instance.sasTemplateId),
      'CUID': _stringFromInt(instance.cuid),
      'CONTRACT_NUM': _stringFromInt(instance.contractNumber),
      'title': instance.title,
      'body': instance.body,
      'inboxItemId': instance.inboxItemId,
      'productId': instance.productId,
      'rewardId': instance.rewardId,
      'orderId': instance.orderId,
      'LogoutUserId': instance.logoutUserId,
      'questTrackingName': instance.questTrackingName,
      'trackingName': instance.trackingName,
      'traceId': instance.traceId,
      'imageUrl': instance.imageUrl,
      'rewardEligible': _stringFromBool(instance.rewardEligible),
      'newLevel': _stringFromInt(instance.newLevel),
      'xpAwarded': instance.xpAwarded,
      'orderState': instance.orderState,
      'waitForType': instance.waitForType,
      'flowStep': instance.flowStep,
      'trxID': instance.transactionId,
      'type': instance.flowType,
      'status': instance.status,
      'liveActivityEvent': instance.liveActivityEvent,
      'timeToWaiting': instance.timeToWaiting,
      'cta': instance.cta,
      'GMA_INAPP_TYPE': instance.gmaInAppType,
      'GMA_INAPP_VAR_PAYLOAD': instance.gmaInAppPayLoad,
      'GMA_PRODUCT_NAME': instance.gmaProductName,
      'GMA_PRODUCT_TYPE': instance.gmaProductType,
      'GMA_INAPP': _stringFromBool(instance.gmaInApp),
      'GMA_TEMPLATE_ID': _stringFromInt(instance.gmaTemplateId),
      'expires': instance.expiredTimestamp,
      'payload': instance.payload,
    };

const _$MessageTypeEnumMap = {
  MessageType.unknown: 'unknown',
  MessageType.generic: 'generic',
  MessageType.authSignedInOnOtherDevice: 'auth.SignedInOnOtherDevice',
  MessageType.authLogoutPhoneUpdated: 'auth.LogoutPhoneUpdated',
  MessageType.authLogoutAccountDelete: 'auth.LogoutAccountDeleted',
  MessageType.identityIdentifyResult: 'identity.IdentifyResult',
  MessageType.messagingInboxMessage: 'messaging.InboxMessage',
  MessageType.orderStateChanged: 'ordering.OrderStateChanged',
  MessageType.deeplink: 'deeplink',
  MessageType.dynamicFormNotification: 'loan.DynamicFormNotification',
  MessageType.signatureDynamicFormNotification:
      'signature.DynamicFormNotification',
  MessageType.contractsOverviewChanged: 'financial.ContractsOverviewChanged',
  MessageType.contractsAccountBalanceChanged: 'financial.AccountBalanceChanged',
  MessageType.contractsCardStatusChanged: 'financial.CardStatusChanged',
  MessageType.contractsCardBlockChanged: 'financial.CardBlockChanged',
  MessageType.pendingActionChanged: 'pendingAction.Changed',
  MessageType.phoneNumberChanged: 'identity.PhoneNumberChanged',
  MessageType.ongoingMaintenance: 'ongoingMaintenance',
  MessageType.posApproved: 'mapp.pos.approved',
  MessageType.posRejected: 'mapp.pos.rejected',
  MessageType.posCanceled: 'mapp.pos.canceled',
  MessageType.posBlocked: 'mapp.pos.blocked',
  MessageType.raBlocked: 'mapp.ra.blocked',
  MessageType.raCanceled: 'mapp.ra.canceled',
  MessageType.ordersPendingConfirmation: 'mapp.orders.pendingConfirmation',
  MessageType.ordersConfirmed: 'mapp.orders.confirmed',
  MessageType.ordersCanceled: 'mapp.orders.cancelled',
  MessageType.ordersExpired: 'mapp.orders.expired',
  MessageType.ordersClosed: 'mapp.orders.closed',
  MessageType.ordersScanEval: 'mapp.orders.scanEval',
  MessageType.ordersSignPending: 'mapp.orders.signPending',
  MessageType.ordersSignSuccess: 'mapp.orders.signSuccess',
  MessageType.ordersSignFail: 'mapp.orders.signFail',
  MessageType.ordersWithdraw: 'mapp.orders.withdraw',
  MessageType.testNotification: 'testNotification',
  MessageType.transactionsQrTrxResult: 'transactions.qrTrxResult',
};
