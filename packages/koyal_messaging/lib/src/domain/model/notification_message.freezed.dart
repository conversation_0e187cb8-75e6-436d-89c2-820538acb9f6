// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

NotificationMessage _$NotificationMessageFromJson(Map<String, dynamic> json) {
  return _NotificationMessage.fromJson(json);
}

/// @nodoc
mixin _$NotificationMessage {
  String get id =>
      throw _privateConstructorUsedError; //Is true if the message opened the app from terminated or background state
  bool get messageOpenedApp => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  MessageNotificationPart? get notification =>
      throw _privateConstructorUsedError;
  MessageDataPart? get data => throw _privateConstructorUsedError;
  MessageOrigin? get origin => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationMessageCopyWith<NotificationMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationMessageCopyWith<$Res> {
  factory $NotificationMessageCopyWith(
          NotificationMessage value, $Res Function(NotificationMessage) then) =
      _$NotificationMessageCopyWithImpl<$Res, NotificationMessage>;
  @useResult
  $Res call(
      {String id,
      bool messageOpenedApp,
      DateTime? createdAt,
      MessageNotificationPart? notification,
      MessageDataPart? data,
      MessageOrigin? origin});

  $MessageNotificationPartCopyWith<$Res>? get notification;
  $MessageDataPartCopyWith<$Res>? get data;
}

/// @nodoc
class _$NotificationMessageCopyWithImpl<$Res, $Val extends NotificationMessage>
    implements $NotificationMessageCopyWith<$Res> {
  _$NotificationMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? messageOpenedApp = null,
    Object? createdAt = freezed,
    Object? notification = freezed,
    Object? data = freezed,
    Object? origin = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      messageOpenedApp: null == messageOpenedApp
          ? _value.messageOpenedApp
          : messageOpenedApp // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      notification: freezed == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as MessageNotificationPart?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as MessageDataPart?,
      origin: freezed == origin
          ? _value.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as MessageOrigin?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MessageNotificationPartCopyWith<$Res>? get notification {
    if (_value.notification == null) {
      return null;
    }

    return $MessageNotificationPartCopyWith<$Res>(_value.notification!,
        (value) {
      return _then(_value.copyWith(notification: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MessageDataPartCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $MessageDataPartCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_NotificationMessageCopyWith<$Res>
    implements $NotificationMessageCopyWith<$Res> {
  factory _$$_NotificationMessageCopyWith(_$_NotificationMessage value,
          $Res Function(_$_NotificationMessage) then) =
      __$$_NotificationMessageCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      bool messageOpenedApp,
      DateTime? createdAt,
      MessageNotificationPart? notification,
      MessageDataPart? data,
      MessageOrigin? origin});

  @override
  $MessageNotificationPartCopyWith<$Res>? get notification;
  @override
  $MessageDataPartCopyWith<$Res>? get data;
}

/// @nodoc
class __$$_NotificationMessageCopyWithImpl<$Res>
    extends _$NotificationMessageCopyWithImpl<$Res, _$_NotificationMessage>
    implements _$$_NotificationMessageCopyWith<$Res> {
  __$$_NotificationMessageCopyWithImpl(_$_NotificationMessage _value,
      $Res Function(_$_NotificationMessage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? messageOpenedApp = null,
    Object? createdAt = freezed,
    Object? notification = freezed,
    Object? data = freezed,
    Object? origin = freezed,
  }) {
    return _then(_$_NotificationMessage(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      messageOpenedApp: null == messageOpenedApp
          ? _value.messageOpenedApp
          : messageOpenedApp // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      notification: freezed == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as MessageNotificationPart?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as MessageDataPart?,
      origin: freezed == origin
          ? _value.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as MessageOrigin?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_NotificationMessage extends _NotificationMessage {
  _$_NotificationMessage(
      {required this.id,
      this.messageOpenedApp = false,
      this.createdAt,
      this.notification,
      this.data,
      this.origin = MessageOrigin.push})
      : super._();

  factory _$_NotificationMessage.fromJson(Map<String, dynamic> json) =>
      _$$_NotificationMessageFromJson(json);

  @override
  final String id;
//Is true if the message opened the app from terminated or background state
  @override
  @JsonKey()
  final bool messageOpenedApp;
  @override
  final DateTime? createdAt;
  @override
  final MessageNotificationPart? notification;
  @override
  final MessageDataPart? data;
  @override
  @JsonKey()
  final MessageOrigin? origin;

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_NotificationMessage &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.messageOpenedApp, messageOpenedApp) ||
                other.messageOpenedApp == messageOpenedApp) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.notification, notification) ||
                other.notification == notification) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.origin, origin) || other.origin == origin));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, messageOpenedApp, createdAt, notification, data, origin);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_NotificationMessageCopyWith<_$_NotificationMessage> get copyWith =>
      __$$_NotificationMessageCopyWithImpl<_$_NotificationMessage>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_NotificationMessageToJson(
      this,
    );
  }
}

abstract class _NotificationMessage extends NotificationMessage {
  factory _NotificationMessage(
      {required final String id,
      final bool messageOpenedApp,
      final DateTime? createdAt,
      final MessageNotificationPart? notification,
      final MessageDataPart? data,
      final MessageOrigin? origin}) = _$_NotificationMessage;
  _NotificationMessage._() : super._();

  factory _NotificationMessage.fromJson(Map<String, dynamic> json) =
      _$_NotificationMessage.fromJson;

  @override
  String get id;
  @override //Is true if the message opened the app from terminated or background state
  bool get messageOpenedApp;
  @override
  DateTime? get createdAt;
  @override
  MessageNotificationPart? get notification;
  @override
  MessageDataPart? get data;
  @override
  MessageOrigin? get origin;
  @override
  @JsonKey(ignore: true)
  _$$_NotificationMessageCopyWith<_$_NotificationMessage> get copyWith =>
      throw _privateConstructorUsedError;
}
