import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_live_activities_core/capp_live_activities_core.dart';
import 'package:collection/collection.dart';
import 'package:convert/convert.dart';
import 'package:dio/dio.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';
import 'package:selfcareapi/model/models.dart' as api_model;
import 'package:selfcareapi/selfcareapi.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../domain/model/fcm_push_token.dart';
import '../../domain/model/fcm_user_token.dart';
import '../../domain/model/notification_message.dart';
import '../../domain/repositories/i_koyal_messaging_repository.dart';
import '../../domain/services/i_local_notification_service.dart';
import '../../domain/services/i_notification_service.dart';
import '../../domain/services/i_reliable_notification_service.dart';
import '../storage/messaging_storage.dart';

class MessagingRepository implements IKoyalMessagingRepository {
  final IKoyalLocalNotificationService localNotificationService;
  final IKoyalRemoteNotificationService remoteNotificationService;
  final IReliableNotificationService reliableNotificationService;
  final IIdentityRepository identityRepository;
  final IUserRepository userRepository;
  final ICurrentUserRepository currentUserRepository;
  final KoyalTrackingService koyalTrackingService;
  final IFeatureFlagRepository featureFlagRepository;
  final IPlatformService platformService;
  final MessagingApi api;
  final MessagingStorage storage;
  final Logger logger;

  final StreamController<NotificationMessage> messageController = StreamController<NotificationMessage>.broadcast();
  final StreamController<NotificationMessage> messageTappedController =
      StreamController<NotificationMessage>.broadcast();
  final StreamController<bool> _tokenRegistrationController = StreamController<bool>.broadcast();
  final StreamController<String> _tokenChangedController = StreamController<String>.broadcast();

  final Subject<List<NotificationMessage>> notifHistorySubject = BehaviorSubject<List<NotificationMessage>>();
  List<NotificationMessage> notifications = [];
  NotificationMessage? lastedLiveActivitiesNotifications;
  StreamSubscription<CurrentUser?>? _userProfileSubscription;

  @override
  Stream<NotificationMessage> get onReceived => messageController.stream;
  @override
  Stream<NotificationMessage> get onTapped => messageTappedController.stream;

  @override
  Stream<String> get onTokenChanged => _tokenChangedController.stream;

  // Seems that this is not used and can be removed
  @override
  Stream<bool> get onTokenRegistered => _tokenRegistrationController.stream;

  @override
  bool get tokenEncodingEnabled => featureFlagRepository.getFlag(KoyalFeatureFlag.encodeFcmToken)?.enabled ?? false;

  @override
  Future<String?> get pushToken => remoteNotificationService.pushToken;

  MessagingRepository({
    required this.api,
    required this.platformService,
    required this.storage,
    required this.logger,
    required this.localNotificationService,
    required this.remoteNotificationService,
    required this.reliableNotificationService,
    required this.identityRepository,
    required this.userRepository,
    required this.currentUserRepository,
    required this.koyalTrackingService,
    required this.featureFlagRepository,
  });

  /// IMPORTANT: run before logout process starts (accesstoken)
  ///
  /// sometimes it takes a while and request got a 401 error
  /// but we don't want refresh token, for this request is
  /// 401 error valid response and we can continue.
  @override
  Future<bool> deleteToken({String? token}) async {
    try {
      final accessLevel = await userRepository.getAccessLevel();
      final canRemove = accessLevel.isRight() && token != null;

      if (canRemove) {
        final encToken = encodeTokenIfPossible(token);
        koyalTrackingService.trackAnalyticsEvent(
          eventCategory: KoyalTrackingCategories.pushToken,
          eventAction: KoyalTrackingActions.delete,
          event: KoyalEvent.pushTokenTracking,
          eventLabel: KoyalTrackingLabels.deletePushToken,
          userPropertyMap: {KoyalTrackingProperties.propertyIsPushTokenEncoded: '$tokenEncodingEnabled'},
        );
        if (tokenEncodingEnabled) {
          await api.commonNotificationsMessagingTokenDeletePost(
            deleteMessagingTokenRequest: api_model.DeleteMessagingTokenRequest(
              deviceMessagingToken: encToken,
              encoded: tokenEncodingEnabled,
            ),
            options: Options(validateStatus: (status) => [204, 401].contains(status)),
          );
        } else {
          await api.commonNotificationsMessagingTokenTokenDelete(
            token,
            options: Options(validateStatus: (status) => [204, 401].contains(status)),
          );
        }
      }

      await storage.deletePushToken();
      await storage.deleteUserToken();
      logger.d('KM: MessagingRepository(): deleteToken: $token encoded: $tokenEncodingEnabled');
      return true;
    } on DioError catch (e) {
      koyalTrackingService.trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.pushToken,
        eventAction: KoyalTrackingActions.deleteResponse,
        event: KoyalEvent.pushTokenTracking,
        eventLabel: '${e.response?.statusCode}',
        userPropertyMap: {KoyalTrackingProperties.propertyIsPushTokenEncoded: '$tokenEncodingEnabled'},
      );
      logger.d('KM: $runtimeType: deleteToken encoded: $tokenEncodingEnabled failed: ${e.message}');
    } on Exception catch (e) {
      koyalTrackingService.trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.pushToken,
        eventAction: KoyalTrackingActions.deleteFail,
        event: KoyalEvent.pushTokenTracking,
        eventLabel: '',
        userPropertyMap: {KoyalTrackingProperties.propertyIsPushTokenEncoded: '$tokenEncodingEnabled'},
      );
      logger
        ..i('KM: $runtimeType: deleteToken encoded: $tokenEncodingEnabled failed: $e')
        ..wtf(e);
    }
    return false;
  }

  api_model.DevicePlatform? _getDevicePlatform() {
    if (Platform.isAndroid) {
      return api_model.DevicePlatform.android;
    } else if (Platform.isIOS) {
      return api_model.DevicePlatform.ios;
    } else {
      return null;
    }
  }

  /// Send api request with firebase token
  @override
  Future<bool> registerToken({required String token, bool force = false}) async {
    try {
      final deviceId = await platformService.deviceId();
      final currentUserId = await currentUserRepository.userId();
      if (currentUserId == null) {
        _tokenRegistrationController.add(false);
        logger.d('KM: MessagingRepository(): registerToken: currentUserId is null');
        return false;
      }

      /// log event to track analytics
      koyalTrackingService.trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.pushToken,
        eventAction: KoyalTrackingActions.registration,
        event: KoyalEvent.pushTokenTracking,
        eventLabel: KoyalTrackingLabels.registerPushToken,
        userPropertyMap: {KoyalTrackingProperties.propertyIsPushTokenEncoded: '$tokenEncodingEnabled'},
      );

      final encToken = tokenEncodingEnabled ? encodeTokenIfPossible(token) : token;

      final v2IsEnabledFlag = await featureFlagRepository.hasFeatureFlag(FeatureFlag.v2Notifications);
      await v2IsEnabledFlag.fold((l) async {}, (isEnabled) async {
        if (isEnabled) {
          await api.commonNotificationsV2MessagingTokenPost(
            updateMessagingTokenRequestV2: api_model.UpdateMessagingTokenRequestV2(
              deviceMessagingToken: encToken,
              encoded: tokenEncodingEnabled,
              deviceId: deviceId,
              devicePlatform: _getDevicePlatform(),
            ),
          );
        } else {
          await api.commonNotificationsMessagingTokenPost(
            updateMessagingTokenRequest: api_model.UpdateMessagingTokenRequest(
              deviceMessagingToken: encToken,
              encoded: tokenEncodingEnabled,
              deviceId: deviceId,
            ),
          );
        }
      });

      /// save token to storage after successful request
      await storage.insertUserToken(FcmUserToken(token: token, userId: currentUserId));

      logger.d('KM: MessagingRepository(): registerToken: $token for $currentUserId encoded: $tokenEncodingEnabled');
      koyalTrackingService.trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.pushToken,
        eventAction: KoyalTrackingActions.registrationSuccess,
        event: KoyalEvent.pushTokenTracking,
        eventLabel: '',
        userPropertyMap: {KoyalTrackingProperties.propertyIsPushTokenEncoded: '$tokenEncodingEnabled'},
      );
      _tokenRegistrationController.add(true);
      return true;
    } on DioError catch (e) {
      _tokenRegistrationController.add(false);
      logger.d('KM: $runtimeType: registerToken failed: ${e.message}');

      koyalTrackingService.trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.pushToken,
        eventAction: KoyalTrackingActions.registrationResponse,
        event: KoyalEvent.pushTokenTracking,
        eventLabel: '${e.response?.statusCode}',
        userPropertyMap: {KoyalTrackingProperties.propertyIsPushTokenEncoded: '$tokenEncodingEnabled'},
      );
    } on Exception catch (e) {
      _tokenRegistrationController.add(false);
      logger
        ..i('KM: $runtimeType: registerToken failed: $e')
        ..wtf(e);
      koyalTrackingService.trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.pushToken,
        eventAction: KoyalTrackingActions.registrationFail,
        event: KoyalEvent.pushTokenTracking,
        eventLabel: '',
        userPropertyMap: {KoyalTrackingProperties.propertyIsPushTokenEncoded: '$tokenEncodingEnabled'},
      );
    }
    return false;
  }

  /// Clear notification center
  /// clean `all` notifications from notification center
  /// if `messagingId` is null
  @override
  Future<void> clear(int messageId) => localNotificationService.clear(messageId);

  @override
  void clearHistory() {
    notifications.clear();
    notifHistorySubject.add([]);
  }

  /// Initialize firebase app
  @override
  Future<void> initApp() async {
    await remoteNotificationService.initApp();
    await reliableNotificationService.initApp();
  }

  /// Initialized listeners for
  ///
  /// `Remote notifications` and `LocalNotifications`
  @override
  Future<bool> init() async {
    try {
      reliableNotificationService.recieved.listen(
        (event) {
          event.body;
          messageController.add(event);
        },
      );

      localNotificationService.onSelectStream.listen(messageTappedController.add);

      await Future.wait(
        [
          localNotificationService.setup(),
          remoteNotificationService.processInitialMessage(),
        ],
      );
      return true;
    } on Exception catch (e) {
      logger.wtf(e);
      koyalTrackingService.trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.pushToken,
        eventAction: KoyalTrackingActions.initialization,
        eventLabel: KoyalTrackingLabels.failed,
        event: KoyalEvent.pushTokenTracking,
      );
      return false;
    }
  }

  @override
  Future<bool> initWithWaiting() async {
    try {
      remoteNotificationService.received.listen(messageController.add);
      remoteNotificationService.onMessageOpenedApp.listen(messageTappedController.add);

      remoteNotificationService.tokenChanged.listen(_tokenChangedController.add);
      await remoteNotificationService.setup();
    } on Exception catch (e) {
      logger.wtf(e);
      koyalTrackingService.trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.pushToken,
        eventAction: KoyalTrackingActions.initialization,
        eventLabel: KoyalTrackingLabels.failed,
        event: KoyalEvent.pushTokenTracking,
      );
      return false;
    }
    return true;
  }

  @override
  void dispose() {
    messageController.close();
    messageTappedController.close();
    localNotificationService.close();
    remoteNotificationService.close();
    _userProfileSubscription?.cancel();
    notifHistorySubject.close();
  }

  @override
  Future<bool> sendDebugNotify({String? title, String? body, Map<String, String>? data}) async {
    try {
      await api.commonNotificationsTestNotifyPost(title: title, body: body, requestBody: data);
      return true;
    } on DioError catch (e) {
      logger.i(e);
    } on Exception catch (e) {
      logger.wtf(e);
    }

    return false;
  }

  @override
  Future<bool> testNotifications() async {
    final lastTestDate = await storage.getLastNotificationTestDate();
    final isOlderThanLimit =
        DateTime.now().toUtc().difference(lastTestDate ?? DateTime.now().toUtc()) > const Duration(days: 1);

    if (lastTestDate == null || isOlderThanLimit) {
      final isSuccess = await sendDebugNotify(title: '', body: '', data: {'notificationType': 'testNotification'});

      if (isSuccess) {
        await storage.insertLastNotificationTestDate(DateTime.now().toUtc());
      }

      return isSuccess;
    }

    return false;
  }

  @override
  Future<void> refreshPushToken() async {
    await deleteToken(token: await pushToken);
    await remoteNotificationService.refreshPushToken();
  }

  @override
  void show(NotificationMessage message) {
    if (message.shouldShowLocalNotification()) {
      if (!message.isDataMessage || message.isLiveActivity) {
        if (Platform.isIOS) {
          //on ios already handle by the notification service extension
          return;
        }
        localNotificationService.show(message);
      }
    }
  }

  /// We should post token when
  /// - user changed & access token is valid
  /// - fcm token was changed & access token is valid
  @override
  Future<bool> shouldPostToken({String? token}) async {
    final currentUserId = await currentUserRepository.userId();
    logger.d('KM: userid: $currentUserId, token: $token');

    if (token == null || token == '' || currentUserId == null) {
      logger.d('KM: MessagingRepository(): shouldPostToken: NO');
      return false;
    }

    final userExists = (await userRepository.getAccessLevel()).isRight();
    logger.d('KM: user exits: $userExists');
    final currentFcmUserToken = await storage.getUserToken();
    logger.d('KM: user fcm token: $currentFcmUserToken');
    final differentUser = currentFcmUserToken?.userId != currentUserId;
    final shouldPost = (differentUser && userExists) ||
        (userExists && token != currentFcmUserToken?.token) ||
        (currentFcmUserToken == null);
    logger.d('KM: MessagingRepository(): shouldPostToken: $shouldPost √[$currentUserId] √[$currentFcmUserToken]');
    return shouldPost;
  }

  /// Encoding token for backend in case of encoding is enabled
  /// otherwise will return token as is
  /// [token] - token to encode
  @override
  String encodeTokenIfPossible(String token) {
    return tokenEncodingEnabled ? hex.encode(utf8.encode(token)) : token;
  }

  @override
  void addToHistory(NotificationMessage message) {
    notifications.add(message);
    notifHistorySubject.add(notifications);
    if (message.isLiveActivity) lastedLiveActivitiesNotifications = message;
  }

  @override
  Stream<List<NotificationMessage>> get notificationHistoryStream => notifHistorySubject.stream;

  @override
  Future<void> cleanStorage() async {
    await storage.deletePushToken();
    await storage.deleteUserToken();
  }

  @override
  Future<void> registerUser({required String? userId}) async {
    reliableNotificationService.subscribeUser(userId: userId);
  }

  @override
  Future<String> getLastedLiveActivityStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.reload();
      final notifcations = await localNotificationService.getActiveNotifications();
      return notifcations.liveActivities.firstWhereOrNull((o) => o.status.value.isNotEmpty)?.status.value ??
          prefs.getString(LiveActivityModel.storeKeyLALasStatus) ??
          '';
    } catch (_) {}
    return '';
  }

  @override
  Future<List<String>>? getListLABackgroundStatus() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.reload();
    return prefs.getStringList(LiveActivityModel.storeKeyLA) ?? [];
  }

  @override
  Future<void> removeLastLABackgroundStatus() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.reload();
    await prefs.setStringList(LiveActivityModel.storeKeyLA, []);
  }

  @override
  Future<void> makeTokenExpired() async {
    final token = await storage.getPushToken();
    await storage.deletePushToken();
    await storage.insertPushToken(
      FcmPushToken(
        token: token?.token ?? '',
        originDateTime: DateTime.now().subtract(const Duration(days: 60)).toUtc(),
      ),
    );
  }
}
