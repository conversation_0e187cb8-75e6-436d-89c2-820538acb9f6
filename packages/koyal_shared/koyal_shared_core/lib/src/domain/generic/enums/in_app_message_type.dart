enum InAppMessageType {
  inApp,
  welcomeScreen,
  inAppPersonalized,
}

extension InAppMessageTypeExtension on String? {
  InAppMessageType? getInAppMessageType() {
    switch (this) {
      case 'InApp':
        return InAppMessageType.inApp;
      case 'WelcomeScreen':
        return InAppMessageType.welcomeScreen;
      case 'InAppPersonalized':
        return InAppMessageType.inAppPersonalized;
    }

    return null;
  }
}