part of 'manager.dart';

mixin _InstallerServer on _GmaInstaller, BaseLoggerMixin {
  Future<void> prepareDartServer() async {
    if (results?[Constants.argBootstrapServerBuild] == true || results?[Constants.argBootstrapServerRun] == true) {
      await _prepareDartServer();
    }
  }

  Future<void> _prepareDartServer() async {
    _log(formatSubtitle('Prepare local server.'), preLine: true);
    await _install();
    await _prepareServers();
    await _runServers();
  }

  Future<void> _install() async {
    _log(formatBody('Check vschttpd.'));
    try {
      final processRunner = ProcessRunner();
      await processRunner.runProcess(['vschttpd', '--help']);
      _log(formatBody('vschttpd is installed.'));
      _log(formatSuccess(), preLine: true);
    } catch (e, _) {
      _log(formatHeader('vschttpd will be installed.'));
      final installProcessRunner = ProcessRunner();
      final _result = await installProcessRunner.runProcess(['dart', 'pub', 'global', 'activate', 'vschttpd']);
      if (_result.exitCode == 0) {
        _log(formatCaption('vschttpd was installed.'));
        _log('');
        _log(formatSuccess(message: 'Successfly finished.', preLine: true));
      } else {
        _err(formatFailed('vschttpd was not installed.'));
      }
    }
  }

  Future<void> _prepareServers() async {
    ProcessPool pool = ProcessPool(numWorkers: 2);
    final workspace = GmaWorkspace.isInitialized() ? GmaWorkspace.fromDirectory() : null;
    if (workspace == null) return;
    final _appsWithServers = workspace.config.apps.where((element) => element.port != null);
    final _canInstallServers = _appsWithServers.isNotEmpty;

    if (_canInstallServers) {
      _log(formatSubtitle('Prepare app servers.'), preLine: true);

      if (results?[Constants.argBootstrapServerBuild] == true) {
        final builders = _appsWithServers.map((app) => WorkerJob(
              ['flutter', 'build', 'web'],
              workingDirectory: Directory(path.join(Directory.current.path, app.folder)),
              runInShell: true,
            ));
        await for (final WorkerJob job in pool.startWorkers(builders.toList())) {
          _log(formatHeader(job.name));
          if (job.result.exitCode > 0) {
            _log(formatFailed(job.result.stderr));
          } else {
            _log(formatHeader('build dart server.'));
            _log(formatCaption(job.name));
            _log('');
            _log(formatSuccess(message: 'Successfly finished.', preLine: true));
          }
        }
      }
    }
  }

  Future<void> _runServers() async {
    final workspace = GmaWorkspace.isInitialized() ? GmaWorkspace.fromDirectory() : null;
    if (workspace == null) return;
    final _appsWithServers = workspace.config.apps.where((element) => element.port != null);

    if (results?[Constants.argBootstrapServerRun] == true) {
      _log(formatSubtitle('Run app servers.'), preLine: true);
      for (final app in _appsWithServers) {
        final _command = [
          'vschttpd',
          '-r',
          (path.join(Directory.current.path, app.folder, 'build', 'web')),
          '-p',
          '${app.port}',
          '-a',
          'localhost',
          Platform.isWindows ? '' : '&'
        ];
        try {
          final processRunner = ProcessRunner();
          _log(formatCaption('${app.packageName}: run server. http://localhost:${app.port} ${_command.join(' ')}'));
          processRunner
              .runProcess(
            _command,
            failOk: false,
            runInShell: !Platform.isWindows,
          )
              .then((result) {
            _log(formatCaption('server runns ${result.exitCode}'));
            _log(formatBody(app.packageName));
            _log(formatSuccess(message: 'Successfly finished.', preLine: true));
          });
        } catch (e, _) {
          _log(formatFailed('exc: ${e.runtimeType}'));
          switch (e.runtimeType) {
            case ProcessRunnerException:
              _log(formatFailed('Server is running. don`t need to start again.'));
              break;
            default:
              _log(formatFailed(e.toString()));
              break;
          }
        }
      }
    }
  }
}
